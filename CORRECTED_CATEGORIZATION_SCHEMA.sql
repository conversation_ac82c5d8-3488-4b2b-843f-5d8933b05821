-- =====================================================
-- JiraniPay Smart Categorization Database Schema
-- Corrected for your existing database structure
-- =====================================================

-- Custom Categories Table
-- Stores user-defined custom categories with metadata
CREATE TABLE custom_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(50) DEFAULT 'folder',
  color VARCHAR(7) DEFAULT '#B2BEC3',
  keywords TEXT[],
  parent_category UUID REFERENCES custom_categories(id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE,
  
  -- Add foreign key constraint to user_profiles
  CONSTRAINT fk_custom_categories_user_id
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- User Categorization Preferences
-- Stores user-specific categorization patterns and preferences
CREATE TABLE user_categorization_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  amount_patterns JSONB DEFAULT '{}',
  temporal_patterns JSONB DEFAULT '{}',
  custom_categories JSONB DEFAULT '{}',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add foreign key constraint to user_profiles
  CONSTRAINT fk_user_categorization_preferences_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- Categorization Learning Data
-- Stores machine learning data for improving categorization accuracy
CREATE TABLE categorization_learning_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  learning_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add foreign key constraint to user_profiles
  CONSTRAINT fk_categorization_learning_data_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- Add missing categorization field to existing transactions table
-- Note: category column already exists, only adding category_confidence
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS category_confidence DECIMAL(3,2) DEFAULT 0;

-- Add check constraint to ensure confidence is between 0 and 1
-- First check if constraint already exists, then add if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'check_category_confidence'
        AND table_name = 'transactions'
    ) THEN
        ALTER TABLE transactions
        ADD CONSTRAINT check_category_confidence
        CHECK (category_confidence >= 0 AND category_confidence <= 1);
    END IF;
END $$;

-- =====================================================
-- Indexes for Performance Optimization
-- =====================================================

-- Index for custom categories lookup by user
CREATE INDEX IF NOT EXISTS idx_custom_categories_user_id 
ON custom_categories(user_id) WHERE is_active = true;

-- Index for custom categories by name (for search)
CREATE INDEX IF NOT EXISTS idx_custom_categories_name
ON custom_categories(name) WHERE is_active = true;

-- Unique constraint for category names per user (only for active categories)
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_category_name_per_user
ON custom_categories(user_id, name) WHERE is_active = true;

-- Index for transactions by category (for analytics)
CREATE INDEX IF NOT EXISTS idx_transactions_category 
ON transactions(category) WHERE category IS NOT NULL;

-- Index for transactions by category confidence (for review)
CREATE INDEX IF NOT EXISTS idx_transactions_category_confidence 
ON transactions(category_confidence) WHERE category_confidence IS NOT NULL;

-- Index for transactions categorization lookup
CREATE INDEX IF NOT EXISTS idx_transactions_user_category 
ON transactions(user_id, category, created_at);

-- =====================================================
-- Row Level Security (RLS) Policies
-- =====================================================

-- Enable RLS on custom categories
ALTER TABLE custom_categories ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own custom categories
DROP POLICY IF EXISTS custom_categories_user_policy ON custom_categories;
CREATE POLICY custom_categories_user_policy ON custom_categories
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Enable RLS on user categorization preferences
ALTER TABLE user_categorization_preferences ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own preferences
DROP POLICY IF EXISTS user_categorization_preferences_user_policy ON user_categorization_preferences;
CREATE POLICY user_categorization_preferences_user_policy ON user_categorization_preferences
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Enable RLS on categorization learning data
ALTER TABLE categorization_learning_data ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own learning data
DROP POLICY IF EXISTS categorization_learning_data_user_policy ON categorization_learning_data;
CREATE POLICY categorization_learning_data_user_policy ON categorization_learning_data
  FOR ALL USING (auth.uid()::text = user_id::text);

-- =====================================================
-- Functions for Maintenance
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
DROP TRIGGER IF EXISTS update_custom_categories_updated_at ON custom_categories;
CREATE TRIGGER update_custom_categories_updated_at
  BEFORE UPDATE ON custom_categories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_categorization_preferences_updated_at ON user_categorization_preferences;
CREATE TRIGGER update_user_categorization_preferences_updated_at
  BEFORE UPDATE ON user_categorization_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_categorization_learning_data_updated_at ON categorization_learning_data;
CREATE TRIGGER update_categorization_learning_data_updated_at
  BEFORE UPDATE ON categorization_learning_data
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Sample Data (Optional)
-- =====================================================

-- Insert some sample custom categories for testing
-- Note: Replace 'your-user-id-here' with an actual user_id from your user_profiles table

/*
INSERT INTO custom_categories (user_id, name, description, icon, color, keywords) VALUES
  ('your-user-id-here', 'Coffee & Snacks', 'Daily coffee and snack purchases', 'cafe', '#8B4513', ARRAY['coffee', 'starbucks', 'cafe', 'snack']),
  ('your-user-id-here', 'Gym & Fitness', 'Fitness and gym related expenses', 'fitness', '#FF6347', ARRAY['gym', 'fitness', 'workout', 'sports']),
  ('your-user-id-here', 'Online Shopping', 'E-commerce and online purchases', 'bag', '#4169E1', ARRAY['amazon', 'jumia', 'online', 'shopping']);
*/

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check if tables were created successfully
-- SELECT table_name FROM information_schema.tables 
-- WHERE table_schema = 'public' 
-- AND table_name IN ('custom_categories', 'user_categorization_preferences', 'categorization_learning_data');

-- Check if category_confidence column was added
-- SELECT column_name, data_type FROM information_schema.columns 
-- WHERE table_name = 'transactions' AND column_name = 'category_confidence';

-- Check indexes
-- SELECT indexname FROM pg_indexes WHERE tablename IN ('custom_categories', 'transactions');
