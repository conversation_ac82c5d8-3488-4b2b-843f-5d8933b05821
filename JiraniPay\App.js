import 'react-native-gesture-handler';
import React, { useEffect, useState, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
// StatusBar removed to fix render error - can be added back later
import { View, ActivityIndicator, StyleSheet, StatusBar } from 'react-native';
import { useUserActivity } from './hooks/useUserActivity';
import EnhancedSplashScreen from './components/EnhancedSplashScreen';
import NavigationUtils from './utils/navigationUtils';
import OnboardingScreen from './screens/OnboardingScreen';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import CompleteProfileScreen from './screens/CompleteProfileScreen';
import ForgotPasswordScreen from './screens/ForgotPasswordScreen';
import FAQScreen from './screens/FAQScreen';

import AIChatScreen from './screens/AIChatScreen';
import WalletScreen from './screens/WalletScreen';
import TopUpScreen from './screens/TopUpScreen';
import TransactionHistoryScreen from './screens/TransactionHistoryScreen';
import AnalyticsScreen from './screens/AnalyticsScreen';
import BudgetInsightsScreen from './screens/BudgetInsightsScreen';
// Enhanced Analytics
import EnhancedDashboardScreen from './screens/EnhancedDashboardScreen';
import AnalyticsExportScreen from './screens/AnalyticsExportScreen';
// Smart Categorization
import CategoryManagementScreen from './screens/CategoryManagementScreen';
import BulkCategorizationScreen from './screens/BulkCategorizationScreen';
import SavingsScreen from './screens/SavingsScreen';
import AutomaticSavingsScreen from './screens/AutomaticSavingsScreen';
// New Savings & Investment Screens
import SavingsDashboardScreen from './screens/SavingsDashboardScreen';
import SavingsAccountCreationScreen from './screens/SavingsAccountCreationScreen';
import SavingsAccountDetailsScreen from './screens/SavingsAccountDetailsScreen';
import InvestmentDashboardScreen from './screens/InvestmentDashboardScreen';
import FinancialPlanningDashboardScreen from './screens/FinancialPlanningDashboardScreen';
import SavingsTestScreen from './screens/SavingsTestScreen';
// Additional Savings Screens
import SavingsGoalsScreen from './screens/SavingsGoalsScreen';
import SavingsTransfersScreen from './screens/SavingsTransfersScreen';
import SavingsReportsScreen from './screens/SavingsReportsScreen';
import SavingsAccountsListScreen from './screens/SavingsAccountsListScreen';
import SavingsAnalyticsScreen from './screens/SavingsAnalyticsScreen';
import SavingsReportSchedulingScreen from './screens/SavingsReportSchedulingScreen';
import FinancialGoalCreationScreen from './screens/FinancialGoalCreationScreen';
import FinancialGoalDetailsScreen from './screens/FinancialGoalDetailsScreen';
// Investment Screens
import InvestmentPortfolioCreationScreen from './screens/InvestmentPortfolioCreationScreen';
import InvestmentPortfolioDetailsScreen from './screens/InvestmentPortfolioDetailsScreen';
import MarketOverviewScreen from './screens/MarketOverviewScreen';
import AssetSearchScreen from './screens/AssetSearchScreen';
import AssetDetailsScreen from './screens/AssetDetailsScreen';
import InvestmentAnalyticsScreen from './screens/InvestmentAnalyticsScreen';
import InvestmentTransactionsScreen from './screens/InvestmentTransactionsScreen';
import InvestmentReportsScreen from './screens/InvestmentReportsScreen';
import InvestmentPortfoliosListScreen from './screens/InvestmentPortfoliosListScreen';
import BillsOptimizationScreen from './screens/BillsOptimizationScreen';
import BillPaymentScreen from './screens/BillPaymentScreen';
import BillProvidersScreen from './screens/BillProvidersScreen';
import BillDetailsScreen from './screens/BillDetailsScreen';
import BillAmountScreen from './screens/BillAmountScreen';
import BillConfirmationScreen from './screens/BillConfirmationScreen';
import BillSuccessScreen from './screens/BillSuccessScreen';
import WalletSettingsScreen from './screens/WalletSettingsScreen';
import SendMoneyScreen from './screens/SendMoneyScreen';
import RequestMoneyScreen from './screens/RequestMoneyScreen';
import RequestAmountScreen from './screens/RequestAmountScreen';
import RequestHistoryScreen from './screens/RequestHistoryScreen';
import ManualRequestRecipientScreen from './screens/ManualRequestRecipientScreen';
import TransferAmountScreen from './screens/TransferAmountScreen';
import TransferConfirmationScreen from './screens/TransferConfirmationScreen';
import TransferSuccessScreen from './screens/TransferSuccessScreen';
import ManualRecipientScreen from './screens/ManualRecipientScreen';
import QRScannerScreen from './screens/QRScannerScreen';
import QRGeneratorScreen from './screens/QRGeneratorScreen';
import SecuritySettingsScreen from './screens/SecuritySettingsScreen';
import PrivacyControlsScreen from './screens/PrivacyControlsScreen';
import TwoFactorAuthScreen from './screens/TwoFactorAuthScreen';
import SessionTimeoutScreen from './screens/SessionTimeoutScreen';
import TrustedDevicesScreen from './screens/TrustedDevicesScreen';
import SecurityActivityScreen from './screens/SecurityActivityScreen';
import SecurityTipsScreen from './screens/SecurityTipsScreen';
import SecurityPolicyScreen from './screens/SecurityPolicyScreen';
import SecurityFAQScreen from './screens/SecurityFAQScreen';
import ContactSupportScreen from './screens/ContactSupportScreen';
import CreateTicketScreen from './screens/CreateTicketScreen';
import NotificationScreen from './screens/NotificationScreen';
import AccountVerificationScreen from './screens/AccountVerificationScreen';
import DocumentUploadScreen from './screens/DocumentUploadScreen';
import VerificationStatusScreen from './screens/VerificationStatusScreen';
import VerificationLimitsScreen from './screens/VerificationLimitsScreen';
import EmailVerificationScreen from './screens/EmailVerificationScreen';
import ProfilePictureScreen from './screens/ProfilePictureScreen';
import EditProfileScreen from './screens/EditProfileScreen';
import PrivacyPolicyScreen from './screens/PrivacyPolicyScreen';
import DataProtectionScreen from './screens/DataProtectionScreen';
// Predictive Analytics & Budgeting Screens
import BudgetManagementScreen from './screens/BudgetManagementScreen';
import CreateBudgetScreen from './screens/CreateBudgetScreen';
import MainNavigator from './navigation/MainNavigator';
import authService from './services/authService';
import enhancedNetworkService from './services/enhancedNetworkService';
import walletService from './services/walletService';
import automaticSavingsService from './services/automaticSavingsService';
import billsManagementService from './services/billsManagementService';
import currencyService from './services/currencyService';
import consentEnforcementService from './services/consentEnforcementService';
import notificationService from './services/notificationService';
import { LanguageProvider } from './contexts/LanguageContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { Colors } from './constants/Colors';
import { ErrorUtils } from 'react-native';

const Stack = createStackNavigator();

if (!__DEV__) {
  // Production error handler
  ErrorUtils.setGlobalHandler((error, isFatal) => {
    // Log the error
    console.error('Global error:', error);
    
    // For ReadableNativeMap errors specifically
    if (error.message && error.message.includes('ReadableNativeMap')) {
      console.warn('ReadableNativeMap error detected, attempting recovery');
      // You can add specific recovery logic here
    }
    
    // You can report to your error tracking service here
  });
}

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showSplash, setShowSplash] = useState(true);
  const [splashError, setSplashError] = useState(null);
  const navigationRef = useRef();

  // Initialize user activity tracking for session timeout
  const activityHandlers = useUserActivity();

  useEffect(() => {
    console.log('🚀 App useEffect triggered - starting initialization');
    initializeApp();

    // Emergency fallback - if app is still loading after 3 seconds, force completion
    const emergencyTimeout = setTimeout(() => {
      if (isLoading) {
        console.error('🚨 EMERGENCY: App initialization timeout - forcing completion');
        console.log('🔧 This indicates a service initialization issue that needs investigation');
        setIsLoading(false);
        setIsAuthenticated(false);
        setShowSplash(false);
      }
    }, 3000); // Reduced to 3 seconds for faster recovery

    return () => clearTimeout(emergencyTimeout);
  }, []);

  // Handle splash screen completion
  const handleSplashComplete = () => {
    try {
      console.log('🎨 Splash screen animation completed');
      setShowSplash(false);
    } catch (error) {
      console.error('❌ Error completing splash screen:', error);
      setSplashError(error.message);
      // Still hide splash screen even if there's an error
      setShowSplash(false);
    }
  };

  // Fallback timeout to ensure app doesn't get stuck on splash screen
  useEffect(() => {
    const splashTimeout = setTimeout(() => {
      if (showSplash) {
        console.warn('⚠️ Splash screen timeout - forcing completion');
        console.log('🔧 Current app state - isLoading:', isLoading, 'isAuthenticated:', isAuthenticated);
        setSplashError('Splash screen timeout');
        setShowSplash(false);

        // Force app to complete loading if it's still loading
        if (isLoading) {
          console.warn('⚠️ App still loading after splash timeout - forcing completion');
          setIsLoading(false);
          setIsAuthenticated(false);
        }
      }
    }, 3000); // Reduced to 3 seconds for faster recovery

    return () => clearTimeout(splashTimeout);
  }, [showSplash, isLoading, isAuthenticated]);

  useEffect(() => {
    // Initialize unified navigation system
    NavigationUtils.setNavigationRef(navigationRef);
    const backHandler = NavigationUtils.initializeBackHandler();

    return () => {
      backHandler.remove();
    };
  }, []);

  const startBackgroundProcessing = () => {
    // Check for automatic savings and bills every 5 minutes
    const interval = setInterval(async () => {
      try {
        if (automaticSavingsService.isInitialized) {
          await automaticSavingsService.executeAutomaticSavings();
          await automaticSavingsService.processSavingsReminders();
        }

        if (billsManagementService.isInitialized) {
          await billsManagementService.processBillReminders();
        }
      } catch (error) {
        console.error('❌ Background processing error:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Clean up interval on app unmount
    return () => clearInterval(interval);
  };

  const initializeApp = async () => {
    try {
      console.log('🚀 Initializing JiraniPay app...');

      // PRODUCTION MODE: Robust initialization with proper error handling
      console.log('🏭 Production mode initialization starting...');

      // FULL INITIALIZATION (when QUICK_START is false)
      // Add auth state listener FIRST (before any initialization)
      authService.addAuthStateListener((user) => {
        console.log('🔐 Auth state changed, user:', user ? 'authenticated' : 'not authenticated');
        setIsAuthenticated(!!user);
        setIsLoading(false);
      });

      // PRODUCTION MODE: Fast initialization - complete immediately
      console.log('🔐 Production mode: Fast initialization');

      // Set app to unauthenticated state immediately
      setIsAuthenticated(false);
      setIsLoading(false);

      // Initialize services in background (non-blocking)
      setTimeout(() => {
        initializeBackgroundServices();
      }, 100);

    } catch (error) {
      console.error('❌ Error initializing app:', error);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  // Initialize background services (non-blocking)
  const initializeBackgroundServices = async () => {
    try {
      console.log('🔧 Initializing background services...');

      // Clear any existing sessions (non-blocking)
      authService.clearStoredSession().catch(error => {
        console.warn('⚠️ Error clearing sessions:', error.message);
      });

      // Initialize device recognition service (non-blocking)
      import('./services/deviceUserRecognitionService')
        .then(module => module.default.initialize())
        .then(() => console.log('✅ Device User Recognition Service initialized'))
        .catch(error => console.warn('⚠️ Device User Recognition Service failed:', error.message));

      // Initialize enhanced network service (non-blocking)
      enhancedNetworkService.initialize()
        .then(() => console.log('✅ Enhanced network service initialized'))
        .catch(error => console.warn('⚠️ Enhanced network service initialization failed:', error.message));

      // Initialize auth service (non-blocking)
      authService.initialize()
        .then(() => console.log('✅ Auth service initialized'))
        .catch(error => console.warn('⚠️ Auth service initialization failed:', error.message));

      // Initialize other services in background
      initializeServicesInBackground();

    } catch (error) {
      console.warn('⚠️ Background services initialization error:', error);
    }
  };

  // Initialize all services in background after authentication
  const initializeServicesInBackground = async () => {
    try {
      console.log('🔧 Initializing services in background...');

      // Set timeout for background service initialization
      const backgroundTimeout = setTimeout(() => {
        console.warn('⚠️ Background services initialization timeout - continuing anyway');
        console.log('🔧 This is normal for slow networks or devices');
      }, 3000); // ✅ FIX: Reduced to 3 seconds for faster recovery

      try {
        // Initialize services in parallel for faster loading with individual timeouts
        const servicePromises = [
          // WalletService doesn't have initialize method - removed

          Promise.race([
            automaticSavingsService.initialize(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('AutomaticSavingsService timeout')), 1500))
          ]).catch(error => console.warn('⚠️ AutomaticSavingsService init failed:', error.message)),

          Promise.race([
            billsManagementService.initialize(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('BillsManagementService timeout')), 1500))
          ]).catch(error => console.warn('⚠️ BillsManagementService init failed:', error.message)),

          Promise.race([
            currencyService.initialize(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('CurrencyService timeout')), 1500))
          ]).catch(error => console.warn('⚠️ CurrencyService init failed:', error.message)),

          Promise.race([
            // ✅ FIX: Only initialize consent enforcement if user is authenticated
            (async () => {
              const currentUser = authService.getCurrentUser();
              if (currentUser?.id) {
                return consentEnforcementService.initialize(currentUser.id);
              } else {
                console.log('⚠️ Skipping consent enforcement - no authenticated user');
                return Promise.resolve({ success: true, skipped: true });
              }
            })(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('ConsentEnforcementService timeout')), 1500))
          ]).catch(error => console.warn('⚠️ ConsentEnforcementService init failed:', error.message)),

          Promise.race([
            notificationService.initialize(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('NotificationService timeout')), 1500))
          ]).catch(error => console.warn('⚠️ NotificationService init failed:', error.message))
        ];

        await Promise.allSettled(servicePromises);
        clearTimeout(backgroundTimeout);

        // Start background processing after services are ready
        startBackgroundProcessing();

        console.log('✅ Background services initialized');
      } catch (error) {
        console.error('❌ Error in background services initialization:', error);
        clearTimeout(backgroundTimeout);
      }
    } catch (error) {
      console.error('❌ Error initializing background services:', error);
    }
  };

  // Show splash screen first, regardless of loading state
  if (showSplash) {
    return (
      <ThemeProvider>
        <StatusBar
          barStyle="light-content"
          backgroundColor="transparent"
          translucent
        />
        <EnhancedSplashScreen
          onAnimationComplete={handleSplashComplete}
        />
      </ThemeProvider>
    );
  }

  // Show loading indicator after splash screen if still loading
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
      </View>
    );
  }

  return (
    <ThemeProvider>
      <LanguageProvider>
        <CurrencyProvider>
          <View style={{ flex: 1 }} {...activityHandlers}>
          <NavigationContainer ref={navigationRef}>
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            {isAuthenticated ? (
            <>
              <Stack.Screen name="MainApp" component={MainNavigator} />
              <Stack.Screen name="FAQ" component={FAQScreen} />
              <Stack.Screen name="AIChat" component={AIChatScreen} />
              <Stack.Screen name="Wallet" component={WalletScreen} />
              <Stack.Screen name="TopUp" component={TopUpScreen} />
              <Stack.Screen name="TransactionHistory" component={TransactionHistoryScreen} />
              <Stack.Screen name="Analytics" component={AnalyticsScreen} />
              <Stack.Screen name="BudgetInsights" component={BudgetInsightsScreen} />
              {/* Enhanced Analytics */}
              <Stack.Screen name="EnhancedDashboard" component={EnhancedDashboardScreen} />
              <Stack.Screen name="AnalyticsExport" component={AnalyticsExportScreen} />
              {/* Predictive Analytics & Budgeting */}
              <Stack.Screen name="BudgetManagement" component={BudgetManagementScreen} />
              <Stack.Screen name="CreateBudget" component={CreateBudgetScreen} />
              {/* Smart Categorization */}
              <Stack.Screen name="CategoryManagement" component={CategoryManagementScreen} />
              <Stack.Screen name="BulkCategorization" component={BulkCategorizationScreen} />
              <Stack.Screen name="Savings" component={SavingsScreen} />
              <Stack.Screen name="AutomaticSavings" component={AutomaticSavingsScreen} />
              {/* New Savings & Investment Screens */}
              <Stack.Screen name="SavingsDashboard" component={SavingsDashboardScreen} />
              <Stack.Screen name="SavingsAccountCreation" component={SavingsAccountCreationScreen} />
              <Stack.Screen name="SavingsAccountDetails" component={SavingsAccountDetailsScreen} />
              <Stack.Screen name="InvestmentDashboard" component={InvestmentDashboardScreen} />
              <Stack.Screen name="FinancialPlanningDashboard" component={FinancialPlanningDashboardScreen} />
              <Stack.Screen name="SavingsTest" component={SavingsTestScreen} />
              {/* Additional Savings Screens */}
              <Stack.Screen name="SavingsGoals" component={SavingsGoalsScreen} />
              <Stack.Screen name="SavingsTransfers" component={SavingsTransfersScreen} />
              <Stack.Screen name="SavingsReports" component={SavingsReportsScreen} />
              <Stack.Screen name="SavingsAccountsList" component={SavingsAccountsListScreen} />
              <Stack.Screen name="SavingsAnalytics" component={SavingsAnalyticsScreen} />
              <Stack.Screen name="SavingsReportScheduling" component={SavingsReportSchedulingScreen} />
              <Stack.Screen name="FinancialGoalCreation" component={FinancialGoalCreationScreen} />
              <Stack.Screen name="FinancialGoalDetails" component={FinancialGoalDetailsScreen} />
              {/* Investment Screens */}
              <Stack.Screen name="InvestmentPortfolioCreation" component={InvestmentPortfolioCreationScreen} />
              <Stack.Screen name="InvestmentPortfolioDetails" component={InvestmentPortfolioDetailsScreen} />
              <Stack.Screen name="MarketOverview" component={MarketOverviewScreen} />
              <Stack.Screen name="AssetSearch" component={AssetSearchScreen} />
              <Stack.Screen name="AssetDetails" component={AssetDetailsScreen} />
              <Stack.Screen name="InvestmentAnalytics" component={InvestmentAnalyticsScreen} />
              <Stack.Screen name="InvestmentTransactions" component={InvestmentTransactionsScreen} />
              <Stack.Screen name="InvestmentReports" component={InvestmentReportsScreen} />
              <Stack.Screen name="InvestmentPortfoliosList" component={InvestmentPortfoliosListScreen} />
              <Stack.Screen name="BillsOptimization" component={BillsOptimizationScreen} />
              <Stack.Screen name="BillPayment" component={BillPaymentScreen} />
              {/* Bill Payment Flow */}
              <Stack.Screen name="BillProviders" component={BillProvidersScreen} />
              <Stack.Screen name="BillDetails" component={BillDetailsScreen} />
              <Stack.Screen name="BillAmount" component={BillAmountScreen} />
              <Stack.Screen name="BillConfirmation" component={BillConfirmationScreen} />
              <Stack.Screen name="BillSuccess" component={BillSuccessScreen} />
              <Stack.Screen name="WalletSettings" component={WalletSettingsScreen} />
              {/* Send Money Flow */}
              <Stack.Screen name="SendMoney" component={SendMoneyScreen} />
              <Stack.Screen name="RequestMoney" component={RequestMoneyScreen} />
              <Stack.Screen name="RequestAmount" component={RequestAmountScreen} />
              <Stack.Screen name="RequestHistory" component={RequestHistoryScreen} />
              <Stack.Screen name="ManualRequestRecipient" component={ManualRequestRecipientScreen} />
              <Stack.Screen name="ManualRecipient" component={ManualRecipientScreen} />
              <Stack.Screen name="TransferAmount" component={TransferAmountScreen} />
              <Stack.Screen name="TransferConfirmation" component={TransferConfirmationScreen} />
              <Stack.Screen name="TransferSuccess" component={TransferSuccessScreen} />
              {/* QR Code Flow */}
              <Stack.Screen name="QRScanner" component={QRScannerScreen} />
              <Stack.Screen name="QRGenerator" component={QRGeneratorScreen} />
              {/* Security & Privacy */}
              <Stack.Screen name="SecuritySettings" component={SecuritySettingsScreen} />
              <Stack.Screen name="PrivacyControls" component={PrivacyControlsScreen} />
              <Stack.Screen name="TwoFactorAuth" component={TwoFactorAuthScreen} />
              <Stack.Screen name="SessionTimeout" component={SessionTimeoutScreen} />
              <Stack.Screen name="TrustedDevices" component={TrustedDevicesScreen} />
              <Stack.Screen name="SecurityActivity" component={SecurityActivityScreen} />
              <Stack.Screen name="SecurityTips" component={SecurityTipsScreen} />
              <Stack.Screen name="SecurityPolicy" component={SecurityPolicyScreen} />
              <Stack.Screen name="SecurityFAQ" component={SecurityFAQScreen} />
              <Stack.Screen name="ContactSupport" component={ContactSupportScreen} />
              <Stack.Screen name="CreateTicket" component={CreateTicketScreen} />
              <Stack.Screen name="Notifications" component={NotificationScreen} />
              {/* Profile Management */}
              <Stack.Screen name="EditProfile" component={EditProfileScreen} />
              <Stack.Screen name="ProfilePicture" component={ProfilePictureScreen} />
              {/* Account Verification */}
              <Stack.Screen name="AccountVerification" component={AccountVerificationScreen} />
              <Stack.Screen name="DocumentUpload" component={DocumentUploadScreen} />
              <Stack.Screen name="VerificationStatus" component={VerificationStatusScreen} />
              <Stack.Screen name="VerificationLimits" component={VerificationLimitsScreen} />
              <Stack.Screen name="EmailVerification" component={EmailVerificationScreen} />
              {/* Privacy & Data Protection */}
              <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicyScreen} />
              <Stack.Screen name="DataProtection" component={DataProtectionScreen} />
            </>
          ) : (
            <>
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="Onboarding" component={OnboardingScreen} />
              <Stack.Screen name="Register" component={RegisterScreen} />
              <Stack.Screen name="CompleteProfile" component={CompleteProfileScreen} />
              <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
            </>
          )}
        </Stack.Navigator>
          </NavigationContainer>
          </View>
        </CurrencyProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.appBackground,
  },
});



