# 🔧 DATABASE FIX - Money Request Function Error

## **🚨 CURRENT ERROR:**
```
❌ Error loading money request history: structure of query does not match function result type
❌ Returned type character varying(3) does not match expected type text in column 6
```

## **🎯 PROBLEM:**
The database function `get_user_money_requests` has a type mismatch between:
- **Table definition**: `currency VARCHAR(3)` 
- **Function definition**: `currency TEXT`

## **✅ QUICK FIX:**

### **Step 1: Open Supabase SQL Editor**
1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Create a new query

### **Step 2: Copy and Execute This SQL**
```sql
-- Fix Money Requests Function - Type Mismatch Issue
-- This fixes the "structure of query does not match function result type" error

-- Drop and recreate the function with correct return types
DROP FUNCTION IF EXISTS get_user_money_requests(UUID, INTEGER, INTEGER, TEXT);

-- Create function to get user's money request history with correct types
CREATE OR REPLACE FUNCTION get_user_money_requests(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_status TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    type TEXT,
    contact_name TEXT,
    contact_phone TEXT,
    amount DECIMAL,
    currency VARCHAR(3),
    purpose VARCHAR(100),
    note TEXT,
    status VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        CASE 
            WHEN mr.requester_id = p_user_id THEN 'sent'::TEXT
            ELSE 'received'::TEXT
        END as type,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_name
            ELSE up.full_name
        END as contact_name,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_phone
            ELSE up.phone_number
        END as contact_phone,
        mr.amount,
        mr.currency,
        mr.purpose,
        mr.note,
        mr.status,
        mr.created_at,
        mr.updated_at,
        mr.expires_at,
        mr.approved_at,
        mr.declined_at
    FROM public.money_requests mr
    LEFT JOIN public.user_profiles up ON mr.requester_id = up.user_id
    WHERE 
        (mr.requester_id = p_user_id OR mr.recipient_id = p_user_id OR 
         mr.recipient_phone IN (
             SELECT phone_number FROM public.user_profiles 
             WHERE user_id = p_user_id
         ))
        AND (p_status IS NULL OR mr.status = p_status)
    ORDER BY mr.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_money_requests TO authenticated;
```

### **Step 3: Execute the SQL**
1. Click **Run** to execute the SQL
2. You should see: `Success. No rows returned`

### **Step 4: Test the Fix**
1. Go back to your app
2. Press **Request Money** again
3. The error should be gone!

---

## **🔍 WHAT THIS FIXES:**

1. **Type Mismatch**: Changes function return types to match table column types
2. **Currency Field**: `VARCHAR(3)` instead of `TEXT`
3. **Purpose Field**: `VARCHAR(100)` instead of `TEXT`  
4. **Status Field**: `VARCHAR(20)` instead of `TEXT`

---

## **✅ EXPECTED RESULT:**

After applying this fix:
- ✅ No more "structure of query does not match function result type" error
- ✅ Request Money screen loads without errors
- ✅ Request History loads (empty list initially, which is correct)
- ✅ Recent contacts load properly

---

## **🧪 TESTING:**

1. **Open Request Money** - Should load without errors
2. **Check Request History** - Should show empty list (no dummy data)
3. **Try creating a request** - Should work properly

The money request functionality will now work correctly with real database operations instead of dummy data!
