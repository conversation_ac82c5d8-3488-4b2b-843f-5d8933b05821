-- Enhanced Dashboard Critical Fixes
-- Run this SQL to fix all database-related issues

-- 1. Add missing foreign key constraint for budget_categories
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_budget_categories_budget_id'
    ) THEN
        ALTER TABLE budget_categories 
        ADD CONSTRAINT fk_budget_categories_budget_id 
        FOREIGN KEY (budget_id) REFERENCES user_budgets(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key constraint for budget_categories';
    ELSE
        RAISE NOTICE 'Foreign key constraint already exists for budget_categories';
    END IF;
END $$;

-- 2. Add missing merchant_name column to transactions table
DO $$
BEGIN
    -- Check if the column already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'merchant_name'
    ) THEN
        ALTER TABLE transactions 
        ADD COLUMN merchant_name VARCHAR(255);
        
        -- Update existing records with a default value
        UPDATE transactions 
        SET merchant_name = 'Unknown Merchant' 
        WHERE merchant_name IS NULL;
        
        RAISE NOTICE 'Added merchant_name column to transactions table';
    ELSE
        RAISE NOTICE 'merchant_name column already exists in transactions table';
    END IF;
END $$;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transactions_merchant_name ON transactions(merchant_name);
CREATE INDEX IF NOT EXISTS idx_transactions_user_category ON transactions(user_id, category);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- 4. Verify all budget tables exist and have proper structure
DO $$
DECLARE
    missing_tables TEXT[] := ARRAY[]::TEXT[];
    current_table TEXT;
BEGIN
    -- Check for missing tables
    FOR current_table IN
        SELECT unnest(ARRAY['user_budgets', 'budget_categories', 'budget_alerts', 'spending_forecasts', 'budget_performance', 'predictive_models'])
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = current_table
        ) THEN
            missing_tables := array_append(missing_tables, current_table);
        END IF;
    END LOOP;

    IF array_length(missing_tables, 1) > 0 THEN
        RAISE NOTICE 'Missing tables: %', array_to_string(missing_tables, ', ');
        RAISE NOTICE 'Please run the PREDICTIVE_BUDGETING_SCHEMA.sql script to create missing tables';
    ELSE
        RAISE NOTICE 'All budget tables exist';
    END IF;
END $$;

-- 5. Add sample data for testing (optional - only if no data exists)
DO $$
DECLARE
    test_user_id UUID;
    sample_budget_id UUID;
BEGIN
    -- Check if we have any test data
    SELECT user_id INTO test_user_id 
    FROM user_profiles 
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        -- Check if user has any budgets
        IF NOT EXISTS (
            SELECT 1 FROM user_budgets WHERE user_id = test_user_id
        ) THEN
            -- Create a sample budget for testing
            INSERT INTO user_budgets (
                user_id, 
                name, 
                description, 
                total_amount, 
                start_date,
                is_active
            ) VALUES (
                test_user_id,
                'Monthly Budget',
                'Sample budget for testing Enhanced Dashboard',
                2000000, -- 2M UGX
                CURRENT_DATE,
                true
            ) RETURNING id INTO sample_budget_id;
            
            -- Add sample budget categories
            INSERT INTO budget_categories (budget_id, category_name, allocated_amount, spent_amount) VALUES
            (sample_budget_id, 'food_dining', 500000, 0),
            (sample_budget_id, 'transportation', 300000, 0),
            (sample_budget_id, 'bills_utilities', 400000, 0),
            (sample_budget_id, 'entertainment', 200000, 0),
            (sample_budget_id, 'shopping', 300000, 0),
            (sample_budget_id, 'healthcare', 200000, 0),
            (sample_budget_id, 'savings', 100000, 0);
            
            RAISE NOTICE 'Created sample budget data for testing';
        END IF;
        
        -- Add some sample transactions if none exist
        IF NOT EXISTS (
            SELECT 1 FROM transactions WHERE user_id = test_user_id
        ) THEN
            INSERT INTO transactions (
                user_id, 
                amount, 
                description, 
                category, 
                merchant_name,
                created_at
            ) VALUES
            (test_user_id, -50000, 'Grocery shopping', 'food_dining', 'Shoprite', NOW() - INTERVAL '1 day'),
            (test_user_id, -25000, 'Taxi fare', 'transportation', 'SafeBoda', NOW() - INTERVAL '2 days'),
            (test_user_id, -150000, 'Electricity bill', 'bills_utilities', 'UMEME', NOW() - INTERVAL '3 days'),
            (test_user_id, -30000, 'Movie tickets', 'entertainment', 'Century Cinemax', NOW() - INTERVAL '4 days'),
            (test_user_id, 500000, 'Salary deposit', 'income', 'Company Ltd', NOW() - INTERVAL '5 days');
            
            RAISE NOTICE 'Created sample transaction data for testing';
        END IF;
    END IF;
END $$;

-- 6. Update RLS policies to ensure proper access
DO $$
BEGIN
    -- Refresh RLS policies for budget tables
    DROP POLICY IF EXISTS user_budgets_policy ON user_budgets;
    CREATE POLICY user_budgets_policy ON user_budgets
        FOR ALL USING (auth.uid()::text = user_id::text);
    
    DROP POLICY IF EXISTS budget_categories_policy ON budget_categories;
    CREATE POLICY budget_categories_policy ON budget_categories
        FOR ALL USING (
            budget_id IN (
                SELECT id FROM user_budgets WHERE user_id::text = auth.uid()::text
            )
        );
    
    RAISE NOTICE 'Updated RLS policies for budget tables';
END $$;

-- 7. Verify the fixes
DO $$
BEGIN
    RAISE NOTICE 'Enhanced Dashboard database fixes completed successfully!';
    RAISE NOTICE 'Running verification checks...';
END $$;

SELECT
    'Database Fix Verification' as status,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_budget_categories_budget_id'
        ) THEN '✅ Foreign key constraint exists'
        ELSE '❌ Foreign key constraint missing'
    END as foreign_key_status,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'transactions' AND column_name = 'merchant_name'
        ) THEN '✅ merchant_name column exists'
        ELSE '❌ merchant_name column missing'
    END as merchant_column_status,
    (
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN ('user_budgets', 'budget_categories', 'budget_alerts', 'spending_forecasts', 'budget_performance', 'predictive_models')
    ) as budget_tables_count;
