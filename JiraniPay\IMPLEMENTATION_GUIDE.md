# **JiraniPay Implementation Guide**
## **Step-by-Step Execution Document**

---

## **📋 IMPLEMENTATION OVERVIEW**

### **Execution Principles**
- ✅ One task at a time, fully tested before proceeding
- ✅ All changes must pass validation before moving forward
- ✅ Rollback procedures documented for each task
- ✅ Progress tracking with clear checkboxes
- ✅ Dependencies clearly mapped and respected

### **Priority Levels**
- **P0**: Critical - Blocks other features
- **P1**: High - Core functionality
- **P2**: Medium - Enhancement features
- **P3**: Low - Nice-to-have features

---

## **🎯 PHASE 1: CORE FINANCIAL FEATURES**

### **TASK 1.1: Enhanced Money Transfer System**
**Priority**: P0 | **Estimated Time**: 3-4 days | **Dependencies**: None

#### **Subtask 1.1.1: Contact Integration & Validation**
**Time Estimate**: 8 hours

**Files to Create/Modify:**
- [ ] `services/contactService.js` (NEW)
- [ ] `components/ContactPicker.js` (NEW)
- [ ] `utils/phoneValidation.js` (MODIFY)
- [ ] `services/sendMoneyService.js` (MODIFY)

**Database Changes:**
```sql
-- Add to migration file: 001_contact_integration.sql
CREATE TABLE favorite_contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    contact_phone VARCHAR(20) NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    frequency_count INTEGER DEFAULT 1,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, contact_phone)
);

CREATE INDEX idx_favorite_contacts_user_id ON favorite_contacts(user_id);
CREATE INDEX idx_favorite_contacts_frequency ON favorite_contacts(user_id, frequency_count DESC);
```

**Implementation Steps:**
1. [ ] Create `contactService.js` with phone validation and contact management
2. [ ] Implement `ContactPicker.js` component with search and favorites
3. [ ] Add phone number validation utilities
4. [ ] Integrate contact picker into send money flow
5. [ ] Add favorite contacts functionality

**Testing Requirements:**
- [ ] Unit tests for phone validation (10 test cases)
- [ ] Unit tests for contact service CRUD operations
- [ ] Integration test for contact picker component
- [ ] Manual test: Add/remove favorite contacts
- [ ] Manual test: Search contacts functionality

**Acceptance Criteria:**
- [ ] Users can select contacts from device contact list
- [ ] Phone numbers are validated for Uganda format
- [ ] Favorite contacts are saved and prioritized
- [ ] Search functionality works for contact names and numbers
- [ ] Contact picker integrates seamlessly with send money flow

**Rollback Procedure:**
1. Remove contact picker from send money screen
2. Drop favorite_contacts table
3. Revert sendMoneyService.js changes
4. Remove contactService.js and ContactPicker.js files

---

#### **Subtask 1.1.2: Transaction Limits & Fraud Detection**
**Time Estimate**: 12 hours

**Files to Create/Modify:**
- [ ] `services/transactionLimitsService.js` (NEW)
- [ ] `services/fraudDetectionService.js` (NEW)
- [ ] `services/sendMoneyService.js` (MODIFY)
- [ ] `utils/riskCalculator.js` (NEW)

**Database Changes:**
```sql
-- Add to migration file: 002_transaction_limits.sql
ALTER TABLE user_profiles ADD COLUMN daily_spent DECIMAL(15,2) DEFAULT 0;
ALTER TABLE user_profiles ADD COLUMN monthly_spent DECIMAL(15,2) DEFAULT 0;
ALTER TABLE user_profiles ADD COLUMN last_reset_date DATE DEFAULT CURRENT_DATE;

CREATE TABLE transaction_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    kyc_level VARCHAR(20) NOT NULL DEFAULT 'basic',
    daily_limit DECIMAL(15,2) NOT NULL,
    monthly_limit DECIMAL(15,2) NOT NULL,
    annual_limit DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

CREATE TABLE fraud_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    risk_score DECIMAL(3,2) NOT NULL,
    risk_factors JSONB,
    action_taken VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Implementation Steps:**
1. [ ] Create transaction limits service with KYC-based limits
2. [ ] Implement basic fraud detection algorithms
3. [ ] Add velocity checking (transactions per hour/day)
4. [ ] Create risk scoring system
5. [ ] Integrate limits checking into send money flow

**Testing Requirements:**
- [ ] Unit tests for transaction limits calculation
- [ ] Unit tests for fraud detection algorithms
- [ ] Integration test for limits enforcement
- [ ] Load test: Multiple rapid transactions
- [ ] Manual test: Exceed daily/monthly limits

**Acceptance Criteria:**
- [ ] Transaction limits enforced based on KYC level
- [ ] Fraud detection flags suspicious patterns
- [ ] Users receive clear error messages when limits exceeded
- [ ] Risk scores calculated for all transactions
- [ ] Suspicious transactions require additional verification

**Rollback Procedure:**
1. Remove fraud detection from transaction flow
2. Drop fraud_scores and transaction_limits tables
3. Remove daily/monthly spent columns from user_profiles
4. Remove transactionLimitsService.js and fraudDetectionService.js

---

#### **Subtask 1.1.3: Real-time Notifications & Receipts**
**Time Estimate**: 10 hours

**Files to Create/Modify:**
- [ ] `services/notificationService.js` (MODIFY)
- [ ] `services/receiptService.js` (NEW)
- [ ] `components/TransactionReceipt.js` (NEW)
- [ ] `utils/pdfGenerator.js` (NEW)

**Dependencies Required:**
```bash
npm install react-native-push-notification
npm install react-native-print
npm install react-native-view-shot
```

**Implementation Steps:**
1. [ ] Enhance notification service for transaction alerts
2. [ ] Create receipt generation service
3. [ ] Implement PDF receipt generation
4. [ ] Add QR code to receipts for verification
5. [ ] Integrate notifications into send money flow

**Testing Requirements:**
- [ ] Unit tests for receipt generation
- [ ] Integration test for notification delivery
- [ ] Manual test: Send money and verify notifications
- [ ] Manual test: Generate and share receipt
- [ ] Performance test: Receipt generation speed

**Acceptance Criteria:**
- [ ] Both sender and receiver get instant notifications
- [ ] PDF receipts generated with transaction details
- [ ] Receipts include QR code for verification
- [ ] Receipts can be shared via multiple channels
- [ ] Notification preferences respected

**Rollback Procedure:**
1. Remove notification calls from send money flow
2. Remove receipt generation functionality
3. Uninstall added dependencies
4. Remove receiptService.js and related components

---

### **TASK 1.2: Bill Payment System Completion**
**Priority**: P0 | **Estimated Time**: 4-5 days | **Dependencies**: Task 1.1 (for payment processing)

#### **Subtask 1.2.1: Provider Integration Framework**
**Time Estimate**: 16 hours

**Files to Create/Modify:**
- [ ] `services/billProviders/baseProvider.js` (NEW)
- [ ] `services/billProviders/umecoProvider.js` (NEW)
- [ ] `services/billProviders/nwscProvider.js` (NEW)
- [ ] `services/billProviders/mtnProvider.js` (NEW)
- [ ] `services/billProviders/airtelProvider.js` (NEW)
- [ ] `services/billPaymentService.js` (MODIFY)

**Database Changes:**
```sql
-- Add to migration file: 003_bill_providers.sql
CREATE TABLE bill_providers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider_code VARCHAR(20) UNIQUE NOT NULL,
    provider_name VARCHAR(100) NOT NULL,
    provider_type VARCHAR(50) NOT NULL, -- electricity, water, telecom
    api_endpoint TEXT,
    is_active BOOLEAN DEFAULT true,
    commission_rate DECIMAL(5,4) DEFAULT 0.0200, -- 2%
    min_amount DECIMAL(15,2) DEFAULT 1000,
    max_amount DECIMAL(15,2) DEFAULT ********,
    validation_regex TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE bill_accounts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES bill_providers(id),
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    is_favorite BOOLEAN DEFAULT false,
    last_used TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, provider_id, account_number)
);

-- Insert default providers
INSERT INTO bill_providers (provider_code, provider_name, provider_type, min_amount, max_amount, validation_regex) VALUES
('UMEME', 'UMEME Limited', 'electricity', 5000, 5000000, '^[0-9]{8,12}$'),
('NWSC', 'National Water & Sewerage Corp', 'water', 2000, 2000000, '^[0-9]{6,10}$'),
('MTN_AIRTIME', 'MTN Airtime', 'telecom', 500, 500000, '^256[0-9]{9}$'),
('AIRTEL_AIRTIME', 'Airtel Airtime', 'telecom', 500, 500000, '^256[0-9]{9}$');
```

**Implementation Steps:**
1. [ ] Create base provider class with common interface
2. [ ] Implement UMEME provider with account validation
3. [ ] Implement NWSC provider integration
4. [ ] Implement MTN/Airtel airtime providers
5. [ ] Create provider factory and registration system

**Testing Requirements:**
- [ ] Unit tests for each provider implementation
- [ ] Mock API tests for provider validation
- [ ] Integration tests for bill payment flow
- [ ] Manual test: Validate real account numbers
- [ ] Error handling tests for invalid accounts

**Acceptance Criteria:**
- [ ] All major Uganda bill providers supported
- [ ] Account validation works for each provider
- [ ] Provider-specific business rules enforced
- [ ] Graceful error handling for API failures
- [ ] Commission calculation accurate

**Rollback Procedure:**
1. Remove provider integrations from bill payment service
2. Drop bill_providers and bill_accounts tables
3. Remove all provider implementation files
4. Revert billPaymentService.js to previous version

#### **Subtask 1.2.2: Bill Payment UI Implementation**
**Time Estimate**: 12 hours

**Files to Create/Modify:**
- [ ] `screens/BillPaymentScreen.js` (MODIFY)
- [ ] `screens/BillProvidersScreen.js` (MODIFY)
- [ ] `screens/BillDetailsScreen.js` (MODIFY)
- [ ] `components/BillProviderCard.js` (NEW)
- [ ] `components/AccountValidationInput.js` (NEW)

**Implementation Steps:**
1. [ ] Redesign bill payment screen with provider categories
2. [ ] Implement provider selection with search functionality
3. [ ] Create account validation input with real-time checking
4. [ ] Add bill amount input with provider-specific limits
5. [ ] Implement payment confirmation flow

**Testing Requirements:**
- [ ] UI component tests for all new components
- [ ] Navigation flow tests
- [ ] Form validation tests
- [ ] Manual test: Complete bill payment flow
- [ ] Accessibility tests for screen readers

**Acceptance Criteria:**
- [ ] Intuitive provider selection interface
- [ ] Real-time account validation feedback
- [ ] Clear payment confirmation screen
- [ ] Error states properly handled
- [ ] Consistent with app design system

**Rollback Procedure:**
1. Revert screen files to previous versions
2. Remove new component files
3. Remove bill payment navigation routes
4. Restore original bill payment placeholder

---

#### **Subtask 1.2.3: Recurring Bill Payments**
**Time Estimate**: 8 hours

**Files to Create/Modify:**
- [ ] `services/recurringBillsService.js` (NEW)
- [ ] `screens/RecurringBillsScreen.js` (NEW)
- [ ] `components/RecurringBillCard.js` (NEW)
- [ ] `utils/scheduleManager.js` (NEW)

**Database Changes:**
```sql
-- Add to migration file: 004_recurring_bills.sql
CREATE TABLE recurring_bills (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    bill_account_id UUID REFERENCES bill_accounts(id) ON DELETE CASCADE,
    amount DECIMAL(15,2),
    frequency VARCHAR(20) NOT NULL, -- weekly, monthly, quarterly
    next_payment_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    auto_pay BOOLEAN DEFAULT false,
    reminder_days INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_recurring_bills_next_payment ON recurring_bills(next_payment_date) WHERE is_active = true;
```

**Implementation Steps:**
1. [ ] Create recurring bills service with scheduling logic
2. [ ] Implement recurring bill management screen
3. [ ] Add bill reminder notifications
4. [ ] Create automatic payment processing
5. [ ] Add recurring bill analytics

**Testing Requirements:**
- [ ] Unit tests for scheduling logic
- [ ] Integration tests for automatic payments
- [ ] Manual test: Set up recurring bill
- [ ] Manual test: Receive bill reminders
- [ ] Edge case tests: Failed automatic payments

**Acceptance Criteria:**
- [ ] Users can set up recurring bill payments
- [ ] Automatic payments process correctly
- [ ] Reminders sent before due dates
- [ ] Failed payments handled gracefully
- [ ] Users can modify/cancel recurring bills

**Rollback Procedure:**
1. Disable automatic payment processing
2. Drop recurring_bills table
3. Remove recurring bills service and screens
4. Remove bill reminder notifications

---

### **TASK 1.3: Savings & Investment Features**
**Priority**: P1 | **Estimated Time**: 5-6 days | **Dependencies**: Task 1.1 (for transfers)

#### **Subtask 1.3.1: Savings Goals Implementation**
**Time Estimate**: 14 hours

**Files to Create/Modify:**
- [ ] `services/savingsGoalsService.js` (NEW)
- [ ] `screens/SavingsGoalsScreen.js` (NEW)
- [ ] `screens/CreateSavingsGoalScreen.js` (NEW)
- [ ] `components/SavingsGoalCard.js` (NEW)
- [ ] `components/ProgressCircle.js` (NEW)

**Database Changes:**
```sql
-- Add to migration file: 005_savings_goals.sql
CREATE TABLE savings_goals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    goal_name VARCHAR(100) NOT NULL,
    target_amount DECIMAL(15,2) NOT NULL,
    current_amount DECIMAL(15,2) DEFAULT 0,
    target_date DATE,
    category VARCHAR(50), -- emergency, vacation, education, etc.
    is_active BOOLEAN DEFAULT true,
    auto_save_enabled BOOLEAN DEFAULT false,
    auto_save_amount DECIMAL(15,2) DEFAULT 0,
    auto_save_frequency VARCHAR(20), -- daily, weekly, monthly
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE savings_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    savings_goal_id UUID REFERENCES savings_goals(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES transactions(id),
    amount DECIMAL(15,2) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- deposit, withdrawal
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_savings_goals_user_id ON savings_goals(user_id);
CREATE INDEX idx_savings_transactions_goal_id ON savings_transactions(savings_goal_id);
```

**Implementation Steps:**
1. [ ] Create savings goals service with CRUD operations
2. [ ] Implement savings goals overview screen
3. [ ] Create goal creation wizard
4. [ ] Add progress visualization components
5. [ ] Implement goal achievement notifications

**Testing Requirements:**
- [ ] Unit tests for savings calculations
- [ ] Integration tests for goal creation
- [ ] Manual test: Create and fund savings goal
- [ ] Manual test: Goal progress updates
- [ ] Performance test: Multiple goals rendering

**Acceptance Criteria:**
- [ ] Users can create multiple savings goals
- [ ] Progress tracked accurately with visual indicators
- [ ] Goal categories help with organization
- [ ] Achievement milestones celebrated
- [ ] Goals can be edited or deleted

**Rollback Procedure:**
1. Remove savings goals from navigation
2. Drop savings_goals and savings_transactions tables
3. Remove savings goals service and screens
4. Remove progress visualization components

---

## **🚀 PHASE 2: UX & ANALYTICS ENHANCEMENT**

### **TASK 2.1: Enhanced Dashboard Analytics**
**Priority**: P1 | **Estimated Time**: 3-4 days | **Dependencies**: Task 1.1 (transaction data)

#### **Subtask 2.1.1: Smart Transaction Categorization**
**Time Estimate**: 10 hours

**Files to Create/Modify:**
- [ ] `services/categorizationService.js` (NEW)
- [ ] `utils/mlCategorization.js` (NEW)
- [ ] `components/CategoryPieChart.js` (NEW)
- [ ] `screens/DashboardScreen.js` (MODIFY)

**Database Changes:**
```sql
-- Add to migration file: 008_categorization.sql
CREATE TABLE transaction_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_code VARCHAR(20) UNIQUE NOT NULL,
    category_name VARCHAR(50) NOT NULL,
    parent_category VARCHAR(20),
    icon_name VARCHAR(50),
    color_code VARCHAR(7),
    keywords TEXT[], -- Array of keywords for auto-categorization
    is_active BOOLEAN DEFAULT true
);

ALTER TABLE transactions ADD COLUMN category_id UUID REFERENCES transaction_categories(id);
ALTER TABLE transactions ADD COLUMN auto_categorized BOOLEAN DEFAULT false;

-- Insert default categories
INSERT INTO transaction_categories (category_code, category_name, icon_name, color_code, keywords) VALUES
('FOOD', 'Food & Dining', 'restaurant', '#FF6B6B', ARRAY['restaurant', 'food', 'cafe', 'lunch', 'dinner']),
('TRANSPORT', 'Transportation', 'car', '#4ECDC4', ARRAY['taxi', 'boda', 'fuel', 'transport', 'uber']),
('UTILITIES', 'Utilities', 'flash', '#45B7D1', ARRAY['umeme', 'electricity', 'water', 'nwsc']),
('SHOPPING', 'Shopping', 'bag', '#96CEB4', ARRAY['shop', 'store', 'market', 'buy']),
('ENTERTAINMENT', 'Entertainment', 'play', '#FFEAA7', ARRAY['movie', 'game', 'fun', 'entertainment']),
('HEALTH', 'Healthcare', 'medical', '#FD79A8', ARRAY['hospital', 'doctor', 'medicine', 'health']),
('EDUCATION', 'Education', 'school', '#A29BFE', ARRAY['school', 'university', 'education', 'fees']),
('SAVINGS', 'Savings', 'piggy-bank', '#00B894', ARRAY['savings', 'investment', 'deposit']);
```

---

## **📝 TASK COMPLETION TRACKING**

### **Phase 1 Progress**
- [ ] Task 1.1.1: Contact Integration & Validation
- [ ] Task 1.1.2: Transaction Limits & Fraud Detection
- [ ] Task 1.1.3: Real-time Notifications & Receipts
- [ ] Task 1.2.1: Provider Integration Framework
- [ ] Task 1.2.2: Bill Payment UI Implementation
- [ ] Task 1.2.3: Recurring Bill Payments
- [ ] Task 1.3.1: Savings Goals Implementation

### **Phase 2 Progress**
- [ ] Task 2.1.1: Smart Transaction Categorization

### **Next Steps**
After completing current tasks, proceed to:
- [ ] Task 2.1.2: Predictive Analytics & Budgeting
- [ ] Task 2.2: Enhanced QR Payment System
- [ ] Phase 3: Security & Compliance Enhancements

---

## **🚨 EMERGENCY PROCEDURES**

### **If Implementation Fails:**
1. **Stop immediately** - Don't proceed to next task
2. **Check rollback procedure** for current task
3. **Execute rollback steps** in reverse order
4. **Verify system stability** before attempting fix
5. **Document the issue** and resolution approach

### **Before Starting Each Task:**
1. **Create git branch** for the task
2. **Backup current database** state
3. **Verify all dependencies** are met
4. **Review acceptance criteria** thoroughly

### **After Completing Each Task:**
1. **Run all tests** (unit, integration, manual)
2. **Verify acceptance criteria** are met
3. **Create git commit** with detailed message
4. **Update progress** in this document
5. **Proceed to next task** only after validation

---

## **📞 SUPPORT & ESCALATION**

### **When to Escalate:**
- Task blocked for more than 4 hours
- Acceptance criteria cannot be met
- Rollback procedure fails
- Critical system functionality broken

### **Escalation Process:**
1. Document the specific issue
2. List attempted solutions
3. Identify impact on other tasks
4. Propose alternative approaches

---

**Last Updated**: [DATE]
**Current Task**: [TASK_NUMBER]
**Status**: [IN_PROGRESS/COMPLETED/BLOCKED]
