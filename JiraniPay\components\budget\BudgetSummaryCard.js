/**
 * Budget Summary Card Component
 * Displays overall budget analytics and forecasts
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';

const BudgetSummaryCard = ({ analytics, forecasts, theme, formatCurrency }) => {
  if (!analytics) return null;

  // Calculate next month forecast
  const nextMonthForecast = forecasts?.forecasts?.[0];
  const forecastAmount = nextMonthForecast?.predicted || 0;
  const forecastConfidence = (nextMonthForecast?.confidence || 0) * 100;

  // Calculate savings rate
  const savingsRate = analytics.totalBudgeted > 0 
    ? Math.max(0, (analytics.totalBudgeted - analytics.totalSpent) / analytics.totalBudgeted * 100)
    : 0;

  // Get status color based on utilization
  const getUtilizationColor = () => {
    if (analytics.averageUtilization > 1) return Colors.status.error;
    if (analytics.averageUtilization > 0.8) return Colors.status.warning;
    return Colors.status.success;
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Budget Overview
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: getUtilizationColor() + '20' }]}>
          <Text style={[styles.statusText, { color: getUtilizationColor() }]}>
            {analytics.averageUtilization > 1 ? 'Over Budget' : 
             analytics.averageUtilization > 0.8 ? 'On Track' : 'Under Budget'}
          </Text>
        </View>
      </View>

      {/* Main Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Total Budgeted
            </Text>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {formatCurrency(analytics.totalBudgeted || 0, 'UGX')}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Total Spent
            </Text>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              {formatCurrency(analytics.totalSpent || 0, 'UGX')}
            </Text>
          </View>
        </View>

        <View style={styles.statRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Utilization Rate
            </Text>
            <Text style={[styles.statValue, { color: getUtilizationColor() }]}>
              {(analytics.averageUtilization * 100).toFixed(1)}%
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Savings Rate
            </Text>
            <Text style={[styles.statValue, { color: Colors.status.success }]}>
              {savingsRate.toFixed(1)}%
            </Text>
          </View>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <Text style={[styles.progressLabel, { color: theme.colors.text }]}>
          Overall Budget Progress
        </Text>
        <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
          <LinearGradient
            colors={[getUtilizationColor(), getUtilizationColor() + '80']}
            style={[
              styles.progressFill,
              { width: `${Math.min(analytics.averageUtilization * 100, 100)}%` }
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
        </View>
        <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
          {(analytics.averageUtilization * 100).toFixed(1)}% of budget used
        </Text>
      </View>

      {/* Categories Summary */}
      <View style={styles.categoriesContainer}>
        <Text style={[styles.categoriesTitle, { color: theme.colors.text }]}>
          Categories Status
        </Text>
        <View style={styles.categoriesStats}>
          <View style={styles.categoryStatItem}>
            <View style={[styles.categoryStatIcon, { backgroundColor: Colors.status.error + '20' }]}>
              <Ionicons name="trending-up" size={16} color={Colors.status.error} />
            </View>
            <Text style={[styles.categoryStatValue, { color: Colors.status.error }]}>
              {analytics.categoriesOverBudget || 0}
            </Text>
            <Text style={[styles.categoryStatLabel, { color: theme.colors.textSecondary }]}>
              Over Budget
            </Text>
          </View>
          
          <View style={styles.categoryStatItem}>
            <View style={[styles.categoryStatIcon, { backgroundColor: Colors.status.success + '20' }]}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
            </View>
            <Text style={[styles.categoryStatValue, { color: Colors.status.success }]}>
              {analytics.categoriesUnderBudget || 0}
            </Text>
            <Text style={[styles.categoryStatLabel, { color: theme.colors.textSecondary }]}>
              Under Budget
            </Text>
          </View>

          <View style={styles.categoryStatItem}>
            <View style={[styles.categoryStatIcon, { backgroundColor: Colors.primary.main + '20' }]}>
              <Ionicons name="apps" size={16} color={Colors.primary.main} />
            </View>
            <Text style={[styles.categoryStatValue, { color: Colors.primary.main }]}>
              {analytics.activeBudgets || 0}
            </Text>
            <Text style={[styles.categoryStatLabel, { color: theme.colors.textSecondary }]}>
              Active Budgets
            </Text>
          </View>
        </View>
      </View>

      {/* Forecast Section */}
      {nextMonthForecast && (
        <View style={styles.forecastContainer}>
          <View style={styles.forecastHeader}>
            <Ionicons name="trending-up" size={20} color={Colors.primary.main} />
            <Text style={[styles.forecastTitle, { color: theme.colors.text }]}>
              Next Month Forecast
            </Text>
          </View>
          <View style={styles.forecastContent}>
            <View style={styles.forecastItem}>
              <Text style={[styles.forecastLabel, { color: theme.colors.textSecondary }]}>
                Predicted Spending
              </Text>
              <Text style={[styles.forecastValue, { color: theme.colors.text }]}>
                {formatCurrency(forecastAmount, 'UGX')}
              </Text>
            </View>
            <View style={styles.forecastItem}>
              <Text style={[styles.forecastLabel, { color: theme.colors.textSecondary }]}>
                Confidence
              </Text>
              <View style={styles.confidenceContainer}>
                <Text style={[styles.forecastValue, { color: Colors.primary.main }]}>
                  {forecastConfidence.toFixed(0)}%
                </Text>
                <View style={[styles.confidenceBar, { backgroundColor: theme.colors.border }]}>
                  <View 
                    style={[
                      styles.confidenceFill,
                      { 
                        width: `${forecastConfidence}%`,
                        backgroundColor: Colors.primary.main
                      }
                    ]} 
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statsContainer: {
    marginBottom: 20,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
  },
  statLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  categoriesContainer: {
    marginBottom: 20,
  },
  categoriesTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  categoriesStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  categoryStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  categoryStatIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryStatValue: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  categoryStatLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  forecastContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: 16,
  },
  forecastHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  forecastTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  forecastContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  forecastItem: {
    flex: 1,
  },
  forecastLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  forecastValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  confidenceBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2,
  },
});

export default BudgetSummaryCard;
