/**
 * Recommendation Card Component
 * Displays budget recommendations and insights
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';

const RecommendationCard = ({ recommendation, onPress, theme }) => {
  // Get icon based on recommendation type
  const getIcon = () => {
    switch (recommendation.type) {
      case 'warning':
        return 'warning';
      case 'alert':
        return 'alert-circle';
      case 'suggestion':
        return 'bulb';
      case 'insight':
        return 'analytics';
      default:
        return 'information-circle';
    }
  };

  // Get color based on priority
  const getColor = () => {
    switch (recommendation.priority) {
      case 'high':
        return Colors.status.error;
      case 'medium':
        return Colors.status.warning;
      case 'low':
        return Colors.primary.main;
      default:
        return Colors.primary.main;
    }
  };

  // Get background color
  const getBackgroundColor = () => {
    return getColor() + '10';
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { 
          backgroundColor: theme.colors.surface,
          borderLeftColor: getColor()
        }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Icon */}
      <View style={[styles.iconContainer, { backgroundColor: getBackgroundColor() }]}>
        <Ionicons 
          name={getIcon()} 
          size={20} 
          color={getColor()} 
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {recommendation.title}
          </Text>
          {recommendation.priority && (
            <View style={[styles.priorityBadge, { backgroundColor: getBackgroundColor() }]}>
              <Text style={[styles.priorityText, { color: getColor() }]}>
                {recommendation.priority.toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        
        <Text style={[styles.message, { color: theme.colors.textSecondary }]}>
          {recommendation.message}
        </Text>

        {/* Action hint */}
        {recommendation.action && (
          <View style={styles.actionContainer}>
            <Text style={[styles.actionText, { color: getColor() }]}>
              Tap to take action
            </Text>
            <Ionicons 
              name="chevron-forward" 
              size={16} 
              color={getColor()} 
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default RecommendationCard;
