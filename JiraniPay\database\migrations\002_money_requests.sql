-- Money Requests Schema for JiraniPay
-- This migration creates the money_requests table and related functionality

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create money_requests table
CREATE TABLE IF NOT EXISTS public.money_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    recipient_phone VARCHAR(20) NOT NULL,
    recipient_name VARCHAR(255),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    purpose VARCHAR(100),
    note TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'declined', 'expired', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    approved_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT valid_expiry CHECK (expires_at > created_at),
    CONSTRAINT valid_approval CHECK (
        (status = 'approved' AND approved_at IS NOT NULL) OR 
        (status != 'approved' AND approved_at IS NULL)
    ),
    CONSTRAINT valid_decline CHECK (
        (status = 'declined' AND declined_at IS NOT NULL) OR 
        (status != 'declined' AND declined_at IS NULL)
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_money_requests_requester_id ON public.money_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_money_requests_recipient_id ON public.money_requests(recipient_id);
CREATE INDEX IF NOT EXISTS idx_money_requests_recipient_phone ON public.money_requests(recipient_phone);
CREATE INDEX IF NOT EXISTS idx_money_requests_status ON public.money_requests(status);
CREATE INDEX IF NOT EXISTS idx_money_requests_created_at ON public.money_requests(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_money_requests_expires_at ON public.money_requests(expires_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_money_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_money_requests_updated_at
    BEFORE UPDATE ON public.money_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_money_requests_updated_at();

-- Enable Row Level Security
ALTER TABLE public.money_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for money_requests
-- Users can view requests they sent or received
CREATE POLICY "Users can view their own money requests" ON public.money_requests
    FOR SELECT USING (
        auth.uid() = requester_id OR 
        auth.uid() = recipient_id OR
        recipient_phone IN (
            SELECT phone_number FROM public.user_profiles 
            WHERE user_id = auth.uid()
        )
    );

-- Users can create money requests
CREATE POLICY "Users can create money requests" ON public.money_requests
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

-- Users can update their own requests (cancel) or respond to requests sent to them
CREATE POLICY "Users can update money requests" ON public.money_requests
    FOR UPDATE USING (
        auth.uid() = requester_id OR 
        auth.uid() = recipient_id OR
        recipient_phone IN (
            SELECT phone_number FROM public.user_profiles 
            WHERE user_id = auth.uid()
        )
    );

-- Create function to get user's money request history
CREATE OR REPLACE FUNCTION get_user_money_requests(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_status TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    type TEXT,
    contact_name TEXT,
    contact_phone TEXT,
    amount DECIMAL,
    currency VARCHAR(3),
    purpose VARCHAR(100),
    note TEXT,
    status VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        CASE 
            WHEN mr.requester_id = p_user_id THEN 'sent'::TEXT
            ELSE 'received'::TEXT
        END as type,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_name
            ELSE up.full_name
        END as contact_name,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_phone
            ELSE up.phone_number
        END as contact_phone,
        mr.amount,
        mr.currency,
        mr.purpose,
        mr.note,
        mr.status,
        mr.created_at,
        mr.updated_at,
        mr.expires_at,
        mr.approved_at,
        mr.declined_at
    FROM public.money_requests mr
    LEFT JOIN public.user_profiles up ON mr.requester_id = up.user_id
    WHERE 
        (mr.requester_id = p_user_id OR mr.recipient_id = p_user_id OR 
         mr.recipient_phone IN (
             SELECT phone_number FROM public.user_profiles 
             WHERE user_id = p_user_id
         ))
        AND (p_status IS NULL OR mr.status = p_status)
    ORDER BY mr.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Create function to create a money request
CREATE OR REPLACE FUNCTION create_money_request(
    p_requester_id UUID,
    p_recipient_phone TEXT,
    p_recipient_name TEXT,
    p_amount DECIMAL,
    p_currency TEXT DEFAULT 'UGX',
    p_purpose TEXT DEFAULT NULL,
    p_note TEXT DEFAULT NULL,
    p_expires_in_days INTEGER DEFAULT 7
)
RETURNS TABLE (
    success BOOLEAN,
    request_id UUID,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_request_id UUID;
    v_recipient_id UUID;
BEGIN
    -- Try to find recipient by phone number
    SELECT user_id INTO v_recipient_id
    FROM public.user_profiles
    WHERE phone_number = p_recipient_phone
    LIMIT 1;

    -- Insert the money request
    INSERT INTO public.money_requests (
        requester_id,
        recipient_id,
        recipient_phone,
        recipient_name,
        amount,
        currency,
        purpose,
        note,
        expires_at
    ) VALUES (
        p_requester_id,
        v_recipient_id,
        p_recipient_phone,
        p_recipient_name,
        p_amount,
        p_currency,
        p_purpose,
        p_note,
        NOW() + (p_expires_in_days || ' days')::INTERVAL
    ) RETURNING id INTO v_request_id;

    RETURN QUERY SELECT 
        TRUE as success,
        v_request_id as request_id,
        'Money request created successfully'::TEXT as message;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT 
        FALSE as success,
        NULL::UUID as request_id,
        SQLERRM::TEXT as message;
END;
$$;

-- Create function to respond to money request
CREATE OR REPLACE FUNCTION respond_to_money_request(
    p_request_id UUID,
    p_user_id UUID,
    p_response TEXT -- 'approve' or 'decline'
)
RETURNS TABLE (
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_request_record RECORD;
BEGIN
    -- Get the request details
    SELECT * INTO v_request_record
    FROM public.money_requests
    WHERE id = p_request_id;

    -- Check if request exists
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, 'Money request not found'::TEXT;
        RETURN;
    END IF;

    -- Check if user is authorized to respond
    IF v_request_record.recipient_id != p_user_id AND 
       v_request_record.recipient_phone NOT IN (
           SELECT phone_number FROM public.user_profiles WHERE user_id = p_user_id
       ) THEN
        RETURN QUERY SELECT FALSE, 'Not authorized to respond to this request'::TEXT;
        RETURN;
    END IF;

    -- Check if request is still pending
    IF v_request_record.status != 'pending' THEN
        RETURN QUERY SELECT FALSE, 'Request is no longer pending'::TEXT;
        RETURN;
    END IF;

    -- Check if request has expired
    IF v_request_record.expires_at < NOW() THEN
        UPDATE public.money_requests 
        SET status = 'expired', updated_at = NOW()
        WHERE id = p_request_id;
        
        RETURN QUERY SELECT FALSE, 'Request has expired'::TEXT;
        RETURN;
    END IF;

    -- Update the request based on response
    IF p_response = 'approve' THEN
        UPDATE public.money_requests 
        SET 
            status = 'approved',
            approved_at = NOW(),
            updated_at = NOW()
        WHERE id = p_request_id;
        
        RETURN QUERY SELECT TRUE, 'Money request approved'::TEXT;
    ELSIF p_response = 'decline' THEN
        UPDATE public.money_requests 
        SET 
            status = 'declined',
            declined_at = NOW(),
            updated_at = NOW()
        WHERE id = p_request_id;
        
        RETURN QUERY SELECT TRUE, 'Money request declined'::TEXT;
    ELSE
        RETURN QUERY SELECT FALSE, 'Invalid response. Use approve or decline'::TEXT;
    END IF;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT FALSE, SQLERRM::TEXT;
END;
$$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.money_requests TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_money_requests TO authenticated;
GRANT EXECUTE ON FUNCTION create_money_request TO authenticated;
GRANT EXECUTE ON FUNCTION respond_to_money_request TO authenticated;
