-- Fraud Detection & Risk Management Database Schema
-- Creates tables for transaction limits, fraud rules, risk scores, and security events

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TRANSACTION LIMITS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.transaction_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    verification_level VARCHAR(20) NOT NULL CHECK (verification_level IN ('basic', 'enhanced', 'premium')),
    daily_limit DECIMAL(15,2) NOT NULL DEFAULT 500000,
    weekly_limit DECIMAL(15,2) NOT NULL DEFAULT 2000000,
    monthly_limit DECIMAL(15,2) NOT NULL DEFAULT 8000000,
    single_transaction_limit DECIMAL(15,2) NOT NULL DEFAULT 100000,
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    is_active BOOLEAN NOT NULL DEFAULT true,
    custom_limits JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_limits CHECK (
        daily_limit > 0 AND 
        weekly_limit > 0 AND 
        monthly_limit > 0 AND 
        single_transaction_limit > 0
    ),
    CONSTRAINT logical_limits CHECK (
        weekly_limit >= daily_limit AND 
        monthly_limit >= weekly_limit
    ),
    
    -- Unique constraint per user
    UNIQUE(user_id)
);

-- =====================================================
-- FRAUD RULES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.fraud_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(100) NOT NULL UNIQUE,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('velocity', 'amount', 'device', 'location', 'behavioral', 'pattern')),
    description TEXT,
    conditions JSONB NOT NULL,
    risk_score INTEGER NOT NULL CHECK (risk_score BETWEEN 0 AND 100),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RISK SCORES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.risk_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    risk_score INTEGER NOT NULL CHECK (risk_score BETWEEN 0 AND 100),
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('minimal', 'low', 'medium', 'high', 'critical')),
    components JSONB NOT NULL DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    calculation_method VARCHAR(50) DEFAULT 'standard',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- FRAUD ANALYSIS LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.fraud_analysis_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    transaction_data JSONB NOT NULL,
    risk_score INTEGER NOT NULL CHECK (risk_score BETWEEN 0 AND 100),
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('minimal', 'low', 'medium', 'high', 'critical')),
    risk_factors JSONB DEFAULT '[]',
    recommendation JSONB NOT NULL,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SECURITY EVENTS TABLE (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'login_attempt', 'login_failure', 'fraud_alert', 'limit_violation', 
        'suspicious_activity', 'device_change', 'location_change', 'password_change',
        'account_lockout', 'verification_attempt'
    )),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'low', 'medium', 'high', 'critical')),
    source_ip INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    details JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TRUSTED DEVICES TABLE (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.trusted_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(100),
    device_type VARCHAR(50) CHECK (device_type IN ('mobile', 'tablet', 'desktop', 'unknown')),
    os_info JSONB DEFAULT '{}',
    browser_info JSONB DEFAULT '{}',
    fingerprint_hash VARCHAR(255),
    is_trusted BOOLEAN NOT NULL DEFAULT false,
    trust_score INTEGER DEFAULT 50 CHECK (trust_score BETWEEN 0 AND 100),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint
    UNIQUE(user_id, device_id)
);

-- =====================================================
-- USER SESSIONS TABLE (Enhanced for fraud detection)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE,
    device_id VARCHAR(255),
    ip_address INET,
    location VARCHAR(100),
    location_details JSONB DEFAULT '{}',
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    risk_score INTEGER DEFAULT 0 CHECK (risk_score BETWEEN 0 AND 100),
    login_method VARCHAR(50) DEFAULT 'password',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Add columns to existing user_sessions table if they don't exist
DO $$
BEGIN
    -- Add device_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_sessions' AND column_name = 'device_id') THEN
        ALTER TABLE public.user_sessions ADD COLUMN device_id VARCHAR(255);
    END IF;

    -- Add location_details column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_sessions' AND column_name = 'location_details') THEN
        ALTER TABLE public.user_sessions ADD COLUMN location_details JSONB DEFAULT '{}';
    END IF;

    -- Add risk_score column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_sessions' AND column_name = 'risk_score') THEN
        ALTER TABLE public.user_sessions ADD COLUMN risk_score INTEGER DEFAULT 0 CHECK (risk_score BETWEEN 0 AND 100);
    END IF;

    -- Add login_method column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_sessions' AND column_name = 'login_method') THEN
        ALTER TABLE public.user_sessions ADD COLUMN login_method VARCHAR(50) DEFAULT 'password';
    END IF;
END $$;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Risk Scores Indexes
CREATE INDEX IF NOT EXISTS idx_risk_scores_user_created ON public.risk_scores(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_risk_scores_level ON public.risk_scores(risk_level);
CREATE INDEX IF NOT EXISTS idx_risk_scores_score ON public.risk_scores(risk_score DESC);

-- Fraud Analysis Logs Indexes
CREATE INDEX IF NOT EXISTS idx_fraud_logs_user_created ON public.fraud_analysis_logs(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_fraud_logs_risk_level ON public.fraud_analysis_logs(risk_level);
CREATE INDEX IF NOT EXISTS idx_fraud_logs_created ON public.fraud_analysis_logs(created_at DESC);

-- Security Events Indexes
CREATE INDEX IF NOT EXISTS idx_security_events_user_created ON public.security_events(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON public.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON public.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_unresolved ON public.security_events(resolved, created_at DESC) WHERE NOT resolved;

-- Trusted Devices Indexes
CREATE INDEX IF NOT EXISTS idx_trusted_devices_user ON public.trusted_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_trusted_devices_last_used ON public.trusted_devices(last_used DESC);
CREATE INDEX IF NOT EXISTS idx_trusted_devices_trusted ON public.trusted_devices(is_trusted);

-- User Sessions Indexes (with column existence check)
DO $$
BEGIN
    -- Create indexes only if columns exist
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_sessions' AND column_name = 'user_id') THEN
        CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active ON public.user_sessions(user_id, is_active);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_sessions' AND column_name = 'session_token') THEN
        CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_sessions' AND column_name = 'device_id') THEN
        CREATE INDEX IF NOT EXISTS idx_user_sessions_device ON public.user_sessions(device_id);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'user_sessions' AND column_name = 'last_activity') THEN
        CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON public.user_sessions(last_activity DESC);
    END IF;
END $$;

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.transaction_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fraud_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.risk_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fraud_analysis_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trusted_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Transaction Limits Policies
CREATE POLICY "Users can view their own transaction limits" ON public.transaction_limits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own transaction limits" ON public.transaction_limits
    FOR UPDATE USING (auth.uid() = user_id);

-- Fraud Rules Policies (Admin only for modifications)
CREATE POLICY "Users can view active fraud rules" ON public.fraud_rules
    FOR SELECT USING (is_active = true);

-- Risk Scores Policies
CREATE POLICY "Users can view their own risk scores" ON public.risk_scores
    FOR SELECT USING (auth.uid() = user_id);

-- Fraud Analysis Logs Policies
CREATE POLICY "Users can view their own fraud analysis logs" ON public.fraud_analysis_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Security Events Policies
CREATE POLICY "Users can view their own security events" ON public.security_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert security events" ON public.security_events
    FOR INSERT WITH CHECK (true);

-- Trusted Devices Policies
CREATE POLICY "Users can manage their own trusted devices" ON public.trusted_devices
    FOR ALL USING (auth.uid() = user_id);

-- User Sessions Policies
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON public.user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- FUNCTIONS FOR FRAUD DETECTION
-- =====================================================

-- Function to get user's current transaction limits
CREATE OR REPLACE FUNCTION get_user_transaction_limits(p_user_id UUID)
RETURNS TABLE (
    verification_level TEXT,
    daily_limit DECIMAL,
    weekly_limit DECIMAL,
    monthly_limit DECIMAL,
    single_transaction_limit DECIMAL,
    currency TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tl.verification_level::TEXT,
        tl.daily_limit,
        tl.weekly_limit,
        tl.monthly_limit,
        tl.single_transaction_limit,
        tl.currency::TEXT
    FROM public.transaction_limits tl
    WHERE tl.user_id = p_user_id AND tl.is_active = true
    LIMIT 1;
    
    -- If no limits found, return default basic limits
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            'basic'::TEXT,
            500000::DECIMAL,
            2000000::DECIMAL,
            8000000::DECIMAL,
            100000::DECIMAL,
            'UGX'::TEXT;
    END IF;
END;
$$;

-- Function to check if transaction exceeds velocity limits
CREATE OR REPLACE FUNCTION check_transaction_velocity(
    p_user_id UUID,
    p_time_window_minutes INTEGER DEFAULT 5,
    p_max_transactions INTEGER DEFAULT 10
)
RETURNS TABLE (
    exceeds_limit BOOLEAN,
    transaction_count INTEGER,
    time_window INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
    v_window_start TIMESTAMP WITH TIME ZONE;
BEGIN
    v_window_start := NOW() - (p_time_window_minutes || ' minutes')::INTERVAL;
    
    SELECT COUNT(*) INTO v_count
    FROM public.transactions
    WHERE user_id = p_user_id
    AND created_at >= v_window_start
    AND status IN ('pending', 'completed');
    
    RETURN QUERY
    SELECT 
        (v_count >= p_max_transactions)::BOOLEAN,
        v_count,
        p_time_window_minutes;
END;
$$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.transaction_limits TO authenticated;
GRANT SELECT ON public.fraud_rules TO authenticated;
GRANT SELECT, INSERT ON public.risk_scores TO authenticated;
GRANT SELECT, INSERT ON public.fraud_analysis_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.security_events TO authenticated;
GRANT ALL ON public.trusted_devices TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_sessions TO authenticated;

GRANT EXECUTE ON FUNCTION get_user_transaction_limits TO authenticated;
GRANT EXECUTE ON FUNCTION check_transaction_velocity TO authenticated;
