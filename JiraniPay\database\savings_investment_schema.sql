-- =====================================================
-- SAVINGS & INVESTMENT DATABASE SCHEMA
-- Comprehensive schema for savings accounts, investments,
-- and financial planning with proper relationships and security
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- =====================================================
-- SAVINGS ACCOUNTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS savings_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Account Details
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL DEFAULT 'general' CHECK (account_type IN ('general', 'goal', 'emergency', 'vacation', 'education', 'retirement')),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    
    -- Balance Information
    current_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00 CHECK (current_balance >= 0),
    available_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00 CHECK (available_balance >= 0),
    
    -- Goal Settings (for goal-based savings)
    target_amount DECIMAL(15,2),
    target_date DATE,
    monthly_target DECIMAL(15,2),
    
    -- Interest Configuration
    interest_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0000 CHECK (interest_rate >= 0),
    interest_calculation_method VARCHAR(20) NOT NULL DEFAULT 'daily' CHECK (interest_calculation_method IN ('daily', 'monthly', 'quarterly', 'annually')),
    last_interest_calculation TIMESTAMP WITH TIME ZONE,
    total_interest_earned DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- Auto Transfer Settings
    auto_transfer_enabled BOOLEAN NOT NULL DEFAULT false,
    auto_transfer_amount DECIMAL(15,2),
    auto_transfer_frequency VARCHAR(20) CHECK (auto_transfer_frequency IN ('daily', 'weekly', 'monthly')),
    auto_transfer_source_account VARCHAR(50), -- 'wallet' or other account ID
    next_auto_transfer TIMESTAMP WITH TIME ZONE,
    
    -- Account Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_locked BOOLEAN NOT NULL DEFAULT false,
    lock_reason VARCHAR(255),
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- SAVINGS TRANSACTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS savings_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    savings_account_id UUID NOT NULL REFERENCES savings_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Transaction Details
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal', 'interest', 'transfer', 'fee', 'bonus')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount != 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    
    -- Balance Information
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    
    -- Transaction References
    reference_number VARCHAR(100) UNIQUE NOT NULL,
    external_reference VARCHAR(100),
    source_account VARCHAR(100), -- For transfers
    destination_account VARCHAR(100), -- For transfers
    
    -- Status and Processing
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Description and Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- INVESTMENT PORTFOLIOS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS investment_portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Portfolio Details
    portfolio_name VARCHAR(255) NOT NULL,
    portfolio_type VARCHAR(50) NOT NULL DEFAULT 'general' CHECK (portfolio_type IN ('general', 'retirement', 'education', 'aggressive', 'conservative', 'balanced')),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    
    -- Portfolio Values
    total_value DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_invested DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_gains_losses DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    cash_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- Performance Metrics
    daily_return DECIMAL(10,6) DEFAULT 0.000000,
    total_return DECIMAL(10,6) DEFAULT 0.000000,
    annualized_return DECIMAL(10,6) DEFAULT 0.000000,
    
    -- Risk Assessment
    risk_level VARCHAR(20) DEFAULT 'moderate' CHECK (risk_level IN ('conservative', 'moderate', 'aggressive')),
    risk_score DECIMAL(5,2) DEFAULT 5.00 CHECK (risk_score BETWEEN 1.00 AND 10.00),
    
    -- Portfolio Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_managed BOOLEAN NOT NULL DEFAULT false, -- Professional management
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- INVESTMENT ASSETS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS investment_assets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Asset Identification
    symbol VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    asset_type VARCHAR(50) NOT NULL CHECK (asset_type IN ('stock', 'bond', 'etf', 'mutual_fund', 'crypto', 'commodity', 'real_estate')),
    exchange VARCHAR(50),
    
    -- Asset Details
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    sector VARCHAR(100),
    industry VARCHAR(100),
    country VARCHAR(3),
    
    -- Current Market Data
    current_price DECIMAL(15,6) NOT NULL DEFAULT 0.000000,
    previous_close DECIMAL(15,6),
    day_change DECIMAL(15,6) DEFAULT 0.000000,
    day_change_percent DECIMAL(10,6) DEFAULT 0.000000,
    
    -- Trading Information
    is_tradeable BOOLEAN NOT NULL DEFAULT true,
    min_order_size DECIMAL(15,6) DEFAULT 1.000000,
    trading_hours JSONB DEFAULT '{}',
    
    -- Risk Metrics
    volatility DECIMAL(10,6),
    beta DECIMAL(10,6),
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- PORTFOLIO HOLDINGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS portfolio_holdings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES investment_portfolios(id) ON DELETE CASCADE,
    asset_id UUID NOT NULL REFERENCES investment_assets(id) ON DELETE RESTRICT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Holding Details
    quantity DECIMAL(15,6) NOT NULL DEFAULT 0.000000 CHECK (quantity >= 0),
    average_cost DECIMAL(15,6) NOT NULL DEFAULT 0.000000,
    total_cost DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    current_value DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- Performance Metrics
    unrealized_gain_loss DECIMAL(15,2) DEFAULT 0.00,
    unrealized_gain_loss_percent DECIMAL(10,6) DEFAULT 0.000000,
    realized_gain_loss DECIMAL(15,2) DEFAULT 0.00,
    
    -- Allocation
    portfolio_weight DECIMAL(10,6) DEFAULT 0.000000 CHECK (portfolio_weight BETWEEN 0.000000 AND 100.000000),
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Timestamps
    first_purchase_date TIMESTAMP WITH TIME ZONE,
    last_transaction_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Unique constraint
    UNIQUE(portfolio_id, asset_id)
);

-- =====================================================
-- INVESTMENT TRANSACTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS investment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID NOT NULL REFERENCES investment_portfolios(id) ON DELETE CASCADE,
    asset_id UUID NOT NULL REFERENCES investment_assets(id) ON DELETE RESTRICT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Transaction Details
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('buy', 'sell', 'dividend', 'split', 'merger', 'fee')),
    quantity DECIMAL(15,6) NOT NULL,
    price DECIMAL(15,6) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    fees DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    net_amount DECIMAL(15,2) NOT NULL,
    
    -- Order Information
    order_type VARCHAR(20) DEFAULT 'market' CHECK (order_type IN ('market', 'limit', 'stop', 'stop_limit')),
    order_id VARCHAR(100),
    execution_price DECIMAL(15,6),
    
    -- Status and Processing
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'executed', 'cancelled', 'failed')),
    executed_at TIMESTAMP WITH TIME ZONE,
    
    -- References
    reference_number VARCHAR(100) UNIQUE NOT NULL,
    external_reference VARCHAR(100),
    
    -- Metadata
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- FINANCIAL GOALS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS financial_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Goal Details
    goal_name VARCHAR(255) NOT NULL,
    goal_type VARCHAR(50) NOT NULL CHECK (goal_type IN ('savings', 'investment', 'debt_payoff', 'retirement', 'emergency_fund', 'vacation', 'education', 'home_purchase')),
    description TEXT,
    
    -- Target Information
    target_amount DECIMAL(15,2) NOT NULL CHECK (target_amount > 0),
    current_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 CHECK (current_amount >= 0),
    target_date DATE,
    
    -- Progress Tracking
    monthly_contribution DECIMAL(15,2),
    recommended_monthly DECIMAL(15,2),
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 CHECK (progress_percentage BETWEEN 0.00 AND 100.00),
    
    -- Associated Accounts
    linked_savings_account UUID REFERENCES savings_accounts(id),
    linked_investment_portfolio UUID REFERENCES investment_portfolios(id),
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_achieved BOOLEAN NOT NULL DEFAULT false,
    achieved_date DATE,
    
    -- Metadata
    priority_level INTEGER DEFAULT 3 CHECK (priority_level BETWEEN 1 AND 5),
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- BUDGETS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Budget Details
    budget_name VARCHAR(255) NOT NULL,
    budget_period VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (budget_period IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    
    -- Budget Amounts
    total_income DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_expenses DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_savings DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_investments DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- Period Information
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_template BOOLEAN NOT NULL DEFAULT false,
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- BUDGET CATEGORIES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS budget_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_id UUID NOT NULL REFERENCES budgets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Category Details
    category_name VARCHAR(255) NOT NULL,
    category_type VARCHAR(20) NOT NULL CHECK (category_type IN ('income', 'expense', 'savings', 'investment')),
    subcategory VARCHAR(255),
    
    -- Budget Amounts
    budgeted_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    actual_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    variance DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- Tracking
    is_fixed BOOLEAN NOT NULL DEFAULT false,
    is_essential BOOLEAN NOT NULL DEFAULT true,
    
    -- Metadata
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Savings Accounts Indexes
CREATE INDEX IF NOT EXISTS idx_savings_accounts_user_id ON savings_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_savings_accounts_type ON savings_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_savings_accounts_active ON savings_accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_savings_auto_transfer ON savings_accounts(auto_transfer_enabled, next_auto_transfer) WHERE auto_transfer_enabled = true;

-- Savings Transactions Indexes
CREATE INDEX IF NOT EXISTS idx_savings_transactions_account ON savings_transactions(savings_account_id);
CREATE INDEX IF NOT EXISTS idx_savings_transactions_user ON savings_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_savings_transactions_type ON savings_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_savings_transactions_status ON savings_transactions(status);
CREATE INDEX IF NOT EXISTS idx_savings_transactions_date ON savings_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_savings_transactions_reference ON savings_transactions(reference_number);

-- Investment Portfolios Indexes
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON investment_portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolios_type ON investment_portfolios(portfolio_type);
CREATE INDEX IF NOT EXISTS idx_portfolios_active ON investment_portfolios(is_active);

-- Investment Assets Indexes
CREATE INDEX IF NOT EXISTS idx_assets_symbol ON investment_assets(symbol);
CREATE INDEX IF NOT EXISTS idx_assets_type ON investment_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assets_tradeable ON investment_assets(is_tradeable);
CREATE INDEX IF NOT EXISTS idx_assets_updated ON investment_assets(last_updated);

-- Portfolio Holdings Indexes
CREATE INDEX IF NOT EXISTS idx_holdings_portfolio ON portfolio_holdings(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_holdings_asset ON portfolio_holdings(asset_id);
CREATE INDEX IF NOT EXISTS idx_holdings_user ON portfolio_holdings(user_id);
CREATE INDEX IF NOT EXISTS idx_holdings_active ON portfolio_holdings(is_active);

-- Investment Transactions Indexes
CREATE INDEX IF NOT EXISTS idx_investment_transactions_portfolio ON investment_transactions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_investment_transactions_asset ON investment_transactions(asset_id);
CREATE INDEX IF NOT EXISTS idx_investment_transactions_user ON investment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_investment_transactions_type ON investment_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_investment_transactions_status ON investment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_investment_transactions_date ON investment_transactions(created_at);

-- Financial Goals Indexes
CREATE INDEX IF NOT EXISTS idx_goals_user_id ON financial_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_goals_type ON financial_goals(goal_type);
CREATE INDEX IF NOT EXISTS idx_goals_active ON financial_goals(is_active);
CREATE INDEX IF NOT EXISTS idx_goals_target_date ON financial_goals(target_date);

-- Budgets Indexes
CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(budget_period);
CREATE INDEX IF NOT EXISTS idx_budgets_active ON budgets(is_active);
CREATE INDEX IF NOT EXISTS idx_budgets_dates ON budgets(start_date, end_date);

-- Budget Categories Indexes
CREATE INDEX IF NOT EXISTS idx_budget_categories_budget ON budget_categories(budget_id);
CREATE INDEX IF NOT EXISTS idx_budget_categories_user ON budget_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_budget_categories_type ON budget_categories(category_type);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE savings_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE savings_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_holdings ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_categories ENABLE ROW LEVEL SECURITY;

-- Savings Accounts Policies
CREATE POLICY "Users can view their own savings accounts" ON savings_accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own savings accounts" ON savings_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own savings accounts" ON savings_accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own savings accounts" ON savings_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- Savings Transactions Policies
CREATE POLICY "Users can view their own savings transactions" ON savings_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create savings transactions" ON savings_transactions
    FOR INSERT WITH CHECK (true); -- System service creates these

CREATE POLICY "System can update savings transactions" ON savings_transactions
    FOR UPDATE USING (true); -- System service updates these

-- Investment Portfolios Policies
CREATE POLICY "Users can view their own portfolios" ON investment_portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own portfolios" ON investment_portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own portfolios" ON investment_portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own portfolios" ON investment_portfolios
    FOR DELETE USING (auth.uid() = user_id);

-- Investment Assets Policies (Public read access for market data)
CREATE POLICY "Anyone can view investment assets" ON investment_assets
    FOR SELECT USING (true);

CREATE POLICY "System can manage investment assets" ON investment_assets
    FOR ALL USING (true); -- System service manages these

-- Portfolio Holdings Policies
CREATE POLICY "Users can view their own holdings" ON portfolio_holdings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage holdings" ON portfolio_holdings
    FOR ALL USING (true); -- System service manages these

-- Investment Transactions Policies
CREATE POLICY "Users can view their own investment transactions" ON investment_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create investment transactions" ON investment_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "System can update investment transactions" ON investment_transactions
    FOR UPDATE USING (true); -- System service updates these

-- Financial Goals Policies
CREATE POLICY "Users can view their own goals" ON financial_goals
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own goals" ON financial_goals
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals" ON financial_goals
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals" ON financial_goals
    FOR DELETE USING (auth.uid() = user_id);

-- Budgets Policies
CREATE POLICY "Users can view their own budgets" ON budgets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own budgets" ON budgets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own budgets" ON budgets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own budgets" ON budgets
    FOR DELETE USING (auth.uid() = user_id);

-- Budget Categories Policies
CREATE POLICY "Users can view their own budget categories" ON budget_categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own budget categories" ON budget_categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own budget categories" ON budget_categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own budget categories" ON budget_categories
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_savings_accounts_updated_at
    BEFORE UPDATE ON savings_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_savings_transactions_updated_at
    BEFORE UPDATE ON savings_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolios_updated_at
    BEFORE UPDATE ON investment_portfolios
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_holdings_updated_at
    BEFORE UPDATE ON portfolio_holdings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_investment_transactions_updated_at
    BEFORE UPDATE ON investment_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_goals_updated_at
    BEFORE UPDATE ON financial_goals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budgets_updated_at
    BEFORE UPDATE ON budgets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budget_categories_updated_at
    BEFORE UPDATE ON budget_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STORED PROCEDURES AND FUNCTIONS
-- =====================================================

-- Function to calculate compound interest
CREATE OR REPLACE FUNCTION calculate_compound_interest(
    p_principal DECIMAL,
    p_rate DECIMAL,
    p_periods INTEGER,
    p_compound_frequency INTEGER DEFAULT 365
)
RETURNS DECIMAL AS $$
BEGIN
    RETURN p_principal * POWER(1 + (p_rate / p_compound_frequency), p_compound_frequency * p_periods);
END;
$$ LANGUAGE plpgsql;

-- Function to update savings account balance
CREATE OR REPLACE FUNCTION update_savings_balance(
    p_account_id UUID,
    p_amount DECIMAL,
    p_transaction_type VARCHAR
)
RETURNS VOID AS $$
DECLARE
    current_balance DECIMAL;
BEGIN
    -- Get current balance
    SELECT current_balance INTO current_balance
    FROM savings_accounts
    WHERE id = p_account_id;

    -- Update balance based on transaction type
    IF p_transaction_type IN ('deposit', 'interest', 'bonus') THEN
        UPDATE savings_accounts
        SET current_balance = current_balance + p_amount,
            available_balance = available_balance + p_amount,
            updated_at = NOW()
        WHERE id = p_account_id;
    ELSIF p_transaction_type IN ('withdrawal', 'fee') THEN
        UPDATE savings_accounts
        SET current_balance = current_balance - p_amount,
            available_balance = available_balance - p_amount,
            updated_at = NOW()
        WHERE id = p_account_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate portfolio performance
CREATE OR REPLACE FUNCTION calculate_portfolio_performance(p_portfolio_id UUID)
RETURNS TABLE (
    total_value DECIMAL,
    total_invested DECIMAL,
    total_return DECIMAL,
    return_percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(SUM(ph.current_value), 0) as total_value,
        COALESCE(SUM(ph.total_cost), 0) as total_invested,
        COALESCE(SUM(ph.current_value - ph.total_cost), 0) as total_return,
        CASE
            WHEN COALESCE(SUM(ph.total_cost), 0) > 0
            THEN (COALESCE(SUM(ph.current_value - ph.total_cost), 0) / SUM(ph.total_cost)) * 100
            ELSE 0
        END as return_percentage
    FROM portfolio_holdings ph
    WHERE ph.portfolio_id = p_portfolio_id
        AND ph.is_active = true
        AND ph.quantity > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VIEWS FOR REPORTING AND ANALYTICS
-- =====================================================

-- Savings account summary view
CREATE OR REPLACE VIEW savings_summary AS
SELECT
    sa.user_id,
    COUNT(*) as total_accounts,
    SUM(sa.current_balance) as total_savings,
    SUM(sa.total_interest_earned) as total_interest,
    AVG(sa.interest_rate) as average_interest_rate,
    COUNT(*) FILTER (WHERE sa.account_type = 'goal') as goal_accounts,
    COUNT(*) FILTER (WHERE sa.auto_transfer_enabled = true) as auto_transfer_accounts
FROM savings_accounts sa
WHERE sa.is_active = true
GROUP BY sa.user_id;

-- Investment portfolio summary view
CREATE OR REPLACE VIEW portfolio_summary AS
SELECT
    ip.user_id,
    COUNT(*) as total_portfolios,
    SUM(ip.total_value) as total_portfolio_value,
    SUM(ip.total_invested) as total_invested,
    SUM(ip.total_gains_losses) as total_gains_losses,
    AVG(ip.total_return) as average_return,
    COUNT(DISTINCT ph.asset_id) as unique_assets
FROM investment_portfolios ip
LEFT JOIN portfolio_holdings ph ON ip.id = ph.portfolio_id AND ph.is_active = true
WHERE ip.is_active = true
GROUP BY ip.user_id;

-- Financial goals progress view
CREATE OR REPLACE VIEW goals_progress AS
SELECT
    fg.user_id,
    fg.goal_type,
    COUNT(*) as total_goals,
    COUNT(*) FILTER (WHERE fg.is_achieved = true) as achieved_goals,
    SUM(fg.target_amount) as total_target,
    SUM(fg.current_amount) as total_current,
    AVG(fg.progress_percentage) as average_progress
FROM financial_goals fg
WHERE fg.is_active = true
GROUP BY fg.user_id, fg.goal_type;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON savings_accounts TO authenticated;
GRANT SELECT ON savings_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON investment_portfolios TO authenticated;
GRANT SELECT ON investment_assets TO authenticated;
GRANT SELECT ON portfolio_holdings TO authenticated;
GRANT SELECT, INSERT ON investment_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON financial_goals TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON budgets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON budget_categories TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant access to views
GRANT SELECT ON savings_summary TO authenticated;
GRANT SELECT ON portfolio_summary TO authenticated;
GRANT SELECT ON goals_progress TO authenticated;

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE savings_accounts IS 'User savings accounts with goal tracking and auto-transfer capabilities';
COMMENT ON TABLE savings_transactions IS 'All transactions for savings accounts including deposits, withdrawals, and interest';
COMMENT ON TABLE investment_portfolios IS 'User investment portfolios with performance tracking';
COMMENT ON TABLE investment_assets IS 'Available investment assets with market data';
COMMENT ON TABLE portfolio_holdings IS 'User holdings within investment portfolios';
COMMENT ON TABLE investment_transactions IS 'Investment buy/sell transactions and order history';
COMMENT ON TABLE financial_goals IS 'User financial goals with progress tracking';
COMMENT ON TABLE budgets IS 'User budgets for financial planning';
COMMENT ON TABLE budget_categories IS 'Budget categories for income, expenses, savings, and investments';

-- =====================================================
-- END OF SCHEMA
-- =====================================================
