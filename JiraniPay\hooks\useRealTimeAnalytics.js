/**
 * Real-time Analytics Hook
 * Provides real-time analytics data with WebSocket/SSE integration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { AppState } from 'react-native';
import enhancedAnalyticsService from '../services/enhancedAnalyticsService';
import authService from '../services/authService';

export const useRealTimeAnalytics = (period = 'month', autoRefresh = true) => {
  const [user, setUser] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  
  const unsubscribeRef = useRef(null);
  const refreshIntervalRef = useRef(null);
  const appStateRef = useRef(AppState.currentState);

  /**
   * Load analytics data
   */
  const loadAnalytics = useCallback(async (showLoading = true) => {
    if (!user?.id) return;

    try {
      if (showLoading) setLoading(true);
      setError(null);

      console.log('📊 Loading analytics data for period:', period);
      const result = await enhancedAnalyticsService.getDashboardAnalytics(user.id, period);

      if (result.success) {
        setAnalytics(result.data);
        setLastUpdated(new Date());
        console.log('✅ Analytics data loaded successfully');
      } else {
        throw new Error(result.error || 'Failed to load analytics');
      }
    } catch (err) {
      console.error('❌ Error loading analytics:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user?.id, period]);

  /**
   * Handle real-time updates
   */
  const handleRealTimeUpdate = useCallback((update) => {
    console.log('📊 Real-time analytics update received:', update.type);
    
    // Refresh analytics data when updates are received
    loadAnalytics(false);
  }, [loadAnalytics]);

  /**
   * Initialize real-time connection
   */
  const initializeRealTime = useCallback(async () => {
    if (!user?.id || !autoRefresh) return;

    try {
      console.log('📊 Initializing real-time analytics connection');

      // Cleanup any existing connections first
      await cleanupRealTime();

      // Initialize real-time service
      const result = await enhancedAnalyticsService.initializeRealTime(user.id);

      if (result.success) {
        // Subscribe to updates only if not already subscribed
        if (!unsubscribeRef.current) {
          unsubscribeRef.current = enhancedAnalyticsService.subscribe(handleRealTimeUpdate);
          setIsConnected(true);
          console.log('✅ Real-time analytics connection established');
        }
      } else {
        console.warn('⚠️ Failed to initialize real-time analytics:', result.error);
        setIsConnected(false);
      }
    } catch (err) {
      console.error('❌ Error initializing real-time analytics:', err);
      setIsConnected(false);
    }
  }, [user?.id, autoRefresh, handleRealTimeUpdate, cleanupRealTime]);

  /**
   * Cleanup real-time connection
   */
  const cleanupRealTime = useCallback(async () => {
    try {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }

      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }

      // Cleanup the service
      await enhancedAnalyticsService.cleanup();

      setIsConnected(false);
      console.log('📊 Real-time analytics connection cleaned up');
    } catch (error) {
      console.error('❌ Error during real-time cleanup:', error);
    }
  }, []);

  /**
   * Handle app state changes
   */
  const handleAppStateChange = useCallback((nextAppState) => {
    if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
      // App came to foreground, refresh data
      console.log('📊 App became active, refreshing analytics');
      loadAnalytics(false);
    }
    appStateRef.current = nextAppState;
  }, [loadAnalytics]);

  /**
   * Manual refresh
   */
  const refresh = useCallback(async () => {
    await loadAnalytics(true);
  }, [loadAnalytics]);

  /**
   * Get specific metric
   */
  const getMetric = useCallback((path) => {
    if (!analytics) return null;
    
    const keys = path.split('.');
    let value = analytics;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return null;
      }
    }
    
    return value;
  }, [analytics]);

  /**
   * Check if data is stale
   */
  const isDataStale = useCallback(() => {
    if (!lastUpdated) return true;
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    return Date.now() - lastUpdated.getTime() > staleThreshold;
  }, [lastUpdated]);

  /**
   * Initialize on mount
   */
  useEffect(() => {
    // Get current user
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, []);

  useEffect(() => {
    let mounted = true;

    const initializeAnalytics = async () => {
      if (user?.id && mounted) {
        await loadAnalytics();

        // Add a small delay to prevent rapid re-initialization
        setTimeout(async () => {
          if (mounted) {
            await initializeRealTime();
          }
        }, 100);
      }
    };

    initializeAnalytics();

    // Set up periodic refresh as fallback
    if (autoRefresh && user?.id) {
      refreshIntervalRef.current = setInterval(() => {
        if (isDataStale() && mounted) {
          console.log('📊 Data is stale, refreshing analytics');
          loadAnalytics(false);
        }
      }, 5 * 60 * 1000); // Check every 5 minutes
    }

    // Listen to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      mounted = false;
      cleanupRealTime();
      subscription?.remove();
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [user?.id, autoRefresh]); // Simplified dependencies

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      cleanupRealTime();
    };
  }, []);

  /**
   * Reload when period changes
   */
  useEffect(() => {
    if (user?.id) {
      loadAnalytics();
    }
  }, [period, loadAnalytics]);

  return {
    // Data
    analytics,
    loading,
    error,
    lastUpdated,
    isConnected,
    
    // Actions
    refresh,
    getMetric,
    
    // Status
    isDataStale: isDataStale(),
    
    // Computed values
    summary: analytics?.summary || {},
    wallet: analytics?.wallet || {},
    transactions: analytics?.transactions || {},
    savings: analytics?.savings || {},
    investments: analytics?.investments || {},
    spendingCategories: Array.isArray(analytics?.spendingCategories?.categories) ? analytics.spendingCategories.categories : [],
    monthlyTrends: Array.isArray(analytics?.monthlyTrends) ? analytics.monthlyTrends : [],
    insights: Array.isArray(analytics?.insights) ? analytics.insights : [],

    // Chart data helpers
    getSpendingTrendData: () => Array.isArray(analytics?.monthlyTrends) ? analytics.monthlyTrends : [],
    getCategoryData: () => Array.isArray(analytics?.spendingCategories?.categories) ? analytics.spendingCategories.categories : [],
    getSavingsProgressData: () => analytics?.savings?.goalProgress || { goals: [] },
    getInvestmentPerformanceData: () => analytics?.investments?.performanceMetrics || [],
  };
};

/**
 * Hook for specific analytics metrics
 */
export const useAnalyticsMetric = (metricPath, period = 'month') => {
  const { getMetric, loading, error, refresh } = useRealTimeAnalytics(period);
  
  const value = getMetric(metricPath);
  
  return {
    value,
    loading,
    error,
    refresh
  };
};

/**
 * Hook for analytics comparison between periods
 */
export const useAnalyticsComparison = (metricPath, currentPeriod = 'month', previousPeriod = 'month') => {
  const current = useAnalyticsMetric(metricPath, currentPeriod);
  const previous = useAnalyticsMetric(metricPath, previousPeriod);
  
  const change = current.value && previous.value 
    ? current.value - previous.value 
    : 0;
    
  const changePercent = previous.value && previous.value !== 0
    ? (change / previous.value) * 100
    : 0;
  
  return {
    current: current.value,
    previous: previous.value,
    change,
    changePercent,
    loading: current.loading || previous.loading,
    error: current.error || previous.error,
    refresh: () => {
      current.refresh();
      previous.refresh();
    }
  };
};
