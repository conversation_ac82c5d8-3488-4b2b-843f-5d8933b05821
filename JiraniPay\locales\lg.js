/**
 * Luganda (lg) translations for JiraniPay
 * Complete translation coverage for all app features
 */

export default {
  // Common UI elements
  common: {
    continue: 'Genda mu maaso',
    cancel: '<PERSON><PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON>o',
    next: '<PERSON><PERSON><PERSON>',
    done: '<PERSON><PERSON><PERSON>',
    loading: '<PERSON><PERSON><PERSON><PERSON><PERSON>...',
    error: '<PERSON><PERSON><PERSON>',
    success: '<PERSON><PERSON><PERSON><PERSON>',
    retry: '<PERSON><PERSON>u ogezaa<PERSON>',
    close: '<PERSON><PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    edit: '<PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON>',
    confirm: '<PERSON><PERSON>a',
    yes: 'Ye',
    no: 'Ned<PERSON>',
    ok: 'Kale',

    // Financial features
    savings: 'Okutereka'
  },

  // Authentication & Onboarding
  auth: {
    welcomeBack: 'Tukulamusizza nate',
    goodMorning: 'Wasuze otya',
    goodAfternoon: 'Osiibye otya',
    goodEvening: 'Osuubidde otya',
    chooseLoginMethod: 'Londa engeri gy\'oyagala okuyingiramu',
    otpLogin: '<PERSON>ira nga okozesa OTP',
    passwordLogin: '<PERSON>ira nga okozesa ekigambo ky\'obukuumi',
    enterPhoneNumber: 'Teeka ennamba y\'essimu',
    enterPassword: 'Teeka ekigambo ky\'obukuumi',
    forgotPassword: 'Weerabidde ekigambo ky\'obukuumi?',
    sendOTP: 'Wereza OTP',
    login: 'Yingira',
    verifyOTP: 'Kakasa OTP',
    resendOTP: 'Ddamu owereze OTP',
    resendOTPIn: 'Ddamu owereze OTP mu',
    dontHaveAccount: 'Tolina akawunti?',
    signUp: 'Weekwandiise',
    useBiometric: 'Kozesa okuyingira kwa biometric'
  },

  // Time-based greetings with names
  greetings: {
    goodMorningName: 'Wasuze otya, {name}',
    goodAfternoonName: 'Osiibye otya, {name}',
    goodEveningName: 'Osuubidde otya, {name}',
    goodMorning: 'Wasuze otya',
    goodAfternoon: 'Osiibye otya',
    goodEvening: 'Osuubidde otya'
  },

  // Dashboard & Home
  dashboard: {
    title: 'Olukalala',
    welcome: 'Tukulamusizza',
    balance: 'Ensigalira',
    totalBalance: 'Ensigalira yonna',
    availableBalance: 'Ensigalira eriwo',
    quickActions: 'Ebikolwa eby\'amangu',
    recentTransactions: 'Ebikolwa bya kaakano',
    viewAll: 'Laba byonna',
    noTransactions: 'Tewali bikolwa',
    sendMoney: 'Wereza ssente',
    payBills: 'Sasula ebisale',
    topUp: 'Jjuza',
    scanQR: 'Kebera QR',
    savings: 'Okutereka',
    analytics: 'Okwekenneenya'
  },

  // Wallet & Transactions
  wallet: {
    title: 'Ensawo',
    myWallet: 'Ensawo yange',
    balance: 'Ensigalira',
    transactions: 'Ebikolwa',
    transactionHistory: 'Ebyafaayo mu bikolwa',
    sendMoney: 'Wereza ssente',
    receiveMoney: 'Funa ssente',
    topUp: 'Jjuza ensawo',
    withdraw: 'Ggya',
    transfer: 'Kyusa',
    deposit: 'Teeka',
    payment: 'Okusasula',
    refund: 'Okuddiza',
    fee: 'Omusolo',
    total: 'Awamu',
    amount: 'Omuwendo',
    recipient: 'Afuna',
    sender: 'Aweereza',
    reference: 'Okweyoleka',
    description: 'Okunnyonnyola',
    date: 'Olunaku',
    time: 'Obudde',
    status: 'Embeera',
    type: 'Ekika',
    category: 'Ekibiina',
    provider: 'Awa obuweereza',
    account: 'Akawunti',
    accountNumber: 'Ennamba y\'akawunti',
    phoneNumber: 'Ennamba y\'essimu',
    transactionId: 'Ennamba y\'ekintu',
    receiptNumber: 'Ennamba y\'ekiwandiiko',
    confirmTransaction: 'Kakasa ekintu',
    transactionSuccessful: 'Ekintu kiwangaazi',
    transactionFailed: 'Ekintu kiremeddwa',
    insufficientFunds: 'Ssente tezimala',
    dailyLimitExceeded: 'Ekipimo kya buli lunaku kiyisiddwa',
    monthlyLimitExceeded: 'Ekipimo kya buli mwezi kiyisiddwa',
    invalidAmount: 'Omuwendo si mutuufu',
    minimumAmount: 'Omuwendo omutono',
    maximumAmount: 'Omuwendo omunene',
    transactionLimits: 'Ebipimo by\'ebikolwa',
    dailyLimit: 'Ekipimo kya buli lunaku',
    monthlyLimit: 'Ekipimo kya buli mwezi',
    remainingDaily: 'Ekisigadde kya buli lunaku',
    remainingMonthly: 'Ekisigadde kya buli mwezi'
  },

  // Currency & Formatting
  currency: {
    ugx: 'Silingi ya Uganda',
    kes: 'Silingi ya Kenya',
    tzs: 'Silingi ya Tanzania',
    rwf: 'Faranga ya Rwanda',
    bif: 'Faranga ya Burundi',
    etb: 'Birr ya Ethiopia',
    usd: 'Doola ya America',
    eur: 'Euro',
    gbp: 'Pawundi ya Bungereza',
    selectCurrency: 'Londa ensimbi',
    preferredCurrency: 'Ensimbi z\'oyagala',
    currencySettings: 'Enteekateeka z\'ensimbi',
    exchangeRate: 'Omuwendo gw\'okuwaanyisa',
    convertedAmount: 'Omuwendo ogukyusiddwa',
    conversionRate: 'Omuwendo gw\'okukyusa',
    lastUpdated: 'Ekyakyusiddwa mu nkomerero',
    updateRates: 'Kyusa emiwendo',
    rateUnavailable: 'Omuwendo teguli',
    conversionError: 'Ensobi mu kukyusa',
    currencyUpdated: 'Ensimbi zikyusiddwa okudda ku {currency}',
    updateError: 'Okukyusa ensimbi tekuwangaazi. Ddamu ogezaako.'
  },

  // Settings
  settings: {
    languageAndCurrency: 'Olulimi n\'Ensimbi',
    language: 'Olulimi',
    languageDescription: 'Londa olulimi lw\'oyagala mu pulogulaamu',
    languageInfo: 'Pulogulaamu ejja kutandika buggya okukozesa enkyukakyuka z\'olulimi',
    languageUpdated: 'Olulimi lukyusiddwa obulungi',
    languageUpdateError: 'Okukyusa olulimi tekuwangaazi. Ddamu ogezaako.',
    currencyDescription: 'Londa ensimbi z\'oyagala mu bikolwa',
    currencyInfo: 'Emiwendo gyonna gijja kulabibwa mu nsimbi z\'oyagala n\'okukyusakyusa okw\'amangu'
  }
};
