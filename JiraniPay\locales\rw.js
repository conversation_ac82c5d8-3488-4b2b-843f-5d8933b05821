/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> (rw) translations for JiraniPay
 * Complete translation coverage for all app features
 */

export default {
  // Common UI elements
  common: {
    continue: '<PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON> inyuma',
    next: '<PERSON><PERSON><PERSON><PERSON>',
    done: 'Byaran<PERSON>ye',
    loading: '<PERSON><PERSON><PERSON><PERSON>y<PERSON>...',
    error: '<PERSON><PERSON><PERSON>',
    success: 'Byagenze neza',
    retry: 'Ongera ugerageze',
    close: '<PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    edit: 'Hindura',
    delete: '<PERSON><PERSON>',
    confirm: 'Emeza',
    yes: '<PERSON><PERSON>',
    no: 'Oya',
    ok: 'Ni byiza',
    notAvailable: 'N<PERSON>boneka',

    // Financial features
    savings: 'Kubi<PERSON>'
  },

  // Authentication & Onboarding
  auth: {
    welcomeBack: 'Murakaza neza',
    goodMorning: '<PERSON><PERSON><PERSON>tse',
    goodAfternoon: '<PERSON><PERSON><PERSON><PERSON>',
    goodEvening: 'Mu<PERSON>uke',
    chooseLoginMethod: 'Hitamo uburyo bwo kwinjira',
    otpLogin: 'Kwinjira ukoresheje OTP',
    passwordLogin: 'Kwinjira ukoresheje ijambo ry\'ibanga',
    enterPhoneNumber: '<PERSON><PERSON><PERSON> nimero ya telefoni',
    enterPassword: 'Shyira ijambo ry\'ibanga',
    forgotPassword: 'Wibagiwe ijambo ry\'ibanga?',
    sendOTP: 'Kohereza OTP',
    login: 'Injira',
    verifyOTP: 'Emeza OTP',
    resendOTP: 'Ongera ukohereze OTP',
    resendOTPIn: 'Ongera ukohereze OTP mu',
    dontHaveAccount: 'Nta konti ufite?',
    signUp: 'Iyandikishe',
    useBiometric: 'Koresha kwinjira kwa biometric'
  },

  // Time-based greetings with names
  greetings: {
    goodMorningName: 'Mwaramutse, {name}',
    goodAfternoonName: 'Mwiriwe, {name}',
    goodEveningName: 'Muramuke, {name}',
    goodMorning: 'Mwaramutse',
    goodAfternoon: 'Mwiriwe',
    goodEvening: 'Muramuke'
  },

  // Dashboard & Home
  dashboard: {
    title: 'Ikibaho',
    welcome: 'Murakaza neza kuri',
    balance: 'Amafaranga asigaye',
    totalBalance: 'Amafaranga yose asigaye',
    availableBalance: 'Amafaranga aboneka',
    quickActions: 'Ibikorwa byihuse',
    recentTransactions: 'Ibikorwa bya vuba',
    viewAll: 'Reba byose',
    noTransactions: 'Nta bikorwa',
    transactionsWillAppear: 'Ibikorwa byawe bya vuba bizagaragara hano',
    sendMoney: 'Kohereza amafaranga',
    payBills: 'Kwishyura amadeni',
    topUp: 'Kuzuza',
    scanQR: 'Gusoma QR',
    savings: 'Kubika',
    analytics: 'Isesengura',
    topCategory: 'Icyiciro cya mbere',
    startUsing: 'Tangira gukoresha JiraniPay kugirango ubone amakuru',
    makeTransactions: 'Kora ibikorwa kugirango ufungure amakuru y\'amafaranga afashijwe na AI'
  },

  // Profile
  profile: {
    title: 'Umwirondoro',
    verifiedAccount: 'Konti yemejwe',
    pendingVerification: 'Itegereje kwemezwa',
    customerService: 'Vugana n\'itsinda ryacu ry\'ubunyangamugayo',
    editProfile: 'Hindura umwirondoro',
    accountSettings: 'Amagenamiterere y\'konti',
    securitySettings: 'Amagenamiterere y\'umutekano',
    privacySettings: 'Amagenamiterere y\'ibanga',
    notificationSettings: 'Amagenamiterere y\'ubutumwa',
    helpSupport: 'Ubufasha n\'inkunga',
    aboutApp: 'Ku bijyanye na porogaramu',
    signOut: 'Gusohoka'
  },

  // Wallet & Transactions
  wallet: {
    title: 'Igikoni',
    myWallet: 'Igikoni cyange',
    balance: 'Amafaranga asigaye',
    transactions: 'Ibikorwa',
    transactionHistory: 'Amateka y\'ibikorwa',
    sendMoney: 'Kohereza amafaranga',
    receiveMoney: 'Kwakira amafaranga',
    topUp: 'Kuzuza igikoni',
    withdraw: 'Gukura',
    transfer: 'Kwimura',
    deposit: 'Kubika',
    payment: 'Kwishyura',
    refund: 'Kugarura',
    fee: 'Ikiguzi',
    total: 'Igiteranyo',
    amount: 'Umubare',
    recipient: 'Uwakira',
    sender: 'Uwohereza',
    reference: 'Indango',
    description: 'Ibisobanuro',
    date: 'Itariki',
    time: 'Igihe',
    status: 'Uko bimeze',
    type: 'Ubwoko',
    category: 'Icyiciro',
    provider: 'Utanga serivisi',
    account: 'Konti',
    accountNumber: 'Nimero ya konti',
    phoneNumber: 'Nimero ya telefoni',
    transactionId: 'Indangamuntu y\'igikorwa',
    receiptNumber: 'Nimero y\'inyemezabuguzi',
    confirmTransaction: 'Emeza igikorwa',
    transactionSuccessful: 'Igikorwa cyagenze neza',
    transactionFailed: 'Igikorwa cyanze',
    insufficientFunds: 'Amafaranga ntahagije',
    dailyLimitExceeded: 'Urugero rwa buri munsi rwarenze',
    monthlyLimitExceeded: 'Urugero rwa buri kwezi rwarenze',
    invalidAmount: 'Umubare utari wo',
    minimumAmount: 'Umubare muto',
    maximumAmount: 'Umubare munini',
    transactionLimits: 'Imipaka y\'ibikorwa',
    dailyLimit: 'Urugero rwa buri munsi',
    monthlyLimit: 'Urugero rwa buri kwezi',
    remainingDaily: 'Ibisigaye bya buri munsi',
    remainingMonthly: 'Ibisigaye bya buri kwezi'
  },

  // Currency & Formatting
  currency: {
    ugx: 'Ishilingi y\'Ubugande',
    kes: 'Ishilingi ya Kenya',
    tzs: 'Ishilingi ya Tanzaniya',
    rwf: 'Ifaranga y\'u Rwanda',
    bif: 'Ifaranga y\'Uburundi',
    etb: 'Birr y\'Ubwetiyopiya',
    usd: 'Idolari y\'Amerika',
    eur: 'Euro',
    gbp: 'Ipawundi y\'Ubwongereza',
    selectCurrency: 'Hitamo ifaranga',
    preferredCurrency: 'Ifaranga ukunda',
    currencySettings: 'Igenamiterere ry\'ifaranga',
    exchangeRate: 'Igiciro cyo guhana',
    convertedAmount: 'Umubare wahinduwe',
    conversionRate: 'Igiciro cyo guhindura',
    lastUpdated: 'Byavuguruwe bwa nyuma',
    updateRates: 'Vugurura ibiciro',
    rateUnavailable: 'Igiciro ntikiboneka',
    conversionError: 'Ikosa mu guhindura',
    currencyUpdated: 'Ifaranga yahinduwe kuri {currency}',
    updateError: 'Guhindura ifaranga byanze. Ongera ugerageze.'
  },

  // Settings
  settings: {
    languageAndCurrency: 'Ururimi n\'Ifaranga',
    language: 'Ururimi',
    languageDescription: 'Hitamo ururimi ukunda kuri porogaramu',
    languageInfo: 'Porogaramu izongera itangire kugira ngo ikoreshe impinduka z\'ururimi',
    languageUpdated: 'Ururimi rwavuguruwe neza',
    languageUpdateError: 'Guvugurura ururimi byanze. Ongera ugerageze.',
    currencyDescription: 'Hitamo ifaranga ukunda mu bikorwa',
    currencyInfo: 'Amabare yose azagaragara mu faranga ukunda hamwe no guhindura mu gihe nyacyo'
  }
};
