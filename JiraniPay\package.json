{"name": "<PERSON><PERSON><PERSON><PERSON>", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node start-expo-only.js && expo start", "start-dev": "node scripts/start-dev.js", "start-prod": "node scripts/start-production.js", "start:setup": "node start-expo-only.js", "start:full": "node start-jiranipay.js", "start:default": "expo start", "start:prod": "EXPO_PUBLIC_ENVIRONMENT=production expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "check-sms": "node scripts/checkSMSConfiguration.js", "test-sms": "node scripts/checkSMSConfiguration.js", "setup-env": "node scripts/setup-secure-environment.js", "validate-config": "node -e \"require('./config/secureEnvironment.js').default.validate()\"", "validate-security": "node scripts/validate-security.js", "debug-env": "node debug-environment.js", "fix-permissions": "node scripts/fix-file-permissions.js", "debug-database": "node debug-database-connection.js", "fix-profiles": "node scripts/fix-missing-profiles.js", "validate-production": "node scripts/validate-production-mode.js", "test-init": "node scripts/test-initialization.js", "test-network": "node scripts/diagnose-network-issues.js", "validate-network-fixes": "node scripts/validate-network-fixes.js", "fix-network-failures": "node scripts/fix-network-failures.js", "validate-syntax": "node scripts/validate-syntax.js", "test-profile-creation": "node scripts/test-profile-creation.js", "test-wallet-fixes": "node scripts/test-wallet-screen-fixes.js", "test-greeting-fixes": "node scripts/test-greeting-fixes.js", "fix-profile-names": "node scripts/fix-profile-names.js", "diagnose-greeting": "node scripts/diagnose-greeting-issues.js", "fix-greeting-comprehensive": "node scripts/fix-greeting-issues-comprehensive.js", "test-profile-update-fixes": "node scripts/test-profile-update-fixes.js", "test-privacy-timeout-fixes": "node scripts/test-privacy-and-timeout-fixes.js", "audit-translations": "node scripts/audit-translation-coverage.js", "test-translations": "node scripts/test-translation-coverage.js", "fix-hardcoded-text": "node scripts/fix-hardcoded-text.js", "fix-hardcoded-text-apply": "node scripts/fix-hardcoded-text.js --apply", "start:dev": "EXPO_PUBLIC_ENVIRONMENT=development expo start", "start:staging": "EXPO_PUBLIC_ENVIRONMENT=staging expo start", "build:dev": "EXPO_PUBLIC_ENVIRONMENT=development expo build", "build:staging": "EXPO_PUBLIC_ENVIRONMENT=staging expo build", "build:prod": "EXPO_PUBLIC_ENVIRONMENT=production expo build"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@supabase/supabase-js": "^2.50.0", "expo": "53.0.18", "expo-application": "~6.1.1", "expo-camera": "~16.1.10", "expo-constants": "^17.1.7", "expo-contacts": "^14.2.5", "expo-crypto": "~14.1.3", "expo-device": "^7.1.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.4", "expo-haptics": "~14.1.4", "expo-image-loader": "^5.1.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-local-authentication": "~16.0.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.4", "expo-print": "^14.1.4", "expo-random": "^14.0.1", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-view-shot": "^4.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "react-redux": "^9.2.0", "typescript": "^5.8.3"}, "private": true}