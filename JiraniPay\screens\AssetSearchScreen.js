/**
 * Asset Search Screen
 * Screen for searching and discovering investment assets (stocks, ETFs, etc.)
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import marketDataService from '../services/marketDataService';
import { formatCurrency } from '../utils/currencyUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const AssetSearchScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { portfolioId } = route.params || {};

  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [popularAssets, setPopularAssets] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // East African market categories
  const assetCategories = [
    { key: 'all', label: 'All Assets' },
    { key: 'banking', label: 'Banking' },
    { key: 'telecommunications', label: 'Telecom' },
    { key: 'utilities', label: 'Utilities' },
    { key: 'consumer_goods', label: 'Consumer Goods' },
    { key: 'manufacturing', label: 'Manufacturing' }
  ];

  const eastAfricanMarkets = [
    { key: 'all', label: 'All Markets', code: 'all' },
    { key: 'use', label: 'Uganda (USE)', code: 'USE' },
    { key: 'nse', label: 'Kenya (NSE)', code: 'NSE' },
    { key: 'dse', label: 'Tanzania (DSE)', code: 'DSE' },
    { key: 'rse', label: 'Rwanda (RSE)', code: 'RSE' }
  ];

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (searchQuery.length > 0 || selectedCategory !== 'all') {
      searchAssets();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, selectedCategory]);

  const loadInitialData = async () => {
    try {
      setInitialLoading(true);

      // Load popular East African assets
      const popularResult = await marketDataService.getPopularEastAfricanAssets();
      if (popularResult.success) {
        setPopularAssets(popularResult.assets);
      }

      setCategories(assetCategories);

    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load East African market data');
    } finally {
      setInitialLoading(false);
    }
  };

  const searchAssets = async () => {
    try {
      setLoading(true);

      let filteredAssets = [];

      if (searchQuery.trim()) {
        // Search with query
        const searchResult = await marketDataService.searchEastAfricanAssets(searchQuery);
        if (searchResult.success) {
          filteredAssets = searchResult.assets;
        }
      } else {
        // No search query, load by category
        if (selectedCategory === 'all') {
          // Load all assets from all markets
          const allMarketsResult = await marketDataService.searchEastAfricanAssets('', 'all');
          if (allMarketsResult.success) {
            filteredAssets = allMarketsResult.assets;
          }
        } else {
          // Load assets by specific sector
          const allMarketsResult = await marketDataService.searchEastAfricanAssets('', 'all');
          if (allMarketsResult.success) {
            filteredAssets = allMarketsResult.assets.filter(asset =>
              asset.sector.toLowerCase().replace(' ', '_') === selectedCategory ||
              asset.sector.toLowerCase().includes(selectedCategory.replace('_', ' ')) ||
              asset.sector.toLowerCase() === selectedCategory.toLowerCase()
            );
          }
        }
      }

      // Apply category filter if not 'all'
      if (selectedCategory !== 'all' && searchQuery.trim()) {
        filteredAssets = filteredAssets.filter(asset =>
          asset.sector.toLowerCase().replace(' ', '_') === selectedCategory ||
          asset.sector.toLowerCase().includes(selectedCategory.replace('_', ' ')) ||
          asset.sector.toLowerCase() === selectedCategory.toLowerCase()
        );
      }

      setSearchResults(filteredAssets);

    } catch (error) {
      console.error('❌ Error searching assets:', error);
      Alert.alert('Error', 'Failed to search East African assets');
    } finally {
      setLoading(false);
    }
  };

  const handleAssetPress = (asset) => {
    if (portfolioId) {
      // Navigate to buy/trade screen if coming from portfolio
      navigation.navigate('AssetTrade', { 
        asset, 
        portfolioId,
        action: 'buy'
      });
    } else {
      // Navigate to asset details
      navigation.navigate('AssetDetails', { 
        symbol: asset.symbol 
      });
    }
  };

  const getChangeColor = (change) => {
    if (change > 0) return theme.colors.success;
    if (change < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const getAssetTypeIcon = (type) => {
    const icons = {
      stock: 'trending-up',
      etf: 'layers',
      crypto: 'logo-bitcoin',
      bonds: 'shield-checkmark'
    };
    return icons[type] || 'help-circle';
  };

  const getAssetTypeColor = (type) => {
    const colors = {
      stock: '#4ECDC4',
      etf: '#45B7D1',
      crypto: '#FF6B35',
      bonds: '#96CEB4'
    };
    return colors[type] || '#6C5CE7';
  };

  const renderCategoryTabs = () => (
    <View style={styles.categoryContainer}>
      <FlatList
        data={categories}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.key && styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(item.key)}
          >
            <Text style={[
              styles.categoryButtonText,
              selectedCategory === item.key && styles.categoryButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderAssetCard = ({ item: asset }) => (
    <TouchableOpacity 
      style={styles.assetCard}
      onPress={() => handleAssetPress(asset)}
    >
      <View style={styles.assetHeader}>
        <View style={styles.assetInfo}>
          <View style={styles.assetTitleRow}>
            <View style={styles.symbolAndMarket}>
              <Text style={styles.assetSymbol}>{asset.symbol}</Text>
              {asset.market && (
                <Text style={styles.assetMarket}>• {asset.market}</Text>
              )}
            </View>
            <View style={[styles.assetTypeTag, { backgroundColor: getAssetTypeColor(asset.type) }]}>
              <Ionicons name={getAssetTypeIcon(asset.type)} size={10} color={theme.colors.white} />
              <Text style={styles.assetTypeText}>{asset.type.toUpperCase()}</Text>
            </View>
          </View>
          <Text style={styles.assetName} numberOfLines={1}>{asset.name}</Text>
          {asset.sector && (
            <Text style={styles.assetSector}>{asset.sector}</Text>
          )}
        </View>
        <View style={styles.assetPrice}>
          <Text style={styles.priceText}>
            {formatCurrency(asset.price, asset.currency || 'UGX')}
          </Text>
          <View style={styles.changeContainer}>
            <Text style={[styles.changeText, { color: getChangeColor(asset.change) }]}>
              {asset.change >= 0 ? '+' : ''}{formatCurrency(asset.change, asset.currency || 'UGX')}
            </Text>
            <Text style={[styles.changePercent, { color: getChangeColor(asset.change) }]}>
              ({asset.change >= 0 ? '+' : ''}{asset.changePercent.toFixed(2)}%)
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.assetFooter}>
        {portfolioId ? (
          <View style={styles.tradeButton}>
            <Ionicons name="add-circle" size={16} color={theme.colors.primary} />
            <Text style={styles.tradeButtonText}>Add to Portfolio</Text>
          </View>
        ) : (
          <View style={styles.viewDetailsButton}>
            <Text style={styles.viewDetailsText}>View Details</Text>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderSearchResults = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      );
    }

    if (searchQuery.length > 0 && searchResults.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="search-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptyDescription}>
            Try searching with different keywords or check the spelling
          </Text>
        </View>
      );
    }

    const dataToShow = searchQuery.length > 0 ? searchResults : popularAssets.filter(asset => 
      selectedCategory === 'all' || asset.type === selectedCategory
    );

    return (
      <FlatList
        data={dataToShow}
        renderItem={renderAssetCard}
        keyExtractor={(item) => item.symbol}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.assetsList}
      />
    );
  };

  if (initialLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading assets...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>
          {portfolioId ? 'Add Assets' : 'Search Assets'}
        </Text>
        <TouchableOpacity onPress={() => navigation.navigate('MarketOverview')}>
          <Ionicons name="trending-up" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search stocks, ETFs, crypto..."
            placeholderTextColor={theme.colors.textSecondary}
            autoCapitalize="characters"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Category Tabs */}
      {renderCategoryTabs()}

      {/* Content */}
      <View style={styles.content}>
        {searchQuery.length === 0 && (
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {selectedCategory === 'all' ? 'Popular Assets' : `Popular ${categories.find(c => c.key === selectedCategory)?.label}`}
            </Text>
          </View>
        )}
        {renderSearchResults()}
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
  },
  categoryContainer: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  categoryButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  categoryButtonTextActive: {
    color: theme.colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  assetsList: {
    paddingBottom: 20,
  },
  assetCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  assetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  assetInfo: {
    flex: 1,
    marginRight: 12,
  },
  assetTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  symbolAndMarket: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  assetSymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 6,
  },
  assetMarket: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  assetTypeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  assetTypeText: {
    fontSize: 8,
    color: theme.colors.white,
    fontWeight: '600',
    marginLeft: 2,
  },
  assetName: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  assetSector: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  assetPrice: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    fontWeight: '500',
    marginRight: 4,
  },
  changePercent: {
    fontSize: 12,
    fontWeight: '500',
  },
  assetFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  tradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tradeButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewDetailsText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
});

export default AssetSearchScreen;
