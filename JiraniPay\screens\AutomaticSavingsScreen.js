import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  TextInput,
  Modal,
  Switch,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import automaticSavingsService from '../services/automaticSavingsService';
import scheduledPaymentsService from '../services/scheduledPaymentsService';
import savingsService from '../services/savingsService';
import currencyService from '../services/currencyService';

const AutomaticSavingsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [savingsAccounts, setSavingsAccounts] = useState([]);
  const [automaticPlans, setAutomaticPlans] = useState([]);
  const [reminders, setReminders] = useState([]);
  
  // Modal states
  const [createPlanModalVisible, setCreatePlanModalVisible] = useState(false);
  const [createReminderModalVisible, setCreateReminderModalVisible] = useState(false);
  
  // Form states for automatic savings plan
  const [planName, setPlanName] = useState('');
  const [planAmount, setPlanAmount] = useState('');
  const [planFrequency, setPlanFrequency] = useState('weekly');
  const [customInterval, setCustomInterval] = useState('');
  const [selectedSavingsAccount, setSelectedSavingsAccount] = useState(null);
  const [reminderEnabled, setReminderEnabled] = useState(true);
  const [reminderMinutes, setReminderMinutes] = useState('60');
  
  // Form states for reminder
  const [reminderTitle, setReminderTitle] = useState('');
  const [reminderDescription, setReminderDescription] = useState('');
  const [reminderAmount, setReminderAmount] = useState('');
  const [reminderFrequency, setReminderFrequency] = useState('weekly');
  const [reminderTime, setReminderTime] = useState('09:00');

  // Time picker states
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedTime, setSelectedTime] = useState(new Date());

  useEffect(() => {
    loadData();

    // Initialize time picker with default time (9:00 AM)
    const defaultTime = new Date();
    defaultTime.setHours(9, 0, 0, 0);
    setSelectedTime(defaultTime);

    // Handle navigation from Budget Insights with recommendation data
    if (route?.params?.recommendation) {
      const { recommendation } = route.params;
      handleRecommendationSetup(recommendation);
    }
  }, [route?.params]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Initialize services
      if (!automaticSavingsService.isInitialized) {
        await automaticSavingsService.initialize();
      }
      
      // Load savings accounts
      const accounts = await savingsService.getSavingsAccounts();
      if (accounts.success) {
        setSavingsAccounts(accounts.data);
        if (accounts.data.length > 0 && !selectedSavingsAccount) {
          setSelectedSavingsAccount(accounts.data[0]);
        }
      }
      
      // Load existing automatic plans
      const plans = automaticSavingsService.getAutomaticSavingsPlans();
      setAutomaticPlans(plans);
      
      // Load reminders
      const savedReminders = automaticSavingsService.getSavingsReminders();
      setReminders(savedReminders);
      
    } catch (error) {
      console.error('❌ Error loading automatic savings data:', error);
      Alert.alert('Error', 'Failed to load automatic savings data');
    } finally {
      setLoading(false);
    }
  };

  const calculateRecommendedAmount = (type) => {
    // Calculate recommended amount based on user's financial profile
    const baseAmount = walletBalance || 0;
    const totalSavings = savingsSummary?.total_savings || 0;

    if (type === 'boost_savings' || type === 'savings_optimization') {
      // Recommend 10-15% of current wallet balance or minimum 10,000 UGX
      const recommendedAmount = Math.max(10000, Math.floor(baseAmount * 0.12));
      return Math.min(recommendedAmount, 100000); // Cap at 100,000 UGX
    } else if (type === 'emergency_fund') {
      // Recommend building emergency fund gradually
      // If they have less than 50,000 in emergency savings, recommend 15,000-30,000 weekly
      const emergencyFundTarget = 200000; // 200,000 UGX target
      const currentEmergencyFund = totalSavings; // Simplified - could be more specific

      if (currentEmergencyFund < emergencyFundTarget) {
        const remainingAmount = emergencyFundTarget - currentEmergencyFund;
        const weeksToTarget = 20; // 20 weeks to build emergency fund
        return Math.max(15000, Math.min(30000, Math.floor(remainingAmount / weeksToTarget)));
      }
      return 15000; // Maintenance amount
    }

    // Default fallback
    return 20000;
  };

  const handleRecommendationSetup = (recommendation) => {
    // Pre-fill form based on recommendation
    if (recommendation.id === 'savings_optimization') {
      setPlanName('Boost Savings Rate');
      setReminderTitle('💰 Boost Your Savings Rate');
      setReminderDescription('Time to increase your savings! Aim for 20% of your income.');

      // Calculate recommended amount based on user's current savings pattern
      const recommendedAmount = calculateRecommendedAmount('savings_optimization');
      setPlanAmount(recommendedAmount.toString());
      setReminderAmount(recommendedAmount.toString());
    } else if (recommendation.id === 'emergency_fund') {
      setPlanName('Emergency Fund Builder');
      setReminderTitle('🛡️ Build Emergency Fund');
      setReminderDescription('Build your emergency fund with consistent weekly savings.');

      // Calculate recommended amount based on user's financial profile
      const recommendedAmount = calculateRecommendedAmount('emergency_fund');
      setPlanAmount(recommendedAmount.toString());
      setReminderAmount(recommendedAmount.toString());
    }
    
    // Auto-open appropriate modal
    setTimeout(() => {
      if (route?.params?.setupType === 'automatic') {
        setCreatePlanModalVisible(true);
      } else {
        setCreateReminderModalVisible(true);
      }
    }, 500);
  };

  const formatCurrency = (amount) => {
    try {
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return currencyService.format(numAmount, 'UGX');
    } catch (error) {
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return `UGX ${numAmount.toLocaleString()}`;
    }
  };

  // Time picker helper functions
  const formatTimeDisplay = (date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const displayMinutes = minutes.toString().padStart(2, '0');
    return `${displayHours}:${displayMinutes} ${ampm}`;
  };

  const formatTimeForStorage = (date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const parseTimeString = (timeString) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const handleTimeChange = (event, selectedDate) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(false);
    }

    if (selectedDate) {
      setSelectedTime(selectedDate);
      const formattedTime = formatTimeForStorage(selectedDate);
      setReminderTime(formattedTime);
    }
  };

  const showTimePickerModal = () => {
    // Parse current reminder time and set it as selected time
    if (reminderTime) {
      const parsedTime = parseTimeString(reminderTime);
      setSelectedTime(parsedTime);
    }
    setShowTimePicker(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const hideTimePicker = () => {
    setShowTimePicker(false);
  };

  const handleCreateAutomaticPlan = async () => {
    if (!planName.trim() || !planAmount || !selectedSavingsAccount) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(planAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const planData = {
        name: planName.trim(),
        description: `Automatic savings plan: ${planName.trim()}`,
        amount,
        frequency: planFrequency,
        customInterval: planFrequency === 'custom' ? parseInt(customInterval) : null,
        targetSavingsAccountId: selectedSavingsAccount.id,
        startDate: new Date(),
        reminderEnabled,
        reminderMinutes: parseInt(reminderMinutes)
      };

      const result = await automaticSavingsService.createAutomaticSavingsPlan(planData);

      if (result.success) {
        // If called from Wallet Settings, also create an auto-save goal
        if (route?.params?.source === 'walletSettings') {
          try {
            const goalData = {
              name: planName.trim(),
              description: `Auto-save goal: ${planName.trim()}`,
              targetAmount: amount * 20, // Estimate target as 20x the transfer amount
              autoSaveAmount: amount,
              frequency: planFrequency,
              isActive: true
            };

            await scheduledPaymentsService.createAutoSaveGoal(goalData);
            console.log('✅ Auto-save goal created for wallet settings');
          } catch (error) {
            console.error('❌ Error creating auto-save goal:', error);
            // Don't fail the whole operation if goal creation fails
          }
        }

        Alert.alert('Success', `Automatic savings plan "${planName}" created successfully!`);
        setCreatePlanModalVisible(false);
        resetPlanForm();
        loadData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Create automatic plan error:', error);
      Alert.alert('Error', 'Failed to create automatic savings plan');
    }
  };

  const handleCreateReminder = async () => {
    if (!reminderTitle.trim() || !reminderDescription.trim() || !reminderAmount) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(reminderAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const reminderData = {
        title: reminderTitle.trim(),
        description: reminderDescription.trim(),
        recommendedAmount: amount,
        frequency: reminderFrequency,
        reminderTime,
        recommendationId: route?.params?.recommendation?.id || null
      };

      const result = await automaticSavingsService.createSavingsReminder(reminderData);

      if (result.success) {
        Alert.alert('Success', `Savings reminder "${reminderTitle}" created successfully!`);
        setCreateReminderModalVisible(false);
        resetReminderForm();
        loadData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Create reminder error:', error);
      Alert.alert('Error', 'Failed to create savings reminder');
    }
  };

  const resetPlanForm = () => {
    setPlanName('');
    setPlanAmount('');
    setPlanFrequency('weekly');
    setCustomInterval('');
    setReminderEnabled(true);
    setReminderMinutes('60');
  };

  const resetReminderForm = () => {
    setReminderTitle('');
    setReminderDescription('');
    setReminderAmount('');
    setReminderFrequency('weekly');
    setReminderTime('09:00');

    // Reset time picker to default (9:00 AM)
    const defaultTime = new Date();
    defaultTime.setHours(9, 0, 0, 0);
    setSelectedTime(defaultTime);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Automatic Savings</Text>
        <Text style={styles.headerSubtitle}>Set up recurring savings & reminders</Text>
      </View>
      <View style={styles.placeholder} />
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setCreatePlanModalVisible(true)}
      >
        <Ionicons name="repeat-outline" size={24} color={Colors.status.success} />
        <Text style={styles.quickActionText}>Auto Savings</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setCreateReminderModalVisible(true)}
      >
        <Ionicons name="notifications-outline" size={24} color={Colors.accent.amber} />
        <Text style={styles.quickActionText}>Set Reminder</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('Savings')}
      >
        <Ionicons name="wallet-outline" size={24} color={Colors.primary.main} />
        <Text style={styles.quickActionText}>View Savings</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAutomaticPlans = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Automatic Savings Plans</Text>
      {automaticPlans.length > 0 ? (
        automaticPlans.map((plan) => (
          <View key={plan.id} style={styles.planCard}>
            <View style={styles.planHeader}>
              <View style={styles.planInfo}>
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planDetails}>
                  {formatCurrency(plan.amount)} • {plan.frequency}
                </Text>
              </View>
              <View style={styles.planStatus}>
                <View style={[styles.statusDot, { backgroundColor: plan.isActive ? Colors.status.success : Colors.neutral.warmGray }]} />
                <Text style={styles.statusText}>{plan.isActive ? 'Active' : 'Inactive'}</Text>
              </View>
            </View>
            <Text style={styles.planStats}>
              Saved: {formatCurrency(plan.totalSaved)} • Executions: {plan.executionCount}
            </Text>
            {plan.nextExecutionDate && (
              <Text style={styles.nextExecution}>
                Next: {new Date(plan.nextExecutionDate).toLocaleDateString()}
              </Text>
            )}
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="repeat-outline" size={48} color={Colors.neutral.warmGray} />
          <Text style={styles.emptyStateText}>No automatic savings plans yet</Text>
          <TouchableOpacity 
            style={styles.createFirstButton}
            onPress={() => setCreatePlanModalVisible(true)}
          >
            <Text style={styles.createFirstButtonText}>Create Your First Plan</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderReminders = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Savings Reminders</Text>
      {reminders.length > 0 ? (
        reminders.map((reminder) => (
          <View key={reminder.id} style={styles.reminderCard}>
            <View style={styles.reminderHeader}>
              <Ionicons name="notifications-outline" size={20} color={Colors.accent.amber} />
              <Text style={styles.reminderTitle}>{reminder.title}</Text>
            </View>
            <Text style={styles.reminderDescription}>{reminder.description}</Text>
            <Text style={styles.reminderDetails}>
              {formatCurrency(reminder.recommendedAmount)} • {reminder.frequency} at {reminder.reminderTime}
            </Text>
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="notifications-outline" size={48} color={Colors.neutral.warmGray} />
          <Text style={styles.emptyStateText}>No savings reminders set</Text>
          <TouchableOpacity 
            style={styles.createFirstButton}
            onPress={() => setCreateReminderModalVisible(true)}
          >
            <Text style={styles.createFirstButtonText}>Set Your First Reminder</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading automatic savings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderQuickActions()}
        {renderAutomaticPlans()}
        {renderReminders()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Create Automatic Plan Modal */}
      <Modal
        visible={createPlanModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setCreatePlanModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create Automatic Savings Plan</Text>
            
            <TextInput
              style={styles.textInput}
              placeholder="Plan name (e.g., Emergency Fund)"
              value={planName}
              onChangeText={setPlanName}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Amount per transfer"
              value={planAmount}
              onChangeText={setPlanAmount}
              keyboardType="numeric"
            />
            
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Frequency:</Text>
              <View style={styles.frequencyButtons}>
                {['weekly', 'monthly', 'custom'].map((freq) => (
                  <TouchableOpacity
                    key={freq}
                    style={[styles.frequencyButton, planFrequency === freq && styles.frequencyButtonActive]}
                    onPress={() => setPlanFrequency(freq)}
                  >
                    <Text style={[styles.frequencyButtonText, planFrequency === freq && styles.frequencyButtonTextActive]}>
                      {freq.charAt(0).toUpperCase() + freq.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            {planFrequency === 'custom' && (
              <TextInput
                style={styles.textInput}
                placeholder="Custom interval (days)"
                value={customInterval}
                onChangeText={setCustomInterval}
                keyboardType="numeric"
              />
            )}
            
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Enable reminders</Text>
              <Switch
                value={reminderEnabled}
                onValueChange={setReminderEnabled}
                trackColor={{ false: Colors.neutral.warmGray, true: Colors.primary.main }}
                thumbColor={reminderEnabled ? Colors.neutral.white : Colors.neutral.creamDark}
              />
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setCreatePlanModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleCreateAutomaticPlan}
              >
                <Text style={styles.confirmButtonText}>Create Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Create Reminder Modal */}
      <Modal
        visible={createReminderModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setCreateReminderModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Set Savings Reminder</Text>
            
            <TextInput
              style={styles.textInput}
              placeholder="Reminder title"
              value={reminderTitle}
              onChangeText={setReminderTitle}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Description"
              value={reminderDescription}
              onChangeText={setReminderDescription}
              multiline
              numberOfLines={2}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Recommended amount"
              value={reminderAmount}
              onChangeText={setReminderAmount}
              keyboardType="numeric"
            />
            
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Frequency:</Text>
              <View style={styles.frequencyButtons}>
                {['daily', 'weekly', 'monthly'].map((freq) => (
                  <TouchableOpacity
                    key={freq}
                    style={[styles.frequencyButton, reminderFrequency === freq && styles.frequencyButtonActive]}
                    onPress={() => setReminderFrequency(freq)}
                  >
                    <Text style={[styles.frequencyButtonText, reminderFrequency === freq && styles.frequencyButtonTextActive]}>
                      {freq.charAt(0).toUpperCase() + freq.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.timePickerContainer}>
              <Text style={styles.pickerLabel}>Reminder Time:</Text>
              <TouchableOpacity
                style={styles.timePickerButton}
                onPress={showTimePickerModal}
              >
                <View style={styles.timePickerContent}>
                  <Ionicons name="time-outline" size={20} color={Colors.primary.main} />
                  <Text style={styles.timePickerText}>
                    {formatTimeDisplay(selectedTime)}
                  </Text>
                  <Ionicons name="chevron-down" size={16} color={Colors.neutral.warmGray} />
                </View>
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setCreateReminderModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleCreateReminder}
              >
                <Text style={styles.confirmButtonText}>Set Reminder</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Time Picker Modal */}
      {showTimePicker && (
        <Modal
          transparent={true}
          animationType="fade"
          visible={showTimePicker}
          onRequestClose={hideTimePicker}
        >
          <View style={styles.timePickerModalOverlay}>
            <View style={styles.timePickerModalContent}>
              <View style={styles.timePickerHeader}>
                <Text style={styles.timePickerTitle}>Select Reminder Time</Text>
              </View>

              <View style={styles.timePickerWrapper}>
                <DateTimePicker
                  value={selectedTime}
                  mode="time"
                  is24Hour={false}
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleTimeChange}
                  style={styles.timePicker}
                />
              </View>

              <View style={styles.timePickerActions}>
                <TouchableOpacity
                  style={styles.timePickerCancelButton}
                  onPress={hideTimePicker}
                >
                  <Text style={styles.timePickerCancelText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.timePickerConfirmButton}
                  onPress={() => {
                    const formattedTime = formatTimeForStorage(selectedTime);
                    setReminderTime(formattedTime);
                    hideTimePicker();
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  }}
                >
                  <Text style={styles.timePickerConfirmText}>Confirm</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.appBackground,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    marginVertical: 16,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    minWidth: 80,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
    marginTop: 4,
    textAlign: 'center',
  },
  section: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  planCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  planDetails: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  planStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  planStats: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginBottom: 4,
  },
  nextExecution: {
    fontSize: 12,
    color: Colors.primary.main,
    fontWeight: '500',
  },
  reminderCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reminderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginLeft: 8,
  },
  reminderDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 8,
  },
  reminderDetails: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 12,
    marginBottom: 16,
  },
  createFirstButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createFirstButtonText: {
    color: Colors.neutral.white,
    fontWeight: '600',
    fontSize: 14,
  },
  bottomSpacing: {
    height: 32,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 20,
    textAlign: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: Colors.neutral.creamLight,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  frequencyButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  frequencyButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  frequencyButtonActive: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  frequencyButtonText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
  },
  frequencyButtonTextActive: {
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.neutral.charcoal,
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: Colors.primary.main,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
  },
  // Time picker styles
  timePickerContainer: {
    marginBottom: 16,
  },
  timePickerButton: {
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    borderRadius: 8,
    backgroundColor: Colors.neutral.creamLight,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  timePickerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timePickerText: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    fontWeight: '500',
    flex: 1,
    marginLeft: 12,
  },
  timePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timePickerModalContent: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 0,
    width: '85%',
    maxWidth: 320,
    overflow: 'hidden',
  },
  timePickerHeader: {
    backgroundColor: Colors.primary.main,
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  timePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  timePickerWrapper: {
    paddingVertical: 20,
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
  },
  timePicker: {
    width: '100%',
    height: 120,
  },
  timePickerActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  timePickerCancelButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: Colors.neutral.creamDark,
  },
  timePickerCancelText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    fontWeight: '500',
  },
  timePickerConfirmButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    backgroundColor: Colors.primary.main,
  },
  timePickerConfirmText: {
    fontSize: 16,
    color: Colors.neutral.white,
    fontWeight: '600',
  },
});

export default AutomaticSavingsScreen;
