import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const EditProfileScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { onProfileUpdated } = route.params || {};

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    country_code: 'UG',
    language_preference: 'en',
    date_of_birth: null,
    currency_preference: 'UGX',
  });
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  console.log('🎨 EditProfile: Theme received:', theme);
  console.log('🎨 EditProfile: Theme colors:', theme?.colors);

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      // For now, use mock data - you can integrate with your profile service later
      setFormData({
        full_name: 'Tuyizere Issa Abdu',
        email: '<EMAIL>',
        phone: '0703089916',
        country_code: 'UG',
        language_preference: 'en',
        date_of_birth: null,
        currency_preference: 'UGX',
      });
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasChanges(false);

      if (onProfileUpdated) {
        onProfileUpdated();
      }

      Alert.alert(
        'Success',
        'Profile updated successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Failed to save profile changes');
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    if (hasChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to go back?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Discard', onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  if (loading) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fcf7f0'
      }}>
        <ActivityIndicator size="large" color="#E67E22" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: '#2C3E50'
        }}>
          Loading Profile...
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: '#fcf7f0' }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={{
        paddingTop: Platform.OS === 'ios' ? 50 : 30,
        paddingBottom: 20,
        paddingHorizontal: 20,
        backgroundColor: '#E67E22'
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              justifyContent: 'center',
              alignItems: 'center'
            }}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#FFFFFF',
            flex: 1,
            textAlign: 'center',
            marginHorizontal: 16
          }}>
            Edit Profile
          </Text>

          <TouchableOpacity
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              minWidth: 60,
              alignItems: 'center',
              opacity: (!hasChanges || saving) ? 0.5 : 1
            }}
            onPress={handleSave}
            disabled={!hasChanges || saving}
            activeOpacity={0.7}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={{
                color: '#FFFFFF',
                fontWeight: '600',
                fontSize: 14
              }}>
                Save
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Form Content */}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={{ padding: 20 }}>
          {/* Personal Information Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Personal Information
            </Text>

            {/* Full Name */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Full Name *
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: errors.full_name ? '#C0392B' : '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#2C3E50'
                }}
                value={formData.full_name}
                onChangeText={(text) => handleFieldChange('full_name', text)}
                placeholder="Enter your full name"
                placeholderTextColor="#95A5A6"
                autoCapitalize="words"
              />
              {errors.full_name && (
                <Text style={{
                  color: '#C0392B',
                  fontSize: 12,
                  marginTop: 4
                }}>
                  {errors.full_name}
                </Text>
              )}
            </View>

            {/* Email */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Email Address *
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: errors.email ? '#C0392B' : '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#2C3E50'
                }}
                value={formData.email}
                onChangeText={(text) => handleFieldChange('email', text)}
                placeholder="Enter your email address"
                placeholderTextColor="#95A5A6"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && (
                <Text style={{
                  color: '#C0392B',
                  fontSize: 12,
                  marginTop: 4
                }}>
                  {errors.email}
                </Text>
              )}
            </View>

            {/* Phone */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Phone Number
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#F8F9FA',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#95A5A6'
                }}
                value={formData.phone}
                editable={false}
                placeholder="Phone number"
                placeholderTextColor="#95A5A6"
              />
              <Text style={{
                fontSize: 12,
                color: '#95A5A6',
                marginTop: 4
              }}>
                Phone number cannot be changed
              </Text>
            </View>

            {/* Date of Birth */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Date of Birth
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={() => {/* Add date picker functionality later */}}
              >
                <Text style={{
                  fontSize: 16,
                  color: formData.date_of_birth ? '#2C3E50' : '#95A5A6'
                }}>
                  {formData.date_of_birth || 'Select date of birth'}
                </Text>
                <Ionicons name="calendar-outline" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Account Settings Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Account Settings
            </Text>

            {/* Country */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Country
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={() => {/* Add country selector functionality later */}}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>🇺🇬</Text>
                  <Text style={{ fontSize: 16, color: '#2C3E50' }}>Uganda</Text>
                </View>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>

            {/* Language Preference */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Language
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={() => {/* Add language selector functionality later */}}
              >
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>English</Text>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>

            {/* Currency Preference */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Preferred Currency
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={() => {/* Add currency selector functionality later */}}
              >
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>UGX - Ugandan Shilling</Text>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Security Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Security & Privacy
            </Text>

            {/* Change Password */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12
              }}
              onPress={() => {/* Navigate to change password screen */}}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="lock-closed-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Change Password</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>

            {/* Two-Factor Authentication */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12
              }}
              onPress={() => {/* Navigate to 2FA settings */}}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="shield-checkmark-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Two-Factor Authentication</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>

            {/* Privacy Settings */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
              onPress={() => {/* Navigate to privacy settings */}}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="eye-off-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Privacy Settings</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>
          </View>

          <View style={{ height: 40 }} />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EditProfileScreen;
