import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  Modal,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';
import { getCurrentUserId } from '../utils/userUtils';
import { currencyService } from '../services/currencyService';

const EditProfileScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { onProfileUpdated } = route.params || {};

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    country_code: 'UG',
    language_preference: 'en',
    date_of_birth: null,
    // NOTE: currency_preference removed - handled separately via currencyService
  });
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date(1990, 0, 1)); // Default to a reasonable birth year

  // Modal states
  const [showCountrySelector, setShowCountrySelector] = useState(false);
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);
  const [showCurrencySelector, setShowCurrencySelector] = useState(false);

  // Selection states
  const [selectedCountry, setSelectedCountry] = useState({ code: 'UG', name: 'Uganda', flag: '🇺🇬' });
  const [selectedLanguage, setSelectedLanguage] = useState({ code: 'en', name: 'English' });
  const [selectedCurrency, setSelectedCurrency] = useState({ code: 'UGX', name: 'Ugandan Shilling', symbol: 'UGX' });

  console.log('🎨 EditProfile: Theme received:', theme);
  console.log('🎨 EditProfile: Theme colors:', theme?.colors);
  console.log('🔄 EditProfile: Screen initialized with new save functionality v4.0 - CURRENCY FIXED');

  // Data for selectors
  const countries = [
    { code: 'UG', name: 'Uganda', flag: '🇺🇬' },
    { code: 'KE', name: 'Kenya', flag: '🇰🇪' },
    { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },
    { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },
    { code: 'BI', name: 'Burundi', flag: '🇧🇮' },
    { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },
  ];

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'sw', name: 'Kiswahili' },
    { code: 'fr', name: 'Français' },
    { code: 'ar', name: 'العربية' },
    { code: 'am', name: 'አማርኛ' },
  ];

  const currencies = [
    { code: 'UGX', name: 'Ugandan Shilling', symbol: 'UGX', flag: '🇺🇬' },
    { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪' },
    { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh', flag: '🇹🇿' },
    { code: 'RWF', name: 'Rwandan Franc', symbol: 'RWF', flag: '🇷🇼' },
    { code: 'BIF', name: 'Burundian Franc', symbol: 'BIF', flag: '🇧🇮' },
    { code: 'ETB', name: 'Ethiopian Birr', symbol: 'ETB', flag: '🇪🇹' },
  ];

  useEffect(() => {
    console.log('🔄 EditProfile: Component mounted, loading user profile...');
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      console.log('📱 Loading user profile for editing...');

      // Get current user ID
      const userId = await getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('👤 Loading profile for user:', userId);

      // Load profile from authService
      const profileResult = await authService.getUserProfile(userId);

      if (!profileResult.success) {
        throw new Error(profileResult.error || 'Failed to load profile');
      }

      const profile = profileResult.data;
      console.log('✅ Profile loaded successfully:', profile);

      // Set form data with loaded profile
      setFormData({
        full_name: profile.full_name || '',
        email: profile.email || '',
        phone: profile.phone || profile.phone_number || '',
        country_code: profile.country_code || 'UG',
        language_preference: profile.language_preference || 'en',
        date_of_birth: profile.date_of_birth || null,
        // NOTE: currency_preference removed - handled separately via currencyService
      });

      // Set selected states to match loaded profile
      const countryCode = profile.country_code || 'UG';
      const languageCode = profile.language_preference || 'en';
      // Get currency from currencyService instead of profile
      const currencyCode = 'UGX'; // Default, will be loaded from currencyService

      // Find and set the selected country
      const foundCountry = countries.find(c => c.code === countryCode);
      if (foundCountry) {
        setSelectedCountry(foundCountry);
        console.log('🌍 Set selected country:', foundCountry);
      }

      // Find and set the selected language
      const foundLanguage = languages.find(l => l.code === languageCode);
      if (foundLanguage) {
        setSelectedLanguage(foundLanguage);
        console.log('🗣️ Set selected language:', foundLanguage);
      }

      // Find and set the selected currency
      const foundCurrency = currencies.find(c => c.code === currencyCode);
      if (foundCurrency) {
        setSelectedCurrency(foundCurrency);
        console.log('💰 Set selected currency:', foundCurrency);
      }

      console.log('📝 Form data set:', {
        full_name: profile.full_name,
        email: profile.email,
        phone: profile.phone || profile.phone_number,
        country_code: profile.country_code,
        language_preference: profile.language_preference,
        date_of_birth: profile.date_of_birth
      });

    } catch (error) {
      console.error('❌ Error loading profile:', error);
      Alert.alert('Error', `Failed to load profile data: ${error.message}`);

      // Set default values if loading fails
      setFormData({
        full_name: '',
        email: '',
        phone: '',
        country_code: 'UG',
        language_preference: 'en',
        date_of_birth: null,
        // NOTE: currency_preference removed - handled separately via currencyService
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);
      console.log('💾 Starting profile save process...');

      // Get current user ID
      const userId = await getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('👤 Saving profile for user:', userId);
      console.log('📝 Profile data to save:', formData);
      console.log('🔧 Current selected states:', {
        country: selectedCountry,
        language: selectedLanguage,
        currency: selectedCurrency
      });

      // Prepare profile data for saving (excluding currency_preference)
      const profileData = {
        full_name: formData.full_name.trim(),
        email: formData.email.trim() || null,
        phone_number: formData.phone.trim(),
        country_code: selectedCountry.code || formData.country_code,
        date_of_birth: formData.date_of_birth,
        language_preference: selectedLanguage.code || formData.language_preference || 'en'
        // NOTE: currency_preference is handled separately via currencyService
      };

      console.log('🔧 Using selected values:', {
        country: selectedCountry.code,
        language: selectedLanguage.code,
        currency: selectedCurrency.code
      });

      // Save profile using profileManagementService
      const result = await profileManagementService.updateProfile(userId, profileData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }

      console.log('✅ Profile saved successfully:', result.data);

      // Save currency preference separately using currencyService
      const currencyCode = selectedCurrency.code || 'UGX';
      console.log('💰 Saving currency preference:', currencyCode);

      try {
        const currencyResult = await currencyService.setUserPreferredCurrency(currencyCode);
        if (currencyResult.success) {
          console.log('✅ Currency preference saved successfully');
        } else {
          console.warn('⚠️ Currency preference save failed:', currencyResult.error);
        }
      } catch (currencyError) {
        console.warn('⚠️ Currency preference save error:', currencyError);
      }

      setHasChanges(false);

      // Call the callback if provided
      if (onProfileUpdated) {
        onProfileUpdated();
      }

      Alert.alert(
        'Success',
        'Profile updated successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('❌ Error saving profile:', error);
      Alert.alert(
        'Error',
        `Failed to save profile changes: ${error.message}`,
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    if (hasChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to go back?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Discard', onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const handleDateChange = (event, date) => {
    console.log('📅 Date picker event:', event.type, date);

    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      if (event.type === 'set' && date) {
        setSelectedDate(date);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        handleFieldChange('date_of_birth', formattedDate);
      }
    } else {
      // iOS - only update the selected date, don't close modal yet
      if (date) {
        setSelectedDate(date);
      }
    }
  };

  const showDatePickerModal = () => {
    console.log('📅 Opening date picker');
    setShowDatePicker(true);
  };

  const hideDatePicker = () => {
    console.log('📅 Closing date picker');
    setShowDatePicker(false);
  };

  const forceCloseDatePicker = () => {
    console.log('📅 Force closing date picker');
    setShowDatePicker(false);
    // Add a small delay to ensure state is updated
    setTimeout(() => {
      setShowDatePicker(false);
    }, 100);
  };

  // Security and Privacy navigation functions
  const navigateToSecuritySettings = () => {
    console.log('🔒 Navigating to Security Settings');
    navigation.navigate('SecuritySettings');
  };

  const navigateToTwoFactorAuth = () => {
    console.log('🛡️ Navigating to Two-Factor Authentication');
    navigation.navigate('TwoFactorAuth');
  };

  const navigateToPrivacyControls = () => {
    console.log('👁️ Navigating to Privacy Controls');
    navigation.navigate('PrivacyControls');
  };

  const openCountrySelector = () => {
    console.log('🌍 Opening country selector');
    setShowCountrySelector(true);
  };

  const openLanguageSelector = () => {
    console.log('🗣️ Opening language selector');
    setShowLanguageSelector(true);
  };

  const openCurrencySelector = () => {
    console.log('💰 Opening currency selector');
    setShowCurrencySelector(true);
  };

  const selectCountry = (country) => {
    console.log('🌍 Selected country:', country);
    setSelectedCountry(country);
    setShowCountrySelector(false);
    setHasChanges(true);
  };

  const selectLanguage = (language) => {
    console.log('🗣️ Selected language:', language);
    setSelectedLanguage(language);
    setShowLanguageSelector(false);
    setHasChanges(true);
  };

  const selectCurrency = (currency) => {
    console.log('💰 Selected currency:', currency);
    setSelectedCurrency(currency);
    setShowCurrencySelector(false);
    setHasChanges(true);
  };

  if (loading) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fcf7f0'
      }}>
        <ActivityIndicator size="large" color="#E67E22" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: '#2C3E50'
        }}>
          Loading Profile...
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: '#fcf7f0' }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={{
        paddingTop: Platform.OS === 'ios' ? 50 : 30,
        paddingBottom: 20,
        paddingHorizontal: 20,
        backgroundColor: '#E67E22'
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              justifyContent: 'center',
              alignItems: 'center'
            }}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#FFFFFF',
            flex: 1,
            textAlign: 'center',
            marginHorizontal: 16
          }}>
            Edit Profile
          </Text>

          {showDatePicker ? (
            <TouchableOpacity
              style={{
                backgroundColor: '#C0392B',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 20,
                minWidth: 60,
                alignItems: 'center'
              }}
              onPress={forceCloseDatePicker}
              activeOpacity={0.7}
            >
              <Text style={{
                color: '#FFFFFF',
                fontWeight: '600',
                fontSize: 14
              }}>
                Close
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 20,
                minWidth: 60,
                alignItems: 'center',
                opacity: (!hasChanges || saving) ? 0.5 : 1
              }}
              onPress={handleSave}
              disabled={!hasChanges || saving}
              activeOpacity={0.7}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={{
                  color: '#FFFFFF',
                  fontWeight: '600',
                  fontSize: 14
                }}>
                  Save
                </Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Form Content */}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={{ padding: 20 }}>
          {/* Personal Information Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Personal Information
            </Text>

            {/* Full Name */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Full Name *
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: errors.full_name ? '#C0392B' : '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#2C3E50'
                }}
                value={formData.full_name}
                onChangeText={(text) => handleFieldChange('full_name', text)}
                placeholder="Enter your full name"
                placeholderTextColor="#95A5A6"
                autoCapitalize="words"
              />
              {errors.full_name && (
                <Text style={{
                  color: '#C0392B',
                  fontSize: 12,
                  marginTop: 4
                }}>
                  {errors.full_name}
                </Text>
              )}
            </View>

            {/* Email */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Email Address *
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: errors.email ? '#C0392B' : '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#2C3E50'
                }}
                value={formData.email}
                onChangeText={(text) => handleFieldChange('email', text)}
                placeholder="Enter your email address"
                placeholderTextColor="#95A5A6"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && (
                <Text style={{
                  color: '#C0392B',
                  fontSize: 12,
                  marginTop: 4
                }}>
                  {errors.email}
                </Text>
              )}
            </View>

            {/* Phone */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Phone Number
              </Text>
              <TextInput
                style={{
                  backgroundColor: '#F8F9FA',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  fontSize: 16,
                  color: '#95A5A6'
                }}
                value={formData.phone}
                editable={false}
                placeholder="Phone number"
                placeholderTextColor="#95A5A6"
              />
              <Text style={{
                fontSize: 12,
                color: '#95A5A6',
                marginTop: 4
              }}>
                Phone number cannot be changed
              </Text>
            </View>

            {/* Date of Birth */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Date of Birth
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={showDatePickerModal}
                activeOpacity={0.7}
              >
                <Text style={{
                  fontSize: 16,
                  color: formData.date_of_birth ? '#2C3E50' : '#95A5A6'
                }}>
                  {formData.date_of_birth || 'Select date of birth'}
                </Text>
                <Ionicons name="calendar-outline" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Account Settings Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Account Settings
            </Text>

            {/* Country */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Country
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={openCountrySelector}
                activeOpacity={0.7}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>{selectedCountry.flag}</Text>
                  <Text style={{ fontSize: 16, color: '#2C3E50' }}>{selectedCountry.name}</Text>
                </View>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>

            {/* Language Preference */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Language
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={openLanguageSelector}
                activeOpacity={0.7}
              >
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>{selectedLanguage.name}</Text>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>

            {/* Currency Preference */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#2C3E50',
                marginBottom: 8
              }}>
                Preferred Currency
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  borderColor: '#BDC3C7',
                  borderRadius: 12,
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={openCurrencySelector}
                activeOpacity={0.7}
              >
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>{selectedCurrency.symbol} - {selectedCurrency.name}</Text>
                <Ionicons name="chevron-down" size={20} color="#95A5A6" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Security Section */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#2C3E50',
              marginBottom: 16
            }}>
              Security & Privacy
            </Text>

            {/* Change Password */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12
              }}
              onPress={navigateToSecuritySettings}
              activeOpacity={0.7}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="shield-checkmark-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Security Settings</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>

            {/* Two-Factor Authentication */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12
              }}
              onPress={navigateToTwoFactorAuth}
              activeOpacity={0.7}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="finger-print-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Two-Factor Authentication</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>

            {/* Privacy Settings */}
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderWidth: 1,
                borderColor: '#BDC3C7',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 14,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
              onPress={navigateToPrivacyControls}
              activeOpacity={0.7}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="eye-off-outline" size={20} color="#E67E22" style={{ marginRight: 12 }} />
                <Text style={{ fontSize: 16, color: '#2C3E50' }}>Privacy Controls</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#95A5A6" />
            </TouchableOpacity>
          </View>

          <View style={{ height: 40 }} />
        </View>
      </ScrollView>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={hideDatePicker}
        >
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)'
            }}
            activeOpacity={1}
            onPress={hideDatePicker}
          >
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 20,
                padding: 20,
                margin: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                minWidth: 300,
                maxWidth: '90%'
              }}
              activeOpacity={1}
              onPress={() => {}} // Prevent modal from closing when tapping inside
            >
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 20
              }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#2C3E50',
                  flex: 1,
                  textAlign: 'center'
                }}>
                  Select Date of Birth
                </Text>
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 0,
                    top: -5,
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#BDC3C7',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                  onPress={hideDatePicker}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontSize: 16,
                    fontWeight: 'bold'
                  }}>
                    ×
                  </Text>
                </TouchableOpacity>
              </View>

              <DateTimePicker
                value={selectedDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateChange}
                maximumDate={new Date()}
                minimumDate={new Date(1900, 0, 1)}
              />

              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 20
              }}>
                <TouchableOpacity
                  style={{
                    backgroundColor: '#BDC3C7',
                    paddingHorizontal: 20,
                    paddingVertical: 12,
                    borderRadius: 8,
                    flex: 1,
                    marginRight: 10
                  }}
                  onPress={hideDatePicker}
                  activeOpacity={0.7}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontWeight: '600',
                    textAlign: 'center',
                    fontSize: 16
                  }}>
                    Cancel
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    backgroundColor: '#E67E22',
                    paddingHorizontal: 20,
                    paddingVertical: 12,
                    borderRadius: 8,
                    flex: 1,
                    marginLeft: 10
                  }}
                  onPress={() => {
                    console.log('📅 Confirming date:', selectedDate);
                    const formattedDate = selectedDate.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    });
                    handleFieldChange('date_of_birth', formattedDate);
                    hideDatePicker();
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontWeight: '600',
                    textAlign: 'center',
                    fontSize: 16
                  }}>
                    Confirm
                  </Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}

      {/* Country Selector Modal */}
      {showCountrySelector && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showCountrySelector}
          onRequestClose={() => setShowCountrySelector(false)}
        >
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)'
            }}
            activeOpacity={1}
            onPress={() => setShowCountrySelector(false)}
          >
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 20,
                padding: 20,
                margin: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                minWidth: 300,
                maxWidth: '90%',
                maxHeight: '70%'
              }}
              activeOpacity={1}
              onPress={() => {}}
            >
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 20
              }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#2C3E50',
                  flex: 1,
                  textAlign: 'center'
                }}>
                  Select Country
                </Text>
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 0,
                    top: -5,
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#BDC3C7',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                  onPress={() => setShowCountrySelector(false)}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontSize: 16,
                    fontWeight: 'bold'
                  }}>
                    ×
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView style={{ maxHeight: 300 }}>
                {countries.map((country) => (
                  <TouchableOpacity
                    key={country.code}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      marginBottom: 8,
                      backgroundColor: selectedCountry.code === country.code ? '#E67E22' : '#F8F9FA'
                    }}
                    onPress={() => selectCountry(country)}
                    activeOpacity={0.7}
                  >
                    <Text style={{ fontSize: 24, marginRight: 12 }}>{country.flag}</Text>
                    <Text style={{
                      fontSize: 16,
                      color: selectedCountry.code === country.code ? '#FFFFFF' : '#2C3E50',
                      fontWeight: selectedCountry.code === country.code ? '600' : 'normal'
                    }}>
                      {country.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}

      {/* Language Selector Modal */}
      {showLanguageSelector && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showLanguageSelector}
          onRequestClose={() => setShowLanguageSelector(false)}
        >
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)'
            }}
            activeOpacity={1}
            onPress={() => setShowLanguageSelector(false)}
          >
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 20,
                padding: 20,
                margin: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                minWidth: 300,
                maxWidth: '90%',
                maxHeight: '70%'
              }}
              activeOpacity={1}
              onPress={() => {}}
            >
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 20
              }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#2C3E50',
                  flex: 1,
                  textAlign: 'center'
                }}>
                  Select Language
                </Text>
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 0,
                    top: -5,
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#BDC3C7',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                  onPress={() => setShowLanguageSelector(false)}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontSize: 16,
                    fontWeight: 'bold'
                  }}>
                    ×
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView style={{ maxHeight: 300 }}>
                {languages.map((language) => (
                  <TouchableOpacity
                    key={language.code}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      marginBottom: 8,
                      backgroundColor: selectedLanguage.code === language.code ? '#E67E22' : '#F8F9FA'
                    }}
                    onPress={() => selectLanguage(language)}
                    activeOpacity={0.7}
                  >
                    <Text style={{
                      fontSize: 16,
                      color: selectedLanguage.code === language.code ? '#FFFFFF' : '#2C3E50',
                      fontWeight: selectedLanguage.code === language.code ? '600' : 'normal'
                    }}>
                      {language.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}

      {/* Currency Selector Modal */}
      {showCurrencySelector && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showCurrencySelector}
          onRequestClose={() => setShowCurrencySelector(false)}
        >
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)'
            }}
            activeOpacity={1}
            onPress={() => setShowCurrencySelector(false)}
          >
            <TouchableOpacity
              style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 20,
                padding: 20,
                margin: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                minWidth: 300,
                maxWidth: '90%',
                maxHeight: '70%'
              }}
              activeOpacity={1}
              onPress={() => {}}
            >
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 20
              }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#2C3E50',
                  flex: 1,
                  textAlign: 'center'
                }}>
                  Select Currency
                </Text>
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 0,
                    top: -5,
                    width: 30,
                    height: 30,
                    borderRadius: 15,
                    backgroundColor: '#BDC3C7',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                  onPress={() => setShowCurrencySelector(false)}
                >
                  <Text style={{
                    color: '#FFFFFF',
                    fontSize: 16,
                    fontWeight: 'bold'
                  }}>
                    ×
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView style={{ maxHeight: 300 }}>
                {currencies.map((currency) => (
                  <TouchableOpacity
                    key={currency.code}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      marginBottom: 8,
                      backgroundColor: selectedCurrency.code === currency.code ? '#E67E22' : '#F8F9FA'
                    }}
                    onPress={() => selectCurrency(currency)}
                    activeOpacity={0.7}
                  >
                    <Text style={{ fontSize: 20, marginRight: 12 }}>{currency.flag}</Text>
                    <View style={{ flex: 1 }}>
                      <Text style={{
                        fontSize: 16,
                        color: selectedCurrency.code === currency.code ? '#FFFFFF' : '#2C3E50',
                        fontWeight: selectedCurrency.code === currency.code ? '600' : 'normal'
                      }}>
                        {currency.symbol} - {currency.name}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}
    </KeyboardAvoidingView>
  );
};

export default EditProfileScreen;
