/**
 * Enhanced Dashboard Screen
 * Comprehensive analytics dashboard with real-time financial insights
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

// Components
import {
  SpendingTrendChart,
  CategorySpendingChart,
  MonthlyComparisonChart,
  SavingsProgressChart,
  InvestmentPerformanceChart,
  ChartCard
} from '../components/charts/InteractiveCharts';

// Hooks and Services
import { useRealTimeAnalytics } from '../hooks/useRealTimeAnalytics';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import authService from '../services/authService';

// Constants
import { Colors } from '../constants/Colors';

// Utils
import { formatCurrency as formatCurrencyUtil } from '../utils/currencyUtils';

const { width } = Dimensions.get('window');

const EnhancedDashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { formatAmount } = useCurrencyContext();
  const [user, setUser] = useState(null);
  
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [refreshing, setRefreshing] = useState(false);
  const [exportingChart, setExportingChart] = useState(null);

  // Real-time analytics hook
  const {
    analytics,
    loading,
    error,
    lastUpdated,
    isConnected,
    refresh,
    summary,
    wallet,
    transactions,
    savings,
    investments,
    insights,
    getSpendingTrendData,
    getCategoryData,
    getSavingsProgressData,
    getInvestmentPerformanceData
  } = useRealTimeAnalytics(selectedPeriod);

  const styles = createStyles(theme);

  /**
   * Helper function to format currency with fallback
   */
  const formatCurrencyValue = useCallback((amount, currency = 'UGX') => {
    try {
      // Use the imported utility function directly
      return formatCurrencyUtil(amount, currency);
    } catch (error) {
      console.error('❌ Error formatting currency:', error);
      return `${currency} ${(amount || 0).toLocaleString()}`;
    }
  }, []);

  /**
   * Get current user on mount
   */
  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, []);

  /**
   * Handle refresh
   */
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await refresh();
    setRefreshing(false);
  }, [refresh]);

  /**
   * Handle period change
   */
  const handlePeriodChange = useCallback((period) => {
    setSelectedPeriod(period);
    Haptics.selectionAsync();
  }, []);

  /**
   * Handle chart export
   */
  const handleChartExport = useCallback(async (chartType) => {
    try {
      setExportingChart(chartType);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Navigate to export screen with chart data
      navigation.navigate('AnalyticsExport', {
        chartType,
        data: analytics,
        period: selectedPeriod
      });
    } catch (error) {
      console.error('❌ Error exporting chart:', error);
      Alert.alert('Export Error', 'Failed to export chart. Please try again.');
    } finally {
      setExportingChart(null);
    }
  }, [analytics, selectedPeriod, navigation]);

  /**
   * Navigate to detailed analytics
   */
  const navigateToDetails = useCallback((section) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    switch (section) {
      case 'transactions':
        navigation.navigate('TransactionHistory');
        break;
      case 'savings':
        navigation.navigate('SavingsAnalytics');
        break;
      case 'investments':
        navigation.navigate('InvestmentAnalytics');
        break;
      default:
        navigation.navigate('Analytics');
    }
  }, [navigation]);

  /**
   * Render period selector
   */
  const renderPeriodSelector = () => {
    const periods = [
      { key: 'week', label: 'Week', icon: 'calendar-outline' },
      { key: 'month', label: 'Month', icon: 'calendar' },
      { key: 'quarter', label: 'Quarter', icon: 'calendar-sharp' },
      { key: 'year', label: 'Year', icon: 'calendar-clear' }
    ];

    return (
      <View style={styles.periodSelector}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                selectedPeriod === period.key && styles.periodButtonActive
              ]}
              onPress={() => handlePeriodChange(period.key)}
            >
              <Ionicons
                name={period.icon}
                size={16}
                color={selectedPeriod === period.key ? Colors.neutral.white : theme.colors.textSecondary}
              />
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period.key && styles.periodButtonTextActive
                ]}
              >
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  /**
   * Render summary cards
   */
  const renderSummaryCards = () => {
    const cards = [
      {
        title: 'Net Worth',
        value: summary.totalNetWorth || 0,
        icon: 'wallet',
        color: Colors.primary.main,
        onPress: () => navigateToDetails('wallet')
      },
      {
        title: 'Monthly Income',
        value: summary.monthlyIncome || 0,
        icon: 'trending-up',
        color: Colors.secondary.savanna,
        onPress: () => navigateToDetails('transactions')
      },
      {
        title: 'Monthly Expenses',
        value: summary.monthlyExpenses || 0,
        icon: 'trending-down',
        color: Colors.secondary.heritage,
        onPress: () => navigateToDetails('transactions')
      },
      {
        title: 'Savings Rate',
        value: `${(summary.savingsRate || 0).toFixed(1)}%`,
        icon: 'save',
        color: Colors.accent.gold,
        isPercentage: true,
        onPress: () => navigateToDetails('savings')
      }
    ];

    return (
      <View style={styles.summaryContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {cards.map((card, index) => (
            <TouchableOpacity
              key={index}
              style={styles.summaryCard}
              onPress={card.onPress}
            >
              <LinearGradient
                colors={[card.color, card.color + '80']}
                style={styles.summaryCardGradient}
              >
                <View style={styles.summaryCardHeader}>
                  <Ionicons name={card.icon} size={24} color={Colors.neutral.white} />
                  <Text style={styles.summaryCardTitle}>{card.title}</Text>
                </View>
                <Text style={styles.summaryCardValue}>
                  {card.isPercentage ? card.value : formatCurrencyValue(card.value, wallet.currency)}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  /**
   * Render insights section
   */
  const renderInsights = () => {
    if (!insights || insights.length === 0) return null;

    return (
      <View style={styles.insightsSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Financial Insights
        </Text>
        {insights.slice(0, 3).map((insight, index) => (
          <View key={index} style={[styles.insightCard, { backgroundColor: theme.colors.surface }]}>
            <View style={[styles.insightIcon, { backgroundColor: insight.color + '20' }]}>
              <Ionicons name={insight.icon} size={20} color={insight.color} />
            </View>
            <View style={styles.insightContent}>
              <Text style={[styles.insightTitle, { color: theme.colors.text }]}>
                {insight.title}
              </Text>
              <Text style={[styles.insightDescription, { color: theme.colors.textSecondary }]}>
                {insight.description}
              </Text>
              <Text style={[styles.insightAmount, { color: insight.color }]}>
                {formatCurrencyValue(insight.amount, wallet.currency)}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Render predictive analytics section
   */
  const renderPredictiveAnalytics = () => {
    const forecasts = analytics?.predictiveForecasts;

    if (!forecasts || !Array.isArray(forecasts.forecasts) || forecasts.forecasts.length === 0) {
      return null;
    }

    const nextMonthForecast = forecasts.forecasts[0];
    const confidence = (nextMonthForecast.confidence * 100).toFixed(0);

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Ionicons name="trending-up" size={24} color={Colors.primary.main} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Spending Forecast
          </Text>
        </View>

        <View style={styles.forecastCard}>
          <View style={styles.forecastHeader}>
            <Text style={[styles.forecastLabel, { color: theme.colors.textSecondary }]}>
              Next Month Prediction
            </Text>
            <View style={[styles.confidenceBadge, { backgroundColor: Colors.primary.main + '20' }]}>
              <Text style={[styles.confidenceText, { color: Colors.primary.main }]}>
                {confidence}% confidence
              </Text>
            </View>
          </View>

          <Text style={[styles.forecastAmount, { color: theme.colors.text }]}>
            {formatCurrencyValue(nextMonthForecast.predicted, wallet?.currency || 'UGX')}
          </Text>

          <View style={styles.forecastRange}>
            <Text style={[styles.rangeText, { color: theme.colors.textSecondary }]}>
              Range: {formatCurrencyValue(nextMonthForecast.lowerBound, wallet?.currency || 'UGX')} - {formatCurrencyValue(nextMonthForecast.upperBound, wallet?.currency || 'UGX')}
            </Text>
          </View>
        </View>

        {forecasts.forecasts.length > 1 && (
          <TouchableOpacity
            style={styles.viewMoreButton}
            onPress={() => navigation.navigate('SpendingForecasts', { forecasts })}
          >
            <Text style={[styles.viewMoreText, { color: Colors.primary.main }]}>
              View 3-Month Forecast
            </Text>
            <Ionicons name="chevron-forward" size={16} color={Colors.primary.main} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  /**
   * Render budget analytics section
   */
  const renderBudgetAnalytics = () => {
    const budgetData = analytics?.budgetAnalytics;

    if (!budgetData || budgetData.totalBudgets === 0) {
      return (
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeader}>
            <Ionicons name="wallet" size={24} color={Colors.secondary.heritage} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Budget Management
            </Text>
          </View>

          <View style={styles.emptyBudgetCard}>
            <Ionicons name="add-circle-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyBudgetTitle, { color: theme.colors.text }]}>
              Create Your First Budget
            </Text>
            <Text style={[styles.emptyBudgetText, { color: theme.colors.textSecondary }]}>
              Start tracking your spending with AI-powered budget suggestions
            </Text>
            <TouchableOpacity
              style={[styles.createBudgetButton, { backgroundColor: Colors.secondary.heritage }]}
              onPress={() => navigation.navigate('BudgetManagement')}
            >
              <Text style={styles.createBudgetButtonText}>Create Budget</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    const utilizationRate = budgetData.averageUtilization * 100;
    const getUtilizationColor = () => {
      if (utilizationRate > 100) return Colors.status.error;
      if (utilizationRate > 80) return Colors.status.warning;
      return Colors.status.success;
    };

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Ionicons name="wallet" size={24} color={Colors.secondary.heritage} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Budget Overview
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('BudgetManagement')}>
            <Text style={[styles.viewAllText, { color: Colors.secondary.heritage }]}>
              View All
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.budgetSummary}>
          <View style={styles.budgetMetric}>
            <Text style={[styles.budgetMetricLabel, { color: theme.colors.textSecondary }]}>
              Total Budgeted
            </Text>
            <Text style={[styles.budgetMetricValue, { color: theme.colors.text }]}>
              {formatCurrencyValue(budgetData.totalBudgeted, wallet?.currency || 'UGX')}
            </Text>
          </View>

          <View style={styles.budgetMetric}>
            <Text style={[styles.budgetMetricLabel, { color: theme.colors.textSecondary }]}>
              Total Spent
            </Text>
            <Text style={[styles.budgetMetricValue, { color: getUtilizationColor() }]}>
              {formatCurrencyValue(budgetData.totalSpent, wallet?.currency || 'UGX')}
            </Text>
          </View>
        </View>

        <View style={styles.budgetProgress}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: theme.colors.text }]}>
              Overall Utilization
            </Text>
            <Text style={[styles.progressPercentage, { color: getUtilizationColor() }]}>
              {utilizationRate.toFixed(1)}%
            </Text>
          </View>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${Math.min(utilizationRate, 100)}%`,
                  backgroundColor: getUtilizationColor()
                }
              ]}
            />
          </View>
        </View>

        {budgetData.categoriesOverBudget > 0 && (
          <View style={[styles.alertCard, { backgroundColor: Colors.status.error + '10' }]}>
            <Ionicons name="warning" size={20} color={Colors.status.error} />
            <Text style={[styles.alertText, { color: Colors.status.error }]}>
              {budgetData.categoriesOverBudget} categories are over budget
            </Text>
          </View>
        )}
      </View>
    );
  };

  /**
   * Render budget recommendations section
   */
  const renderBudgetRecommendations = () => {
    const recommendations = analytics?.budgetRecommendations;

    if (!Array.isArray(recommendations) || recommendations.length === 0) {
      return null;
    }

    const highPriorityRecs = recommendations.filter(rec => rec.priority === 'high').slice(0, 2);

    if (highPriorityRecs.length === 0) return null;

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Ionicons name="bulb" size={24} color={Colors.accent.gold} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Budget Recommendations
          </Text>
        </View>

        {highPriorityRecs.map((rec, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.recommendationCard, { backgroundColor: theme.colors.background }]}
            onPress={() => handleRecommendationAction(rec)}
          >
            <View style={[styles.recommendationIcon, { backgroundColor: Colors.accent.gold + '20' }]}>
              <Ionicons
                name={rec.type === 'warning' ? 'warning' : 'bulb'}
                size={16}
                color={Colors.accent.gold}
              />
            </View>
            <View style={styles.recommendationContent}>
              <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>
                {rec.title}
              </Text>
              <Text style={[styles.recommendationMessage, { color: theme.colors.textSecondary }]}>
                {rec.message}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  /**
   * Handle recommendation action
   */
  const handleRecommendationAction = (recommendation) => {
    switch (recommendation.action) {
      case 'review_spending':
        navigation.navigate('SpendingAnalysis');
        break;
      case 'adjust_categories':
        navigation.navigate('BudgetManagement');
        break;
      case 'increase_savings':
        navigation.navigate('SavingsGoals');
        break;
      default:
        // Handle other actions
        break;
    }
  };

  /**
   * Render categorization insights section
   */
  const renderCategorizationInsights = () => {
    const categorizationData = analytics?.categorizationInsights;

    if (!categorizationData || typeof categorizationData !== 'object') {
      return null;
    }

    const hasUncategorized = categorizationData.uncategorizedTransactions?.count > 0;
    const hasLowConfidence = categorizationData.lowConfidenceTransactions?.count > 0;
    const hasRecommendations = Array.isArray(categorizationData.recommendations) && categorizationData.recommendations.length > 0;
    const hasAutomationOpportunities = Array.isArray(categorizationData.automationOpportunities) && categorizationData.automationOpportunities.length > 0;

    if (!hasUncategorized && !hasLowConfidence && !hasRecommendations && !hasAutomationOpportunities) {
      return null;
    }

    return (
      <View style={styles.insightsSection}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Smart Categorization
          </Text>
          <TouchableOpacity
            style={styles.sectionAction}
            onPress={() => navigation.navigate('CategoryManagement')}
          >
            <Text style={[styles.sectionActionText, { color: theme.colors.primary }]}>
              Manage
            </Text>
          </TouchableOpacity>
        </View>

        {/* Uncategorized Transactions */}
        {hasUncategorized && (
          <TouchableOpacity
            style={[styles.categorizationCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('BulkCategorization', { filter: 'uncategorized' })}
          >
            <View style={[styles.categorizationIcon, { backgroundColor: Colors.secondary.heritage + '20' }]}>
              <Ionicons name="help-circle" size={20} color={Colors.secondary.heritage} />
            </View>
            <View style={styles.categorizationContent}>
              <Text style={[styles.categorizationTitle, { color: theme.colors.text }]}>
                Uncategorized Transactions
              </Text>
              <Text style={[styles.categorizationDescription, { color: theme.colors.textSecondary }]}>
                {categorizationData.uncategorizedTransactions?.count || 0} transactions need categorization
              </Text>
              <Text style={[styles.categorizationAmount, { color: Colors.secondary.heritage }]}>
                {formatCurrencyValue(categorizationData.uncategorizedTransactions?.totalAmount || 0, wallet?.currency || 'UGX')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}

        {/* Low Confidence Transactions */}
        {hasLowConfidence && (
          <TouchableOpacity
            style={[styles.categorizationCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('BulkCategorization', { filter: 'low_confidence' })}
          >
            <View style={[styles.categorizationIcon, { backgroundColor: Colors.accent.gold + '20' }]}>
              <Ionicons name="warning" size={20} color={Colors.accent.gold} />
            </View>
            <View style={styles.categorizationContent}>
              <Text style={[styles.categorizationTitle, { color: theme.colors.text }]}>
                Review Categories
              </Text>
              <Text style={[styles.categorizationDescription, { color: theme.colors.textSecondary }]}>
                {categorizationData.lowConfidenceTransactions?.count || 0} transactions may be incorrectly categorized
              </Text>
              <Text style={[styles.categorizationAmount, { color: Colors.accent.gold }]}>
                {formatCurrencyValue(categorizationData.lowConfidenceTransactions?.totalAmount || 0, wallet?.currency || 'UGX')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}

        {/* Automation Opportunities */}
        {hasAutomationOpportunities && (
          <TouchableOpacity
            style={[styles.categorizationCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('CategoryInsights')}
          >
            <View style={[styles.categorizationIcon, { backgroundColor: Colors.primary.main + '20' }]}>
              <Ionicons name="flash" size={20} color={Colors.primary.main} />
            </View>
            <View style={styles.categorizationContent}>
              <Text style={[styles.categorizationTitle, { color: theme.colors.text }]}>
                Automation Opportunities
              </Text>
              <Text style={[styles.categorizationDescription, { color: theme.colors.textSecondary }]}>
                {categorizationData.automationOpportunities?.length || 0} patterns detected for auto-categorization
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  /**
   * Render connection status
   */
  const renderConnectionStatus = () => {
    if (loading) return null;

    return (
      <View style={styles.statusBar}>
        <View style={styles.connectionStatus}>
          <View style={[
            styles.connectionDot,
            { backgroundColor: isConnected ? Colors.secondary.savanna : Colors.neutral.warmGray }
          ]} />
          <Text style={[styles.connectionText, { color: theme.colors.textSecondary }]}>
            {isConnected ? 'Live' : 'Offline'}
          </Text>
        </View>
        {lastUpdated && (
          <Text style={[styles.lastUpdatedText, { color: theme.colors.textSecondary }]}>
            Updated {lastUpdated.toLocaleTimeString()}
          </Text>
        )}
      </View>
    );
  };

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={Colors.status.error} />
          <Text style={[styles.errorTitle, { color: theme.colors.text }]}>
            Unable to Load Analytics
          </Text>
          <Text style={[styles.errorMessage, { color: theme.colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={refresh}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Financial Analytics
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            Real-time insights into your finances
          </Text>
        </View>
        <TouchableOpacity
          style={styles.exportAllButton}
          onPress={() => handleChartExport('all')}
        >
          <Ionicons name="download" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {renderConnectionStatus()}

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
      >
        {renderPeriodSelector()}
        {renderSummaryCards()}
        {renderPredictiveAnalytics()}
        {renderBudgetAnalytics()}
        {renderBudgetRecommendations()}
        {renderInsights()}
        {renderCategorizationInsights()}

        {/* Charts Section */}
        <View style={styles.chartsSection}>
          {/* Spending Trend Chart */}
          <ChartCard
            title="Spending Trends"
            subtitle={`Income vs Expenses - ${selectedPeriod}`}
            theme={theme}
            onExport={() => handleChartExport('spending_trend')}
            loading={exportingChart === 'spending_trend'}
          >
            <SpendingTrendChart
              data={getSpendingTrendData()}
              theme={theme}
              onDataPointClick={(data) => console.log('Data point clicked:', data)}
            />
          </ChartCard>

          {/* Category Spending Chart */}
          <ChartCard
            title="Spending by Category"
            subtitle="Where your money goes"
            theme={theme}
            onExport={() => handleChartExport('category_spending')}
            loading={exportingChart === 'category_spending'}
          >
            <CategorySpendingChart
              data={getCategoryData()}
              theme={theme}
              onSliceClick={(data) => console.log('Slice clicked:', data)}
            />
          </ChartCard>

          {/* Monthly Comparison Chart */}
          <ChartCard
            title="Monthly Comparison"
            subtitle="Net cash flow over time"
            theme={theme}
            onExport={() => handleChartExport('monthly_comparison')}
            loading={exportingChart === 'monthly_comparison'}
          >
            <MonthlyComparisonChart
              data={getSpendingTrendData()}
              theme={theme}
              onBarClick={(data) => console.log('Bar clicked:', data)}
            />
          </ChartCard>

          {/* Savings Progress Chart */}
          {savings.totalAccounts > 0 && (
            <ChartCard
              title="Savings Goals Progress"
              subtitle="Track your savings journey"
              theme={theme}
              onExport={() => handleChartExport('savings_progress')}
              loading={exportingChart === 'savings_progress'}
            >
              <SavingsProgressChart
                data={getSavingsProgressData()}
                theme={theme}
              />
            </ChartCard>
          )}

          {/* Investment Performance Chart */}
          {investments.totalPortfolios > 0 && (
            <ChartCard
              title="Investment Performance"
              subtitle="Portfolio returns over time"
              theme={theme}
              onExport={() => handleChartExport('investment_performance')}
              loading={exportingChart === 'investment_performance'}
            >
              <InvestmentPerformanceChart
                data={getInvestmentPerformanceData()}
                theme={theme}
              />
            </ChartCard>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

/**
 * Create styles based on theme
 */
const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  exportAllButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  connectionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  lastUpdatedText: {
    fontSize: 12,
  },
  scrollView: {
    flex: 1,
  },
  periodSelector: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  periodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  periodButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  periodButtonTextActive: {
    color: Colors.neutral.white,
  },
  summaryContainer: {
    paddingVertical: 16,
  },
  summaryCard: {
    width: width * 0.4,
    marginLeft: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  summaryCardGradient: {
    padding: 16,
    minHeight: 100,
    justifyContent: 'space-between',
  },
  summaryCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryCardTitle: {
    marginLeft: 8,
    fontSize: 12,
    fontWeight: '600',
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  summaryCardValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.neutral.white,
  },
  insightsSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 1,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  insightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  insightDescription: {
    fontSize: 12,
    marginBottom: 4,
  },
  insightAmount: {
    fontSize: 14,
    fontWeight: '700',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: Colors.primary.main + '10',
  },
  sectionActionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  categorizationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  categorizationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categorizationContent: {
    flex: 1,
  },
  categorizationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  categorizationDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  categorizationAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  chartsSection: {
    paddingVertical: 8,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 32,
  },
  // Predictive Analytics Styles
  forecastCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  forecastHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  forecastLabel: {
    fontSize: 14,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  forecastAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  forecastRange: {
    marginBottom: 12,
  },
  rangeText: {
    fontSize: 12,
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    paddingVertical: 8,
  },
  viewMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Budget Analytics Styles
  emptyBudgetCard: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyBudgetTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
  },
  emptyBudgetText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  createBudgetButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createBudgetButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  budgetSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  budgetMetric: {
    flex: 1,
  },
  budgetMetricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  budgetMetricValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  budgetProgress: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
  },
  alertText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Recommendation Styles
  recommendationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  recommendationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  recommendationMessage: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default EnhancedDashboardScreen;
