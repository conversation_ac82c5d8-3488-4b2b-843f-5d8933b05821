/**
 * Financial Goal Creation Screen
 * Screen for creating new financial goals with target amounts and dates
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import financialPlanningService from '../services/financialPlanningService';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const FinancialGoalCreationScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Get goal type from params (if navigated from specific screen)
  const { goalType: initialGoalType } = route.params || {};

  // State
  const [goalName, setGoalName] = useState('');
  const [goalType, setGoalType] = useState(initialGoalType || 'savings');
  const [description, setDescription] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [targetDate, setTargetDate] = useState('');
  const [monthlyContribution, setMonthlyContribution] = useState('');
  const [priorityLevel, setPriorityLevel] = useState(3);
  const [linkedSavingsAccount, setLinkedSavingsAccount] = useState(null);
  const [savingsAccounts, setSavingsAccounts] = useState([]);
  const [showGoalTypeModal, setShowGoalTypeModal] = useState(false);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const goalTypes = [
    { key: 'savings', label: 'General Savings', icon: 'wallet', color: '#4ECDC4' },
    { key: 'emergency_fund', label: 'Emergency Fund', icon: 'shield-checkmark', color: '#96CEB4' },
    { key: 'vacation', label: 'Vacation', icon: 'airplane', color: '#FECA57' },
    { key: 'education', label: 'Education', icon: 'school', color: '#A8E6CF' },
    { key: 'home_purchase', label: 'Home Purchase', icon: 'home', color: '#FFB6C1' },
    { key: 'retirement', label: 'Retirement', icon: 'time', color: '#6C5CE7' },
    { key: 'debt_payoff', label: 'Debt Payoff', icon: 'card', color: '#FF6B35' },
    { key: 'investment', label: 'Investment Goal', icon: 'trending-up', color: '#45B7D1' }
  ];

  const priorities = [
    { level: 1, label: 'Low Priority' },
    { level: 2, label: 'Medium-Low' },
    { level: 3, label: 'Medium' },
    { level: 4, label: 'Medium-High' },
    { level: 5, label: 'High Priority' }
  ];

  useEffect(() => {
    loadSavingsAccounts();
  }, []);

  const loadSavingsAccounts = async () => {
    try {
      const userId = await getCurrentUserId();
      if (!userId) return;

      const result = await savingsAccountService.getUserSavingsAccounts(userId, { isActive: true });
      if (result.success) {
        setSavingsAccounts(result.accounts);
      }
    } catch (error) {
      console.error('❌ Error loading savings accounts:', error);
    }
  };

  const calculateRecommendedMonthly = () => {
    if (!targetAmount || !targetDate) return 0;

    const target = parseFloat(targetAmount);
    const current = parseFloat(currentAmount) || 0;
    const remaining = target - current;

    const targetDateObj = new Date(targetDate);
    const now = new Date();
    const monthsRemaining = Math.max(1, Math.ceil((targetDateObj - now) / (1000 * 60 * 60 * 24 * 30)));

    return remaining / monthsRemaining;
  };

  const handleCreateGoal = async () => {
    try {
      // Validation
      if (!goalName.trim()) {
        Alert.alert('Validation Error', 'Please enter a goal name');
        return;
      }

      if (!targetAmount || parseFloat(targetAmount) <= 0) {
        Alert.alert('Validation Error', 'Please enter a valid target amount');
        return;
      }

      if (!targetDate) {
        Alert.alert('Validation Error', 'Please select a target date');
        return;
      }

      const targetDateObj = new Date(targetDate);
      if (targetDateObj <= new Date()) {
        Alert.alert('Validation Error', 'Target date must be in the future');
        return;
      }

      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to create goals');
        return;
      }

      const goalData = {
        goalName: goalName.trim(),
        goalType,
        description: description.trim(),
        targetAmount: parseFloat(targetAmount),
        currentAmount: parseFloat(currentAmount) || 0,
        targetDate,
        monthlyContribution: parseFloat(monthlyContribution) || 0,
        priorityLevel,
        linkedSavingsAccount: linkedSavingsAccount?.id || null,
        goalStrategy: 'manual' // Can be enhanced later
      };

      const result = await financialPlanningService.createFinancialGoal(userId, goalData);

      if (result.success) {
        Alert.alert(
          'Goal Created!',
          `Your ${goalName} goal has been created successfully!`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to create goal');
      }

    } catch (error) {
      console.error('❌ Error creating goal:', error);
      Alert.alert('Error', 'Failed to create goal');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedGoalType = () => {
    return goalTypes.find(type => type.key === goalType) || goalTypes[0];
  };

  const renderGoalTypeSelector = () => (
    <View style={styles.inputGroup}>
      <Text style={styles.inputLabel}>Goal Type</Text>
      <TouchableOpacity 
        style={styles.selectorButton}
        onPress={() => setShowGoalTypeModal(true)}
      >
        <View style={styles.selectedContent}>
          <View style={[styles.goalTypeIcon, { backgroundColor: getSelectedGoalType().color }]}>
            <Ionicons name={getSelectedGoalType().icon} size={16} color={theme.colors.white} />
          </View>
          <Text style={styles.selectedText}>{getSelectedGoalType().label}</Text>
        </View>
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={showGoalTypeModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowGoalTypeModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowGoalTypeModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Goal Type</Text>
            <TouchableOpacity onPress={() => setShowGoalTypeModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {goalTypes.map((type) => (
              <TouchableOpacity
                key={type.key}
                style={[
                  styles.goalTypeOption,
                  goalType === type.key && styles.goalTypeOptionSelected
                ]}
                onPress={() => {
                  setGoalType(type.key);
                  setShowGoalTypeModal(false);
                }}
              >
                <View style={[styles.goalTypeIcon, { backgroundColor: type.color }]}>
                  <Ionicons name={type.icon} size={20} color={theme.colors.white} />
                </View>
                <Text style={styles.goalTypeLabel}>{type.label}</Text>
                {goalType === type.key && (
                  <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  const renderAccountSelector = () => (
    <View style={styles.inputGroup}>
      <Text style={styles.inputLabel}>Link to Savings Account (Optional)</Text>
      <TouchableOpacity 
        style={styles.selectorButton}
        onPress={() => setShowAccountModal(true)}
      >
        {linkedSavingsAccount ? (
          <View style={styles.selectedContent}>
            <View style={styles.accountIcon}>
              <Ionicons name="wallet" size={16} color={theme.colors.primary} />
            </View>
            <Text style={styles.selectedText}>{linkedSavingsAccount.accountName}</Text>
          </View>
        ) : (
          <Text style={styles.placeholderText}>Select savings account</Text>
        )}
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={showAccountModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAccountModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAccountModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Account</Text>
            <TouchableOpacity onPress={() => setShowAccountModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <TouchableOpacity
              style={[
                styles.accountOption,
                !linkedSavingsAccount && styles.accountOptionSelected
              ]}
              onPress={() => {
                setLinkedSavingsAccount(null);
                setShowAccountModal(false);
              }}
            >
              <Text style={styles.accountOptionText}>No linked account</Text>
              {!linkedSavingsAccount && (
                <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
              )}
            </TouchableOpacity>
            
            {savingsAccounts.map((account) => (
              <TouchableOpacity
                key={account.id}
                style={[
                  styles.accountOption,
                  linkedSavingsAccount?.id === account.id && styles.accountOptionSelected
                ]}
                onPress={() => {
                  setLinkedSavingsAccount(account);
                  setShowAccountModal(false);
                }}
              >
                <View style={styles.accountOptionContent}>
                  <Text style={styles.accountOptionName}>{account.accountName}</Text>
                  <Text style={styles.accountOptionBalance}>
                    {formatCurrency(account.currentBalance, account.currency)}
                  </Text>
                </View>
                {linkedSavingsAccount?.id === account.id && (
                  <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  const recommendedMonthly = calculateRecommendedMonthly();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Create Financial Goal</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          {/* Goal Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Goal Name *</Text>
            <TextInput
              style={styles.textInput}
              value={goalName}
              onChangeText={setGoalName}
              placeholder="e.g., Emergency Fund, Vacation to Europe"
              placeholderTextColor={theme.colors.textSecondary}
              maxLength={50}
            />
          </View>

          {/* Goal Type */}
          {renderGoalTypeSelector()}

          {/* Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Describe your goal..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
              maxLength={200}
            />
          </View>

          {/* Target Amount */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Target Amount (UGX) *</Text>
            <TextInput
              style={styles.amountInput}
              value={targetAmount}
              onChangeText={setTargetAmount}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          {/* Current Amount */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Current Amount (UGX)</Text>
            <TextInput
              style={styles.amountInput}
              value={currentAmount}
              onChangeText={setCurrentAmount}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          {/* Target Date */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Target Date *</Text>
            <TextInput
              style={styles.textInput}
              value={targetDate}
              onChangeText={setTargetDate}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={theme.colors.textSecondary}
            />
            <Text style={styles.helpText}>Format: YYYY-MM-DD (e.g., 2024-12-31)</Text>
          </View>

          {/* Monthly Contribution */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Monthly Contribution (UGX)</Text>
            <TextInput
              style={styles.amountInput}
              value={monthlyContribution}
              onChangeText={setMonthlyContribution}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            {recommendedMonthly > 0 && (
              <Text style={styles.recommendationText}>
                💡 Recommended: {formatCurrency(recommendedMonthly, 'UGX')}/month to reach your goal
              </Text>
            )}
          </View>

          {/* Priority Level */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Priority Level</Text>
            <View style={styles.priorityContainer}>
              {priorities.map((priority) => (
                <TouchableOpacity
                  key={priority.level}
                  style={[
                    styles.priorityButton,
                    priorityLevel === priority.level && styles.priorityButtonActive
                  ]}
                  onPress={() => setPriorityLevel(priority.level)}
                >
                  <Text style={[
                    styles.priorityButtonText,
                    priorityLevel === priority.level && styles.priorityButtonTextActive
                  ]}>
                    {priority.level}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            <Text style={styles.helpText}>
              {priorities.find(p => p.level === priorityLevel)?.label}
            </Text>
          </View>

          {/* Link to Savings Account */}
          {renderAccountSelector()}

          {/* Goal Summary */}
          {targetAmount && targetDate && (
            <View style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Goal Summary</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Target:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(parseFloat(targetAmount), 'UGX')}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Current:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(parseFloat(currentAmount) || 0, 'UGX')}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Remaining:</Text>
                <Text style={styles.summaryValue}>
                  {formatCurrency(parseFloat(targetAmount) - (parseFloat(currentAmount) || 0), 'UGX')}
                </Text>
              </View>
              {recommendedMonthly > 0 && (
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Monthly needed:</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
                    {formatCurrency(recommendedMonthly, 'UGX')}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Create Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.createButton,
            (!goalName.trim() || !targetAmount || !targetDate || loading) && styles.createButtonDisabled
          ]}
          onPress={handleCreateGoal}
          disabled={!goalName.trim() || !targetAmount || !targetDate || loading}
        >
          <Text style={styles.createButtonText}>
            {loading ? 'Creating Goal...' : 'Create Goal'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  amountInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
    textAlign: 'right',
  },
  helpText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  recommendationText: {
    fontSize: 12,
    color: theme.colors.primary,
    marginTop: 4,
    fontWeight: '500',
  },
  selectorButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  goalTypeIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  placeholderText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  priorityButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  priorityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  priorityButtonTextActive: {
    color: theme.colors.white,
  },
  summaryCard: {
    backgroundColor: theme.colors.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginTop: 10,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  createButtonDisabled: {
    opacity: 0.5,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  goalTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  goalTypeOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  goalTypeLabel: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
    flex: 1,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  accountOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  accountOptionContent: {
    flex: 1,
  },
  accountOptionText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  accountOptionName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountOptionBalance: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default FinancialGoalCreationScreen;
