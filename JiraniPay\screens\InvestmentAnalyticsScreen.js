/**
 * Investment Analytics Screen
 * Screen for detailed investment analytics and performance metrics
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { formatCurrency } from '../utils/currencyUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentAnalyticsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { portfolioId } = route.params || {};

  // State
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('1M');
  const [showAdvancedAnalytics, setShowAdvancedAnalytics] = useState(false);

  const periods = ['1W', '1M', '3M', '6M', '1Y', 'ALL'];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);

  const loadAnalytics = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Mock analytics data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalytics({
        totalReturn: 12.45,
        totalReturnAmount: 2456.78,
        annualizedReturn: 15.2,
        volatility: 18.5,
        sharpeRatio: 0.82,
        maxDrawdown: -8.3,
        winRate: 67.5,
        bestDay: 3.2,
        worstDay: -2.8,
        totalTrades: 24,
        avgHoldingPeriod: 45,
        sectorAllocation: [
          { sector: 'Technology', percentage: 35, value: 8750 },
          { sector: 'Healthcare', percentage: 20, value: 5000 },
          { sector: 'Finance', percentage: 15, value: 3750 },
          { sector: 'Consumer', percentage: 12, value: 3000 },
          { sector: 'Energy', percentage: 10, value: 2500 },
          { sector: 'Other', percentage: 8, value: 2000 }
        ],
        assetAllocation: [
          { type: 'Stocks', percentage: 70, value: 17500 },
          { type: 'ETFs', percentage: 20, value: 5000 },
          { type: 'Cash', percentage: 10, value: 2500 }
        ]
      });

    } catch (error) {
      console.error('❌ Error loading analytics:', error);
      Alert.alert('Error', 'Failed to load analytics');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadAnalytics(true);
  };

  const handleExportAnalytics = () => {
    Alert.alert(
      'Export Analytics',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'PDF Report', onPress: () => exportAnalyticsPDF() },
        { text: 'Excel Data', onPress: () => exportAnalyticsCSV() }
      ]
    );
  };

  const exportAnalyticsPDF = async () => {
    try {
      setLoading(true);

      if (!analytics) {
        Alert.alert('No Data', 'No analytics data available to export');
        return;
      }

      // Generate HTML content for analytics PDF
      const htmlContent = generateAnalyticsHTML();

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });

      // Share the PDF
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Investment Analytics Report'
        });

        Alert.alert('Export Successful', 'Investment analytics exported as PDF successfully!');
      } else {
        Alert.alert('Export Successful', 'PDF report has been generated successfully!');
      }

    } catch (error) {
      console.error('❌ Error exporting analytics PDF:', error);
      Alert.alert('Export Failed', 'Failed to export analytics as PDF');
    } finally {
      setLoading(false);
    }
  };

  const exportAnalyticsCSV = async () => {
    try {
      setLoading(true);

      if (!analytics) {
        Alert.alert('No Data', 'No analytics data available to export');
        return;
      }

      // Generate CSV content
      const csvContent = generateAnalyticsCSV();

      // Save to device storage
      const fileName = `investment_analytics_${new Date().toISOString().split('T')[0]}.csv`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Share the CSV file
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'Share Investment Analytics (Excel)'
        });

        Alert.alert('Export Successful', 'Investment analytics exported as CSV successfully!');
      } else {
        Alert.alert('Export Successful', 'CSV file has been generated successfully!');
      }

    } catch (error) {
      console.error('❌ Error exporting analytics CSV:', error);
      Alert.alert('Export Failed', 'Failed to export analytics as CSV');
    } finally {
      setLoading(false);
    }
  };

  const generateAnalyticsHTML = () => {
    const currentDate = new Date().toLocaleDateString();

    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { color: #2E7D32; font-size: 24px; font-weight: bold; }
            .subtitle { color: #666; font-size: 14px; margin-top: 5px; }
            .section { margin: 20px 0; }
            .section-title { color: #2E7D32; font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .metric { display: flex; justify-content: space-between; margin: 5px 0; }
            .positive { color: #2E7D32; }
            .negative { color: #D32F2F; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">JiraniPay Investment Analytics</div>
            <div class="subtitle">Period: ${selectedPeriod} • Generated: ${currentDate}</div>
          </div>

          <div class="section">
            <div class="section-title">Performance Metrics</div>
            <div class="metric">
              <span>Total Return:</span>
              <span class="positive">${formatCurrency(analytics.totalReturn || 0, 'UGX')}</span>
            </div>
            <div class="metric">
              <span>Return Percentage:</span>
              <span class="positive">${(analytics.returnPercentage || 0).toFixed(2)}%</span>
            </div>
            <div class="metric">
              <span>Volatility:</span>
              <span>${(analytics.volatility || 12.3).toFixed(1)}%</span>
            </div>
            <div class="metric">
              <span>Sharpe Ratio:</span>
              <span>${(analytics.sharpeRatio || 1.42).toFixed(2)}</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Risk Analysis</div>
            <div class="metric">
              <span>Portfolio Beta:</span>
              <span>${(analytics.beta || 1.05).toFixed(2)}</span>
            </div>
            <div class="metric">
              <span>Max Drawdown:</span>
              <span class="negative">-${(analytics.maxDrawdown || 8.5).toFixed(1)}%</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Asset Allocation</div>
            <table>
              <tr><th>Asset Type</th><th>Allocation</th><th>Value</th></tr>
              ${(analytics.allocation || []).map(item => `
                <tr>
                  <td>${item.type}</td>
                  <td>${item.percentage.toFixed(1)}%</td>
                  <td>${formatCurrency(item.value, 'UGX')}</td>
                </tr>
              `).join('')}
            </table>
          </div>
        </body>
      </html>
    `;
  };

  const generateAnalyticsCSV = () => {
    let csv = 'Metric,Value,Period\n';
    csv += `Total Return,${analytics.totalReturn || 0},${selectedPeriod}\n`;
    csv += `Return Percentage,${(analytics.returnPercentage || 0).toFixed(2)}%,${selectedPeriod}\n`;
    csv += `Volatility,${(analytics.volatility || 12.3).toFixed(1)}%,${selectedPeriod}\n`;
    csv += `Sharpe Ratio,${(analytics.sharpeRatio || 1.42).toFixed(2)},${selectedPeriod}\n`;
    csv += `Portfolio Beta,${(analytics.beta || 1.05).toFixed(2)},${selectedPeriod}\n`;
    csv += `Max Drawdown,-${(analytics.maxDrawdown || 8.5).toFixed(1)}%,${selectedPeriod}\n`;

    if (analytics.allocation) {
      csv += '\nAsset Type,Allocation %,Value\n';
      analytics.allocation.forEach(item => {
        csv += `${item.type},${item.percentage.toFixed(1)},${item.value}\n`;
      });
    }

    return csv;
  };

  const getPerformanceColor = (value) => {
    if (value > 0) return theme.colors.success;
    if (value < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.periodButtonTextActive
            ]}>
              {period}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderPerformanceMetrics = () => (
    <View style={styles.metricsCard}>
      <Text style={styles.cardTitle}>Performance Metrics</Text>
      
      <View style={styles.metricsGrid}>
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Total Return</Text>
          <Text style={[styles.metricValue, { color: getPerformanceColor(analytics.totalReturn) }]}>
            {analytics.totalReturn >= 0 ? '+' : ''}{analytics.totalReturn.toFixed(2)}%
          </Text>
          <Text style={styles.metricSubvalue}>
            {formatCurrency(analytics.totalReturnAmount, 'USD')}
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Annualized Return</Text>
          <Text style={[styles.metricValue, { color: getPerformanceColor(analytics.annualizedReturn) }]}>
            {analytics.annualizedReturn >= 0 ? '+' : ''}{analytics.annualizedReturn.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Volatility</Text>
          <Text style={styles.metricValue}>
            {analytics.volatility.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Sharpe Ratio</Text>
          <Text style={styles.metricValue}>
            {analytics.sharpeRatio.toFixed(2)}
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Max Drawdown</Text>
          <Text style={[styles.metricValue, { color: theme.colors.error }]}>
            {analytics.maxDrawdown.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricLabel}>Win Rate</Text>
          <Text style={styles.metricValue}>
            {analytics.winRate.toFixed(1)}%
          </Text>
        </View>
      </View>
    </View>
  );

  const renderRiskMetrics = () => (
    <View style={styles.riskCard}>
      <Text style={styles.cardTitle}>Risk Analysis</Text>
      
      <View style={styles.riskMetrics}>
        <View style={styles.riskItem}>
          <Text style={styles.riskLabel}>Best Day</Text>
          <Text style={[styles.riskValue, { color: theme.colors.success }]}>
            +{analytics.bestDay.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.riskItem}>
          <Text style={styles.riskLabel}>Worst Day</Text>
          <Text style={[styles.riskValue, { color: theme.colors.error }]}>
            {analytics.worstDay.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.riskItem}>
          <Text style={styles.riskLabel}>Total Trades</Text>
          <Text style={styles.riskValue}>
            {analytics.totalTrades}
          </Text>
        </View>
        
        <View style={styles.riskItem}>
          <Text style={styles.riskLabel}>Avg Holding</Text>
          <Text style={styles.riskValue}>
            {analytics.avgHoldingPeriod} days
          </Text>
        </View>
      </View>
    </View>
  );

  const renderAllocationChart = (title, data, colorKey) => (
    <View style={styles.allocationCard}>
      <Text style={styles.cardTitle}>{title}</Text>
      
      {data.map((item, index) => (
        <View key={index} style={styles.allocationItem}>
          <View style={styles.allocationHeader}>
            <View style={styles.allocationInfo}>
              <View style={[
                styles.allocationDot, 
                { backgroundColor: getColorForIndex(index) }
              ]} />
              <Text style={styles.allocationLabel}>{item[colorKey]}</Text>
            </View>
            <Text style={styles.allocationPercentage}>{item.percentage}%</Text>
          </View>
          
          <View style={styles.allocationBar}>
            <View 
              style={[
                styles.allocationFill,
                { 
                  width: `${item.percentage}%`,
                  backgroundColor: getColorForIndex(index)
                }
              ]}
            />
          </View>
          
          <Text style={styles.allocationValue}>
            {formatCurrency(item.value, 'USD')}
          </Text>
        </View>
      ))}
    </View>
  );

  const getColorForIndex = (index) => {
    const colors = ['#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF6B35', '#6C5CE7'];
    return colors[index % colors.length];
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Investment Analytics</Text>
        <TouchableOpacity onPress={handleExportAnalytics}>
          <Ionicons name="download-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {analytics && (
          <>
            {renderPerformanceMetrics()}
            {renderRiskMetrics()}
            {renderAllocationChart('Sector Allocation', analytics.sectorAllocation, 'sector')}
            {renderAllocationChart('Asset Allocation', analytics.assetAllocation, 'type')}
          </>
        )}
        
        {/* Advanced Analytics */}
        <View style={styles.analyticsCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Advanced Analytics</Text>
            <TouchableOpacity onPress={() => setShowAdvancedAnalytics(!showAdvancedAnalytics)}>
              <Ionicons
                name={showAdvancedAnalytics ? "chevron-up" : "chevron-down"}
                size={20}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          </View>

          {showAdvancedAnalytics && (
            <>
              {/* Risk Analysis */}
              <View style={styles.riskAnalysis}>
                <Text style={styles.sectionTitle}>Risk Analysis</Text>
                <View style={styles.riskMetrics}>
                  <View style={styles.riskItem}>
                    <Text style={styles.riskLabel}>Portfolio Beta</Text>
                    <Text style={styles.riskValue}>{analytics.beta?.toFixed(2) || '1.05'}</Text>
                  </View>
                  <View style={styles.riskItem}>
                    <Text style={styles.riskLabel}>Volatility</Text>
                    <Text style={styles.riskValue}>{analytics.volatility?.toFixed(1) || '12.3'}%</Text>
                  </View>
                  <View style={styles.riskItem}>
                    <Text style={styles.riskLabel}>Sharpe Ratio</Text>
                    <Text style={styles.riskValue}>{analytics.sharpeRatio?.toFixed(2) || '1.42'}</Text>
                  </View>
                  <View style={styles.riskItem}>
                    <Text style={styles.riskLabel}>Max Drawdown</Text>
                    <Text style={[styles.riskValue, { color: theme.colors.error }]}>
                      -{analytics.maxDrawdown?.toFixed(1) || '8.5'}%
                    </Text>
                  </View>
                </View>
              </View>

              {/* Sector Correlation */}
              <View style={styles.correlationAnalysis}>
                <Text style={styles.sectionTitle}>East African Market Correlation</Text>
                <View style={styles.correlationGrid}>
                  <View style={styles.correlationItem}>
                    <Text style={styles.correlationLabel}>Banking Sector</Text>
                    <View style={styles.correlationBar}>
                      <View style={[styles.correlationFill, {
                        width: '75%',
                        backgroundColor: theme.colors.success
                      }]} />
                    </View>
                    <Text style={styles.correlationValue}>0.75</Text>
                  </View>
                  <View style={styles.correlationItem}>
                    <Text style={styles.correlationLabel}>Telecommunications</Text>
                    <View style={styles.correlationBar}>
                      <View style={[styles.correlationFill, {
                        width: '45%',
                        backgroundColor: theme.colors.warning
                      }]} />
                    </View>
                    <Text style={styles.correlationValue}>0.45</Text>
                  </View>
                  <View style={styles.correlationItem}>
                    <Text style={styles.correlationLabel}>Utilities</Text>
                    <View style={styles.correlationBar}>
                      <View style={[styles.correlationFill, {
                        width: '60%',
                        backgroundColor: theme.colors.primary
                      }]} />
                    </View>
                    <Text style={styles.correlationValue}>0.60</Text>
                  </View>
                </View>
              </View>

              {/* Performance Attribution */}
              <View style={styles.attributionAnalysis}>
                <Text style={styles.sectionTitle}>Performance Attribution</Text>
                <View style={styles.attributionItems}>
                  <View style={styles.attributionItem}>
                    <Text style={styles.attributionLabel}>Asset Selection</Text>
                    <Text style={[styles.attributionValue, { color: theme.colors.success }]}>
                      +{analytics.assetSelection?.toFixed(1) || '2.3'}%
                    </Text>
                  </View>
                  <View style={styles.attributionItem}>
                    <Text style={styles.attributionLabel}>Market Timing</Text>
                    <Text style={[styles.attributionValue, { color: theme.colors.error }]}>
                      -{analytics.marketTiming?.toFixed(1) || '0.8'}%
                    </Text>
                  </View>
                  <View style={styles.attributionItem}>
                    <Text style={styles.attributionLabel}>Currency Impact</Text>
                    <Text style={[styles.attributionValue, { color: theme.colors.success }]}>
                      +{analytics.currencyImpact?.toFixed(1) || '1.2'}%
                    </Text>
                  </View>
                  <View style={styles.attributionItem}>
                    <Text style={styles.attributionLabel}>Sector Allocation</Text>
                    <Text style={[styles.attributionValue, { color: theme.colors.success }]}>
                      +{analytics.sectorAllocation?.toFixed(1) || '0.9'}%
                    </Text>
                  </View>
                </View>
              </View>

              {/* Recommendations */}
              <View style={styles.recommendations}>
                <Text style={styles.sectionTitle}>AI-Powered Recommendations</Text>
                <View style={styles.recommendationsList}>
                  <View style={styles.recommendationItem}>
                    <Ionicons name="trending-up" size={16} color={theme.colors.success} />
                    <Text style={styles.recommendationText}>
                      Consider increasing exposure to East African banking sector
                    </Text>
                  </View>
                  <View style={styles.recommendationItem}>
                    <Ionicons name="shield-checkmark" size={16} color={theme.colors.primary} />
                    <Text style={styles.recommendationText}>
                      Portfolio risk level is optimal for your profile
                    </Text>
                  </View>
                  <View style={styles.recommendationItem}>
                    <Ionicons name="swap-horizontal" size={16} color={theme.colors.warning} />
                    <Text style={styles.recommendationText}>
                      Rebalance recommended: Reduce telecom allocation by 5%
                    </Text>
                  </View>
                </View>
              </View>
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  periodSelector: {
    marginBottom: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: theme.colors.white,
  },
  metricsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    marginBottom: 16,
  },
  metricLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  metricSubvalue: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  riskCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  riskMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  riskItem: {
    width: '48%',
    marginBottom: 12,
  },
  riskLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  riskValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  allocationCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  allocationItem: {
    marginBottom: 16,
  },
  allocationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  allocationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  allocationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  allocationLabel: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  allocationPercentage: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '600',
  },
  allocationBar: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  allocationFill: {
    height: '100%',
    borderRadius: 3,
  },
  allocationValue: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  comingSoonCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    marginBottom: 20,
  },
  comingSoonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  comingSoonDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  riskAnalysis: {
    marginTop: 15,
  },
  riskMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  riskItem: {
    width: '48%',
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    alignItems: 'center',
  },
  riskLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  riskValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  correlationAnalysis: {
    marginTop: 20,
  },
  correlationGrid: {
    marginTop: 10,
  },
  correlationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  correlationLabel: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
  },
  correlationBar: {
    width: 80,
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    marginHorizontal: 10,
  },
  correlationFill: {
    height: '100%',
    borderRadius: 3,
  },
  correlationValue: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    width: 30,
    textAlign: 'right',
  },
  attributionAnalysis: {
    marginTop: 20,
  },
  attributionItems: {
    marginTop: 10,
  },
  attributionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  attributionLabel: {
    fontSize: 14,
    color: theme.colors.text,
  },
  attributionValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  recommendations: {
    marginTop: 20,
  },
  recommendationsList: {
    marginTop: 10,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 10,
  },
});

export default InvestmentAnalyticsScreen;
