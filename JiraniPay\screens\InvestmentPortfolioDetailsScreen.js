/**
 * Investment Portfolio Details Screen
 * Screen for viewing detailed portfolio information, holdings, and performance
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentPortfolioDetailsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { portfolioId } = route.params;

  // State
  const [portfolio, setPortfolio] = useState(null);
  const [holdings, setHoldings] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('holdings');

  const tabs = [
    { key: 'holdings', label: 'Holdings' },
    { key: 'transactions', label: 'Transactions' },
    { key: 'performance', label: 'Performance' }
  ];

  useEffect(() => {
    loadPortfolioData();
  }, []);

  const loadPortfolioData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view portfolio');
        navigation.goBack();
        return;
      }

      // Load portfolio details, holdings, and transactions
      const [portfolioResult, holdingsResult, transactionsResult, performanceResult] = await Promise.all([
        investmentPortfolioService.getPortfolioDetails(portfolioId, userId),
        investmentPortfolioService.getPortfolioHoldings(portfolioId, userId),
        investmentPortfolioService.getPortfolioTransactions(portfolioId, { limit: 10 }),
        investmentPortfolioService.calculatePortfolioPerformance(portfolioId)
      ]);

      if (portfolioResult.success) {
        setPortfolio(portfolioResult.portfolio);
      } else {
        Alert.alert('Error', 'Portfolio not found');
        navigation.goBack();
        return;
      }

      if (holdingsResult.success) {
        setHoldings(holdingsResult.holdings);
      }

      if (transactionsResult.success) {
        setTransactions(transactionsResult.transactions);
      }

      if (performanceResult.success) {
        setPerformance(performanceResult.performance);
      }

    } catch (error) {
      console.error('❌ Error loading portfolio data:', error);
      Alert.alert('Error', 'Failed to load portfolio data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadPortfolioData(true);
  };

  const getPortfolioTypeIcon = (portfolioType) => {
    const icons = {
      general: 'briefcase',
      retirement: 'time',
      education: 'school',
      aggressive: 'trending-up',
      conservative: 'shield-checkmark',
      balanced: 'scale'
    };
    return icons[portfolioType] || 'briefcase';
  };

  const getPortfolioTypeColor = (portfolioType) => {
    const colors = {
      general: '#4ECDC4',
      retirement: '#6C5CE7',
      education: '#FECA57',
      aggressive: '#FF6B35',
      conservative: '#96CEB4',
      balanced: '#45B7D1'
    };
    return colors[portfolioType] || '#4ECDC4';
  };

  const getPerformanceColor = (value) => {
    if (value > 0) return theme.colors.success;
    if (value < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderPortfolioHeader = () => (
    <View style={styles.headerCard}>
      <View style={styles.portfolioHeaderContent}>
        <View style={[styles.portfolioIcon, { backgroundColor: getPortfolioTypeColor(portfolio.portfolioType) }]}>
          <Ionicons name={getPortfolioTypeIcon(portfolio.portfolioType)} size={32} color={theme.colors.white} />
        </View>
        <View style={styles.portfolioHeaderInfo}>
          <Text style={styles.portfolioName}>{portfolio.portfolioName}</Text>
          <Text style={styles.portfolioType}>
            {portfolio.portfolioType.charAt(0).toUpperCase() + portfolio.portfolioType.slice(1)} Portfolio
          </Text>
          {portfolio.description && (
            <Text style={styles.portfolioDescription}>{portfolio.description}</Text>
          )}
        </View>
      </View>
      
      <View style={styles.portfolioValue}>
        <Text style={styles.totalValue}>{formatCurrency(portfolio.totalValue, portfolio.currency)}</Text>
        <View style={styles.performanceRow}>
          <Text style={[styles.returnText, { color: getPerformanceColor(portfolio.totalGainsLosses) }]}>
            {portfolio.totalGainsLosses >= 0 ? '+' : ''}{formatCurrency(portfolio.totalGainsLosses, portfolio.currency)}
          </Text>
          <Text style={[styles.returnPercent, { color: getPerformanceColor(portfolio.totalReturn) }]}>
            ({portfolio.totalReturn >= 0 ? '+' : ''}{portfolio.totalReturn.toFixed(2)}%)
          </Text>
        </View>
      </View>
    </View>
  );

  const renderQuickStats = () => (
    <View style={styles.statsCard}>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>{formatCurrency(portfolio.totalInvested, portfolio.currency)}</Text>
        <Text style={styles.statLabel}>Total Invested</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>{formatCurrency(portfolio.cashBalance, portfolio.currency)}</Text>
        <Text style={styles.statLabel}>Cash Balance</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={[styles.statValue, { color: getPerformanceColor(portfolio.dailyReturn) }]}>
          {portfolio.dailyReturn >= 0 ? '+' : ''}{portfolio.dailyReturn.toFixed(2)}%
        </Text>
        <Text style={styles.statLabel}>Today's Return</Text>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <View style={styles.actionsGrid}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('AssetSearch', { portfolioId })}
        >
          <Ionicons name="add" size={20} color={theme.colors.success} />
          <Text style={styles.actionButtonText}>Buy Assets</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Add cash feature will be available soon!')}
        >
          <Ionicons name="wallet" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Add Cash</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Rebalance feature will be available soon!')}
        >
          <Ionicons name="refresh" size={20} color={theme.colors.warning} />
          <Text style={styles.actionButtonText}>Rebalance</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('InvestmentAnalytics', { portfolioId })}
        >
          <Ionicons name="analytics" size={20} color={theme.colors.info} />
          <Text style={styles.actionButtonText}>Analytics</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            selectedTab === tab.key && styles.tabButtonActive
          ]}
          onPress={() => setSelectedTab(tab.key)}
        >
          <Text style={[
            styles.tabButtonText,
            selectedTab === tab.key && styles.tabButtonTextActive
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderHoldingCard = ({ item: holding }) => (
    <TouchableOpacity 
      style={styles.holdingCard}
      onPress={() => navigation.navigate('AssetDetails', { assetId: holding.assetId })}
    >
      <View style={styles.holdingHeader}>
        <View style={styles.holdingInfo}>
          <Text style={styles.holdingSymbol}>{holding.asset?.symbol || 'N/A'}</Text>
          <Text style={styles.holdingName}>{holding.asset?.name || 'Unknown Asset'}</Text>
        </View>
        <View style={styles.holdingActions}>
          <Text style={styles.holdingValue}>{formatCurrency(holding.currentValue, portfolio.currency)}</Text>
          <Text style={[styles.holdingReturn, { color: getPerformanceColor(holding.unrealizedGainLoss) }]}>
            {holding.unrealizedGainLoss >= 0 ? '+' : ''}{formatCurrency(holding.unrealizedGainLoss, portfolio.currency)}
          </Text>
        </View>
      </View>
      
      <View style={styles.holdingDetails}>
        <View style={styles.holdingDetail}>
          <Text style={styles.holdingDetailLabel}>Quantity</Text>
          <Text style={styles.holdingDetailValue}>{holding.quantity.toFixed(4)}</Text>
        </View>
        <View style={styles.holdingDetail}>
          <Text style={styles.holdingDetailLabel}>Avg Cost</Text>
          <Text style={styles.holdingDetailValue}>{formatCurrency(holding.averageCost, portfolio.currency)}</Text>
        </View>
        <View style={styles.holdingDetail}>
          <Text style={styles.holdingDetailLabel}>Weight</Text>
          <Text style={styles.holdingDetailValue}>{holding.portfolioWeight.toFixed(1)}%</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderTransactionCard = ({ item: transaction }) => (
    <View style={styles.transactionCard}>
      <View style={styles.transactionHeader}>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionSymbol}>{transaction.asset?.symbol || 'N/A'}</Text>
          <Text style={styles.transactionType}>
            {transaction.transactionType.toUpperCase()} • {formatDate(transaction.createdAt)}
          </Text>
        </View>
        <View style={styles.transactionAmount}>
          <Text style={[
            styles.transactionValue,
            { color: transaction.transactionType === 'buy' ? theme.colors.error : theme.colors.success }
          ]}>
            {transaction.transactionType === 'buy' ? '-' : '+'}{formatCurrency(transaction.totalAmount, portfolio.currency)}
          </Text>
        </View>
      </View>
      
      <View style={styles.transactionDetails}>
        <Text style={styles.transactionDetailText}>
          {transaction.quantity.toFixed(4)} shares @ {formatCurrency(transaction.price, portfolio.currency)}
        </Text>
      </View>
    </View>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'holdings':
        return holdings.length > 0 ? (
          <FlatList
            data={holdings}
            renderItem={renderHoldingCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="briefcase-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>No holdings yet</Text>
            <TouchableOpacity 
              style={styles.emptyStateButton}
              onPress={() => navigation.navigate('AssetSearch', { portfolioId })}
            >
              <Text style={styles.emptyStateButtonText}>Start Investing</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'transactions':
        return transactions.length > 0 ? (
          <FlatList
            data={transactions}
            renderItem={renderTransactionCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="swap-horizontal-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>No transactions yet</Text>
          </View>
        );
      
      case 'performance':
        return (
          <View style={styles.performanceContent}>
            <Text style={styles.comingSoonText}>
              📊 Detailed performance analytics coming soon!
            </Text>
            <Text style={styles.comingSoonDescription}>
              This section will include charts, performance metrics, and portfolio analysis.
            </Text>
          </View>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading portfolio...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!portfolio) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={theme.colors.error} />
          <Text style={styles.errorText}>Portfolio not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Portfolio Details</Text>
        <TouchableOpacity onPress={() => Alert.alert('Coming Soon', 'Portfolio settings will be available soon!')}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPortfolioHeader()}
        {renderQuickStats()}
        {renderQuickActions()}
        {renderTabBar()}
        <View style={styles.tabContent}>
          {renderTabContent()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  backButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  headerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  portfolioHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  portfolioIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  portfolioHeaderInfo: {
    flex: 1,
  },
  portfolioName: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  portfolioType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  portfolioDescription: {
    fontSize: 12,
    color: theme.colors.text,
    lineHeight: 16,
  },
  portfolioValue: {
    alignItems: 'center',
  },
  totalValue: {
    fontSize: 28,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  performanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  returnText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  returnPercent: {
    fontSize: 12,
    fontWeight: '500',
  },
  statsCard: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  actionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: 8,
  },
  actionButtonText: {
    fontSize: 10,
    color: theme.colors.text,
    marginTop: 4,
    textAlign: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  tabButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  tabButtonText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: theme.colors.white,
  },
  tabContent: {
    minHeight: 200,
  },
  holdingCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  holdingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  holdingInfo: {
    flex: 1,
  },
  holdingSymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  holdingName: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  holdingActions: {
    alignItems: 'flex-end',
  },
  holdingValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  holdingReturn: {
    fontSize: 12,
    fontWeight: '500',
  },
  holdingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  holdingDetail: {
    alignItems: 'center',
  },
  holdingDetailLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  holdingDetailValue: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
  },
  transactionCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionSymbol: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  transactionType: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  transactionValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  transactionDetails: {
    marginTop: 4,
  },
  transactionDetailText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
    marginBottom: 16,
  },
  emptyStateButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  emptyStateButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  performanceContent: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  comingSoonText: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  comingSoonDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default InvestmentPortfolioDetailsScreen;
