/**
 * Market Overview Screen
 * Screen for viewing market indices, trending stocks, and market news
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import marketDataService from '../services/marketDataService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const MarketOverviewScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [marketIndices, setMarketIndices] = useState([]);
  const [trendingStocks, setTrendingStocks] = useState([]);
  const [marketNews, setMarketNews] = useState([]);
  const [marketStatus, setMarketStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('indices');

  const categories = [
    { key: 'indices', label: 'Indices' },
    { key: 'trending', label: 'Trending' },
    { key: 'news', label: 'News' }
  ];

  // Mock data for demonstration
  const mockIndices = [
    { symbol: 'SPY', name: 'S&P 500', price: 4185.47, change: 23.45, changePercent: 0.56, volume: '89.2M' },
    { symbol: 'QQQ', name: 'NASDAQ 100', price: 350.12, change: -5.23, changePercent: -1.47, volume: '45.8M' },
    { symbol: 'DIA', name: 'Dow Jones', price: 340.85, change: 12.34, changePercent: 3.76, volume: '12.1M' },
    { symbol: 'IWM', name: 'Russell 2000', price: 195.67, change: -2.11, changePercent: -1.07, volume: '28.5M' }
  ];

  const mockTrending = [
    { symbol: 'AAPL', name: 'Apple Inc.', price: 175.43, change: 2.34, changePercent: 1.35, volume: '67.2M' },
    { symbol: 'TSLA', name: 'Tesla Inc.', price: 245.67, change: -8.45, changePercent: -3.33, volume: '89.1M' },
    { symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.12, change: 5.67, changePercent: 1.52, volume: '34.5M' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 2456.78, change: 15.23, changePercent: 0.62, volume: '23.8M' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.', price: 3234.56, change: -12.45, changePercent: -0.38, volume: '45.2M' }
  ];

  const mockNews = [
    {
      id: '1',
      title: 'Federal Reserve Announces Interest Rate Decision',
      summary: 'The Fed maintains current rates amid economic uncertainty...',
      source: 'Financial Times',
      publishedAt: new Date().toISOString(),
      category: 'monetary-policy'
    },
    {
      id: '2',
      title: 'Tech Stocks Rally on AI Optimism',
      summary: 'Major technology companies see gains as AI adoption accelerates...',
      source: 'Reuters',
      publishedAt: new Date(Date.now() - 3600000).toISOString(),
      category: 'technology'
    },
    {
      id: '3',
      title: 'Oil Prices Surge on Supply Concerns',
      summary: 'Crude oil futures jump 3% on geopolitical tensions...',
      source: 'Bloomberg',
      publishedAt: new Date(Date.now() - 7200000).toISOString(),
      category: 'commodities'
    }
  ];

  useEffect(() => {
    loadMarketData();
  }, []);

  const loadMarketData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // In a real app, you would fetch from your market data service
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setMarketIndices(mockIndices);
      setTrendingStocks(mockTrending);
      setMarketNews(mockNews);
      setMarketStatus({
        isOpen: true,
        nextOpen: null,
        nextClose: new Date(Date.now() + 6 * 3600000).toISOString() // 6 hours from now
      });

    } catch (error) {
      console.error('❌ Error loading market data:', error);
      Alert.alert('Error', 'Failed to load market data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadMarketData(true);
  };

  const getChangeColor = (change) => {
    if (change > 0) return theme.colors.success;
    if (change < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderMarketStatus = () => (
    <View style={styles.statusCard}>
      <View style={styles.statusHeader}>
        <View style={styles.statusIndicator}>
          <View style={[
            styles.statusDot, 
            { backgroundColor: marketStatus?.isOpen ? theme.colors.success : theme.colors.error }
          ]} />
          <Text style={styles.statusText}>
            Market {marketStatus?.isOpen ? 'Open' : 'Closed'}
          </Text>
        </View>
        <TouchableOpacity onPress={onRefresh}>
          <Ionicons name="refresh" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
      {marketStatus?.nextClose && (
        <Text style={styles.statusSubtext}>
          Closes at {new Date(marketStatus.nextClose).toLocaleTimeString()}
        </Text>
      )}
    </View>
  );

  const renderCategoryTabs = () => (
    <View style={styles.tabBar}>
      {categories.map((category) => (
        <TouchableOpacity
          key={category.key}
          style={[
            styles.tabButton,
            selectedCategory === category.key && styles.tabButtonActive
          ]}
          onPress={() => setSelectedCategory(category.key)}
        >
          <Text style={[
            styles.tabButtonText,
            selectedCategory === category.key && styles.tabButtonTextActive
          ]}>
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderStockCard = ({ item: stock }) => (
    <TouchableOpacity 
      style={styles.stockCard}
      onPress={() => navigation.navigate('AssetDetails', { symbol: stock.symbol })}
    >
      <View style={styles.stockHeader}>
        <View style={styles.stockInfo}>
          <Text style={styles.stockSymbol}>{stock.symbol}</Text>
          <Text style={styles.stockName}>{stock.name}</Text>
        </View>
        <View style={styles.stockPrice}>
          <Text style={styles.priceText}>
            {formatCurrency(stock.price, stock.currency || 'UGX')}
          </Text>
          <View style={styles.changeContainer}>
            <Text style={[styles.changeText, { color: getChangeColor(stock.change) }]}>
              {stock.change >= 0 ? '+' : ''}{formatCurrency(stock.change, stock.currency || 'UGX')}
            </Text>
            <Text style={[styles.changePercent, { color: getChangeColor(stock.change) }]}>
              ({stock.change >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%)
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.stockFooter}>
        <Text style={styles.volumeText}>Vol: {stock.volume}</Text>
        <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const renderNewsCard = ({ item: news }) => (
    <TouchableOpacity 
      style={styles.newsCard}
      onPress={() => Alert.alert('News', news.summary)}
    >
      <View style={styles.newsHeader}>
        <Text style={styles.newsTitle} numberOfLines={2}>{news.title}</Text>
        <View style={styles.newsCategory}>
          <Text style={styles.newsCategoryText}>{news.category.toUpperCase()}</Text>
        </View>
      </View>
      <Text style={styles.newsSummary} numberOfLines={2}>{news.summary}</Text>
      <View style={styles.newsFooter}>
        <Text style={styles.newsSource}>{news.source}</Text>
        <Text style={styles.newsTime}>{formatDate(news.publishedAt)}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderContent = () => {
    switch (selectedCategory) {
      case 'indices':
        return (
          <FlatList
            data={marketIndices}
            renderItem={renderStockCard}
            keyExtractor={(item) => item.symbol}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        );
      
      case 'trending':
        return (
          <FlatList
            data={trendingStocks}
            renderItem={renderStockCard}
            keyExtractor={(item) => item.symbol}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        );
      
      case 'news':
        return (
          <FlatList
            data={marketNews}
            renderItem={renderNewsCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading market data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Market Overview</Text>
        <TouchableOpacity onPress={() => navigation.navigate('AssetSearch')}>
          <Ionicons name="search" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderMarketStatus()}
        {renderCategoryTabs()}
        <View style={styles.contentContainer}>
          {renderContent()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  statusCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  statusSubtext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 4,
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  tabButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  tabButtonText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: theme.colors.white,
  },
  contentContainer: {
    minHeight: 400,
  },
  stockCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stockInfo: {
    flex: 1,
  },
  stockSymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  stockName: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  stockPrice: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    fontWeight: '500',
    marginRight: 4,
  },
  changePercent: {
    fontSize: 12,
    fontWeight: '500',
  },
  stockFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  volumeText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  newsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  newsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  newsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    marginRight: 8,
  },
  newsCategory: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  newsCategoryText: {
    fontSize: 8,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  newsSummary: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
    marginBottom: 8,
  },
  newsFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  newsSource: {
    fontSize: 10,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  newsTime: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
});

export default MarketOverviewScreen;
