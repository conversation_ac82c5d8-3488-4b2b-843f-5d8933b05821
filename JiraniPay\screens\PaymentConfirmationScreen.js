/**
 * Payment Confirmation Screen
 * Final confirmation screen showing payment details, fees breakdown,
 * and confirmation before processing
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import billPaymentService from '../services/billPaymentService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const PaymentConfirmationScreen = ({ navigation, route }) => {
  const { paymentData, biller, userId, feeCalculation } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [processing, setProcessing] = useState(false);
  const [confirmed, setConfirmed] = useState(false);

  const processPayment = async () => {
    if (processing) return;

    try {
      setProcessing(true);

      const result = await billPaymentService.processBillPayment(userId, paymentData);

      if (result.success) {
        setConfirmed(true);
        
        // Navigate to payment status tracking
        setTimeout(() => {
          navigation.replace('PaymentStatusTracking', {
            paymentId: result.payment.id,
            paymentReference: result.payment.reference,
            amount: result.payment.amount,
            billerName: biller.displayName
          });
        }, 1500);

      } else {
        Alert.alert(
          'Payment Failed',
          result.error || 'Payment processing failed. Please try again.',
          [
            { text: 'Try Again', onPress: () => setProcessing(false) },
            { text: 'Cancel', onPress: () => navigation.goBack() }
          ]
        );
      }
    } catch (error) {
      console.error('❌ Error processing payment:', error);
      Alert.alert(
        'Payment Error',
        'An error occurred while processing your payment. Please try again.',
        [
          { text: 'Try Again', onPress: () => setProcessing(false) },
          { text: 'Cancel', onPress: () => navigation.goBack() }
        ]
      );
    }
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  const renderPaymentDetails = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Payment Details</Text>
      
      <View style={styles.billerInfo}>
        <View style={styles.billerHeader}>
          <View style={[styles.billerIcon, { backgroundColor: biller.category?.color || theme.colors.primary }]}>
            <Ionicons 
              name={getBillerIcon(biller.category?.name)} 
              size={24} 
              color={theme.colors.white} 
            />
          </View>
          <View style={styles.billerDetails}>
            <Text style={styles.billerName}>{biller.displayName}</Text>
            <Text style={styles.billerCategory}>{biller.category?.displayName}</Text>
          </View>
        </View>
      </View>

      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Account Number</Text>
          <Text style={styles.detailValue}>{paymentData.accountNumber}</Text>
        </View>
        
        {paymentData.accountName && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Account Name</Text>
            <Text style={styles.detailValue}>{paymentData.accountName}</Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Method</Text>
          <Text style={styles.detailValue}>
            {paymentData.paymentMethod === 'wallet' ? 'JiraniPay Wallet' : paymentData.paymentMethod}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Date & Time</Text>
          <Text style={styles.detailValue}>{formatDateTime(new Date())}</Text>
        </View>
      </View>
    </View>
  );

  const renderAmountBreakdown = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Amount Breakdown</Text>
      
      <View style={styles.breakdownContainer}>
        <View style={styles.breakdownRow}>
          <Text style={styles.breakdownLabel}>Payment Amount</Text>
          <Text style={styles.breakdownValue}>
            {formatCurrency(paymentData.amount, paymentData.currency)}
          </Text>
        </View>
        
        <View style={styles.breakdownRow}>
          <Text style={styles.breakdownLabel}>Service Fee</Text>
          <Text style={styles.breakdownValue}>
            {formatCurrency(feeCalculation?.fee || 0, paymentData.currency)}
          </Text>
        </View>
        
        <View style={styles.separator} />
        
        <View style={[styles.breakdownRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total Amount</Text>
          <Text style={styles.totalValue}>
            {formatCurrency(feeCalculation?.totalAmount || paymentData.amount, paymentData.currency)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderSecurityInfo = () => (
    <View style={styles.section}>
      <View style={styles.securityHeader}>
        <Ionicons name="shield-checkmark" size={20} color={theme.colors.success} />
        <Text style={styles.securityTitle}>Secure Payment</Text>
      </View>
      <Text style={styles.securityDescription}>
        Your payment is protected by bank-level security. Transaction details are encrypted and secure.
      </Text>
    </View>
  );

  const renderConfirmationButtons = () => {
    if (confirmed) {
      return (
        <View style={styles.buttonContainer}>
          <View style={styles.successContainer}>
            <Ionicons name="checkmark-circle" size={48} color={theme.colors.success} />
            <Text style={styles.successText}>Payment Confirmed!</Text>
            <Text style={styles.successSubtext}>Processing your payment...</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
          disabled={processing}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.confirmButton, processing && styles.confirmButtonDisabled]}
          onPress={processPayment}
          disabled={processing}
        >
          {processing ? (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="small" color={theme.colors.white} />
              <Text style={styles.confirmButtonText}>Processing...</Text>
            </View>
          ) : (
            <Text style={styles.confirmButtonText}>
              Confirm Payment
            </Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} disabled={processing} />
        <Text style={styles.headerTitle}>Confirm Payment</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPaymentDetails()}
        {renderAmountBreakdown()}
        {renderSecurityInfo()}
      </ScrollView>

      {renderConfirmationButtons()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  billerInfo: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  billerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  billerDetails: {
    flex: 1,
  },
  billerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerCategory: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailsContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
    marginLeft: 16,
  },
  breakdownContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  breakdownLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  breakdownValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: 8,
  },
  totalRow: {
    paddingTop: 12,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  securityDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  confirmButton: {
    flex: 2,
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginLeft: 8,
  },
  confirmButtonDisabled: {
    opacity: 0.7,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
  },
  successText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.success,
    marginTop: 12,
  },
  successSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
});

export default PaymentConfirmationScreen;
