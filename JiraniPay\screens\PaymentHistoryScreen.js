/**
 * Payment History Screen
 * Screen for viewing payment history with filtering, search, export options,
 * and detailed payment information
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import paymentHistoryService from '../services/paymentHistoryService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate, formatDateTime } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const PaymentHistoryScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [payments, setPayments] = useState([]);
  const [filteredPayments, setFilteredPayments] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [pagination, setPagination] = useState({ limit: 20, offset: 0, hasMore: true });

  const filterOptions = [
    { key: 'all', label: 'All Payments', icon: 'list-outline' },
    { key: 'completed', label: 'Completed', icon: 'checkmark-circle-outline' },
    { key: 'pending', label: 'Pending', icon: 'time-outline' },
    { key: 'failed', label: 'Failed', icon: 'close-circle-outline' },
    { key: 'this_month', label: 'This Month', icon: 'calendar-outline' },
    { key: 'last_month', label: 'Last Month', icon: 'calendar-outline' }
  ];

  useEffect(() => {
    loadPaymentHistory();
  }, []);

  useEffect(() => {
    filterPayments();
  }, [payments, searchQuery, selectedFilter]);

  const loadPaymentHistory = async (isRefresh = false, isLoadMore = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setPagination({ limit: 20, offset: 0, hasMore: true });
      } else if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const userId = await requireAuthentication('load payment history');
      if (!userId) {
        console.log('User not authenticated, cannot load payment history');
        setPayments([]);
        setSummary({ totalCount: 0, totalAmount: 0, completedCount: 0, pendingCount: 0 });
        return;
      }
      const options = {
        limit: pagination.limit,
        offset: isRefresh ? 0 : pagination.offset,
        status: selectedFilter === 'all' ? null : selectedFilter,
        startDate: getFilterStartDate(selectedFilter),
        endDate: getFilterEndDate(selectedFilter)
      };

      const result = await paymentHistoryService.getPaymentHistory(userId, options);

      if (result.success) {
        if (isRefresh) {
          setPayments(result.payments);
        } else if (isLoadMore) {
          setPayments(prev => [...prev, ...result.payments]);
        } else {
          setPayments(result.payments);
        }

        setSummary(result.summary);
        setPagination(prev => ({
          ...prev,
          offset: isRefresh ? result.payments.length : prev.offset + result.payments.length,
          hasMore: result.pagination.hasMore
        }));
      }
    } catch (error) {
      console.error('❌ Error loading payment history:', error);
      Alert.alert('Error', 'Failed to load payment history. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  const filterPayments = () => {
    let filtered = [...payments];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(payment =>
        payment.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.biller.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.accountNumber.includes(searchQuery) ||
        (payment.accountName && payment.accountName.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredPayments(filtered);
  };

  const getFilterStartDate = (filter) => {
    const now = new Date();
    switch (filter) {
      case 'this_month':
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      case 'last_month':
        return new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString();
      default:
        return null;
    }
  };

  const getFilterEndDate = (filter) => {
    const now = new Date();
    switch (filter) {
      case 'last_month':
        return new Date(now.getFullYear(), now.getMonth(), 0).toISOString();
      default:
        return null;
    }
  };

  const onRefresh = () => {
    loadPaymentHistory(true);
  };

  const onLoadMore = () => {
    if (!loadingMore && pagination.hasMore) {
      loadPaymentHistory(false, true);
    }
  };

  const handlePaymentPress = (payment) => {
    navigation.navigate('PaymentReceipt', { paymentId: payment.id });
  };

  const handleExport = async () => {
    try {
      Alert.alert(
        'Export Payment History',
        'Choose export format',
        [
          {
            text: 'CSV',
            onPress: () => exportHistory('csv')
          },
          {
            text: 'JSON',
            onPress: () => exportHistory('json')
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error exporting history:', error);
    }
  };

  const exportHistory = async (format) => {
    try {
      const userId = await requireAuthentication('export payment history');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to export payment history');
        return;
      }

      const result = await paymentHistoryService.exportPaymentHistory(userId, {
        format,
        startDate: getFilterStartDate(selectedFilter),
        endDate: getFilterEndDate(selectedFilter)
      });

      if (result.success) {
        Alert.alert('Export Successful', `Payment history exported as ${format.toUpperCase()}`);
      }
    } catch (error) {
      console.error('❌ Error exporting history:', error);
      Alert.alert('Error', 'Failed to export payment history');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: theme.colors.success,
      pending: theme.colors.warning,
      processing: theme.colors.info,
      failed: theme.colors.error,
      cancelled: theme.colors.textSecondary
    };
    return colors[status] || theme.colors.textSecondary;
  };

  const getStatusIcon = (status) => {
    const icons = {
      completed: 'checkmark-circle',
      pending: 'time',
      processing: 'sync',
      failed: 'close-circle',
      cancelled: 'ban'
    };
    return icons[status] || 'help-circle';
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  const renderSummaryCard = () => {
    if (!summary) return null;

    return (
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Payment Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{summary.totalCount}</Text>
            <Text style={styles.summaryLabel}>Total Payments</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{formatCurrency(summary.totalAmount)}</Text>
            <Text style={styles.summaryLabel}>Total Amount</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{summary.completedCount}</Text>
            <Text style={styles.summaryLabel}>Completed</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{summary.pendingCount}</Text>
            <Text style={styles.summaryLabel}>Pending</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderFilterBar = () => (
    <View style={styles.filterContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {filterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              selectedFilter === filter.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(filter.key)}
          >
            <Ionicons
              name={filter.icon}
              size={16}
              color={selectedFilter === filter.key ? theme.colors.white : theme.colors.textSecondary}
            />
            <Text style={[
              styles.filterButtonText,
              selectedFilter === filter.key && styles.filterButtonTextActive
            ]}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderPaymentItem = ({ item: payment }) => (
    <TouchableOpacity 
      style={styles.paymentCard}
      onPress={() => handlePaymentPress(payment)}
    >
      <View style={styles.paymentHeader}>
        <View style={styles.paymentIcon}>
          <Ionicons 
            name={getBillerIcon(payment.biller.category)} 
            size={20} 
            color={theme.colors.primary} 
          />
        </View>
        
        <View style={styles.paymentInfo}>
          <Text style={styles.billerName}>{payment.biller.name}</Text>
          <Text style={styles.accountNumber}>Account: {payment.accountNumber}</Text>
          <Text style={styles.paymentDate}>{formatDateTime(payment.createdAt)}</Text>
        </View>

        <View style={styles.paymentAmount}>
          <Text style={styles.amountText}>
            {formatCurrency(payment.totalAmount, payment.currency)}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(payment.status) + '20' }]}>
            <Ionicons 
              name={getStatusIcon(payment.status)} 
              size={12} 
              color={getStatusColor(payment.status)} 
            />
            <Text style={[styles.statusText, { color: getStatusColor(payment.status) }]}>
              {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.paymentFooter}>
        <Text style={styles.referenceText}>Ref: {payment.reference}</Text>
        <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="receipt-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Payment History</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? `No payments match "${searchQuery}"`
          : selectedFilter === 'all'
          ? 'You haven\'t made any payments yet'
          : `No ${selectedFilter} payments found`
        }
      </Text>
      {searchQuery && (
        <TouchableOpacity 
          style={styles.clearSearchButton}
          onPress={() => setSearchQuery('')}
        >
          <Text style={styles.clearSearchText}>Clear Search</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderLoadMoreFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.loadMoreContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Text style={styles.loadMoreText}>Loading more payments...</Text>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading payment history...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Payment History</Text>
        <TouchableOpacity onPress={handleExport}>
          <Ionicons name="download-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search payments..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Bar */}
      {renderFilterBar()}

      {/* Summary Card */}
      {renderSummaryCard()}

      {/* Results Count */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Payments List */}
      <FlatList
        data={filteredPayments}
        renderItem={renderPaymentItem}
        keyExtractor={(item) => item.id}
        style={styles.paymentsList}
        contentContainerStyle={filteredPayments.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderLoadMoreFooter}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 10,
  },
  filterContainer: {
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
    marginLeft: 6,
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 12,
    padding: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  resultsHeader: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  resultsCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  paymentsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  paymentCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentInfo: {
    flex: 1,
  },
  billerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  paymentDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  paymentAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  paymentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  referenceText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
  clearSearchButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 16,
  },
  clearSearchText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadMoreText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
});

export default PaymentHistoryScreen;
