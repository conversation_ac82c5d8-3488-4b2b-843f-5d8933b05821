/**
 * Payment Receipt Screen
 * Comprehensive receipt display with QR codes, sharing options, and transaction details
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Share,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import QRCode from 'react-native-qrcode-svg';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import paymentHistoryService from '../services/paymentHistoryService';
import digitalReceiptService from '../services/digitalReceiptService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const PaymentReceiptScreen = ({ navigation, route }) => {
  const { paymentId } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [payment, setPayment] = useState(null);
  const [receipt, setReceipt] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sharing, setSharing] = useState(false);

  useEffect(() => {
    loadReceiptData();
  }, [paymentId]);

  const loadReceiptData = async () => {
    try {
      setLoading(true);
      const userId = await requireAuthentication('load receipt data');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view receipt');
        navigation.goBack();
        return;
      }
      
      // Load payment details and receipt in parallel
      const [paymentResult, receiptResult] = await Promise.all([
        paymentHistoryService.getPaymentDetails(paymentId, userId),
        digitalReceiptService.getReceiptByTransaction(paymentId, userId)
      ]);

      if (paymentResult.success) {
        setPayment(paymentResult.payment);
      }

      if (receiptResult.success) {
        setReceipt(receiptResult.receipt);
      } else {
        // Generate receipt if it doesn't exist
        const generateResult = await digitalReceiptService.generateReceipt(paymentId, userId);
        if (generateResult.success) {
          setReceipt(generateResult.receipt);
        }
      }
    } catch (error) {
      console.error('❌ Error loading receipt data:', error);
      Alert.alert('Error', 'Failed to load receipt. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      setSharing(true);
      const userId = await requireAuthentication('share receipt');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to share receipt');
        return;
      }
      
      const shareResult = await digitalReceiptService.shareReceipt(receipt.id, userId, 'link');
      
      await Share.share({
        message: shareResult.message,
        url: shareResult.shareUrl,
        title: 'JiraniPay Payment Receipt'
      });
    } catch (error) {
      console.error('❌ Error sharing receipt:', error);
      Alert.alert('Error', 'Failed to share receipt. Please try again.');
    } finally {
      setSharing(false);
    }
  };

  const handleDownload = async () => {
    try {
      const userId = await requireAuthentication('download receipt');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to download receipt');
        return;
      }

      const downloadResult = await digitalReceiptService.downloadReceipt(receipt.id, userId, 'pdf');

      if (downloadResult.success) {
        Alert.alert('Success', 'Receipt downloaded successfully');
      }
    } catch (error) {
      console.error('❌ Error downloading receipt:', error);
      Alert.alert('Error', 'Failed to download receipt. Please try again.');
    }
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  const renderReceiptHeader = () => (
    <View style={styles.receiptHeader}>
      <View style={styles.logoContainer}>
        <View style={styles.logo}>
          <Text style={styles.logoText}>JP</Text>
        </View>
        <Text style={styles.companyName}>JiraniPay</Text>
        <Text style={styles.companyTagline}>Digital Payment Receipt</Text>
      </View>
      
      <View style={styles.statusContainer}>
        <View style={[styles.statusBadge, { backgroundColor: theme.colors.success + '20' }]}>
          <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
          <Text style={[styles.statusText, { color: theme.colors.success }]}>
            Payment Successful
          </Text>
        </View>
      </View>
    </View>
  );

  const renderPaymentDetails = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Payment Details</Text>
      
      <View style={styles.billerInfo}>
        <View style={[styles.billerIcon, { backgroundColor: payment.biller.category?.color || theme.colors.primary }]}>
          <Ionicons 
            name={getBillerIcon(payment.biller.category?.name)} 
            size={24} 
            color={theme.colors.white} 
          />
        </View>
        <View style={styles.billerDetails}>
          <Text style={styles.billerName}>{payment.biller.name}</Text>
          <Text style={styles.billerCategory}>{payment.biller.category}</Text>
        </View>
      </View>

      <View style={styles.detailsGrid}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Reference Number</Text>
          <Text style={styles.detailValue}>{payment.reference}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Account Number</Text>
          <Text style={styles.detailValue}>{payment.accountNumber}</Text>
        </View>
        
        {payment.accountName && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Account Name</Text>
            <Text style={styles.detailValue}>{payment.accountName}</Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Method</Text>
          <Text style={styles.detailValue}>
            {payment.paymentMethod === 'wallet' ? 'JiraniPay Wallet' : payment.paymentMethod}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Date & Time</Text>
          <Text style={styles.detailValue}>{formatDateTime(payment.completedAt || payment.createdAt)}</Text>
        </View>
      </View>
    </View>
  );

  const renderAmountBreakdown = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Amount Breakdown</Text>
      
      <View style={styles.amountContainer}>
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Payment Amount</Text>
          <Text style={styles.amountValue}>
            {formatCurrency(payment.amount, payment.currency)}
          </Text>
        </View>
        
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Service Fee</Text>
          <Text style={styles.amountValue}>
            {formatCurrency(payment.fee, payment.currency)}
          </Text>
        </View>
        
        <View style={styles.separator} />
        
        <View style={[styles.amountRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total Paid</Text>
          <Text style={styles.totalValue}>
            {formatCurrency(payment.totalAmount, payment.currency)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderQRCode = () => {
    if (!receipt?.qrCodeData) return null;
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Verification QR Code</Text>
        
        <View style={styles.qrContainer}>
          <QRCode
            value={JSON.stringify(receipt.qrCodeData)}
            size={120}
            color={theme.colors.text}
            backgroundColor={theme.colors.background}
          />
          <Text style={styles.qrDescription}>
            Scan this QR code to verify the authenticity of this receipt
          </Text>
        </View>
      </View>
    );
  };

  const renderReceiptFooter = () => (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        This is a computer-generated receipt and does not require a signature.
      </Text>
      <Text style={styles.footerText}>
        For support, contact <NAME_EMAIL>
      </Text>
      <Text style={styles.footerText}>
        Receipt ID: {receipt?.receiptNumber || payment?.reference}
      </Text>
    </View>
  );

  const renderActionButtons = () => (
    <View style={styles.actionContainer}>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={handleShare}
        disabled={sharing}
      >
        {sharing ? (
          <ActivityIndicator size="small" color={theme.colors.primary} />
        ) : (
          <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
        )}
        <Text style={styles.actionButtonText}>Share</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.actionButton}
        onPress={handleDownload}
      >
        <Ionicons name="download-outline" size={20} color={theme.colors.primary} />
        <Text style={styles.actionButtonText}>Download</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('BillPayment')}
      >
        <Ionicons name="add-circle-outline" size={20} color={theme.colors.primary} />
        <Text style={styles.actionButtonText}>New Payment</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading receipt...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!payment) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.errorContainer}>
          <Ionicons name="receipt-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.errorTitle}>Receipt Not Found</Text>
          <Text style={styles.errorDescription}>
            The payment receipt could not be loaded. Please try again.
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={loadReceiptData}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Payment Receipt</Text>
        <TouchableOpacity onPress={handleShare} disabled={sharing}>
          <Ionicons name="share-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.receiptContainer}>
          {renderReceiptHeader()}
          {renderPaymentDetails()}
          {renderAmountBreakdown()}
          {renderQRCode()}
          {renderReceiptFooter()}
        </View>
      </ScrollView>

      {renderActionButtons()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  errorDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  retryButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  receiptContainer: {
    backgroundColor: theme.colors.surface,
    margin: 20,
    borderRadius: 12,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  receiptHeader: {
    alignItems: 'center',
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    marginBottom: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  logo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.white,
  },
  companyName: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  companyTagline: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  statusContainer: {
    alignItems: 'center',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  billerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  billerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  billerDetails: {
    flex: 1,
  },
  billerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerCategory: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  detailsGrid: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
    marginLeft: 16,
  },
  amountContainer: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 16,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: 8,
  },
  totalRow: {
    paddingTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  qrContainer: {
    alignItems: 'center',
  },
  qrDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 16,
  },
  footer: {
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  footerText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 14,
    marginBottom: 4,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
  },
  actionButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
    marginTop: 4,
  },
});

export default PaymentReceiptScreen;
