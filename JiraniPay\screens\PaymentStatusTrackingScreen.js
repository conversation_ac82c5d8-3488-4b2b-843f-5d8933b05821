/**
 * Payment Status Tracking Screen
 * Real-time payment status tracking with progress indicators and status updates
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import paymentHistoryService from '../services/paymentHistoryService';
import realTimeEventService from '../services/realTimeEventService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';

const PaymentStatusTrackingScreen = ({ navigation, route }) => {
  const { paymentId, paymentReference, amount, billerName } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [payment, setPayment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentStatus, setCurrentStatus] = useState('pending');
  const [statusHistory, setStatusHistory] = useState([]);
  const [progressAnimation] = useState(new Animated.Value(0));

  // Payment status steps
  const statusSteps = [
    { key: 'pending', label: 'Payment Initiated', icon: 'time-outline' },
    { key: 'processing', label: 'Processing Payment', icon: 'sync-outline' },
    { key: 'completed', label: 'Payment Completed', icon: 'checkmark-circle-outline' },
  ];

  const failedSteps = [
    { key: 'pending', label: 'Payment Initiated', icon: 'time-outline' },
    { key: 'processing', label: 'Processing Payment', icon: 'sync-outline' },
    { key: 'failed', label: 'Payment Failed', icon: 'close-circle-outline' },
  ];

  useEffect(() => {
    loadPaymentDetails();
    subscribeToStatusUpdates();
    
    return () => {
      // Cleanup subscription
      if (unsubscribeStatusUpdates) {
        unsubscribeStatusUpdates();
      }
    };
  }, [paymentId]);

  useEffect(() => {
    animateProgress();
  }, [currentStatus]);

  let unsubscribeStatusUpdates = null;

  const loadPaymentDetails = async () => {
    try {
      setLoading(true);
      const userId = await requireAuthentication('load payment details');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view payment status');
        navigation.goBack();
        return;
      }
      const result = await paymentHistoryService.getPaymentDetails(paymentId, userId);
      
      if (result.success) {
        setPayment(result.payment);
        setCurrentStatus(result.payment.status);
        setStatusHistory(result.payment.statusHistory || []);
      }
    } catch (error) {
      console.error('❌ Error loading payment details:', error);
      Alert.alert('Error', 'Failed to load payment details');
    } finally {
      setLoading(false);
    }
  };

  const subscribeToStatusUpdates = () => {
    unsubscribeStatusUpdates = paymentHistoryService.subscribeToStatusUpdates(
      paymentId,
      (paymentId, newStatus, metadata) => {
        console.log('📊 Payment status updated:', { paymentId, newStatus });
        setCurrentStatus(newStatus);
        
        // Reload payment details to get updated history
        loadPaymentDetails();
        
        // Show completion notification
        if (newStatus === 'completed') {
          setTimeout(() => {
            Alert.alert(
              'Payment Successful!',
              `Your payment of ${formatCurrency(amount)} to ${billerName} was completed successfully.`,
              [
                {
                  text: 'View Receipt',
                  onPress: () => navigation.replace('PaymentReceipt', { paymentId })
                },
                {
                  text: 'Done',
                  onPress: () => navigation.navigate('BillPayment')
                }
              ]
            );
          }, 1000);
        } else if (newStatus === 'failed') {
          setTimeout(() => {
            Alert.alert(
              'Payment Failed',
              metadata.reason || 'Your payment could not be processed. Please try again.',
              [
                {
                  text: 'Try Again',
                  onPress: () => navigation.goBack()
                },
                {
                  text: 'Done',
                  onPress: () => navigation.navigate('BillPayment')
                }
              ]
            );
          }, 1000);
        }
      }
    );
  };

  const animateProgress = () => {
    const steps = currentStatus === 'failed' ? failedSteps : statusSteps;
    const currentIndex = steps.findIndex(step => step.key === currentStatus);
    const progress = currentIndex >= 0 ? (currentIndex + 1) / steps.length : 0;
    
    Animated.timing(progressAnimation, {
      toValue: progress,
      duration: 800,
      useNativeDriver: false,
    }).start();
  };

  const getStepStatus = (stepKey) => {
    const steps = currentStatus === 'failed' ? failedSteps : statusSteps;
    const currentIndex = steps.findIndex(step => step.key === currentStatus);
    const stepIndex = steps.findIndex(step => step.key === stepKey);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  const renderProgressIndicator = () => {
    const steps = currentStatus === 'failed' ? failedSteps : statusSteps;
    
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <Animated.View 
            style={[
              styles.progressFill,
              {
                width: progressAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
                backgroundColor: currentStatus === 'failed' ? theme.colors.error : theme.colors.success
              }
            ]}
          />
        </View>
        
        <View style={styles.stepsContainer}>
          {steps.map((step, index) => {
            const stepStatus = getStepStatus(step.key);
            
            return (
              <View key={step.key} style={styles.stepContainer}>
                <View style={[
                  styles.stepCircle,
                  stepStatus === 'completed' && styles.stepCompleted,
                  stepStatus === 'current' && styles.stepCurrent,
                  currentStatus === 'failed' && step.key === 'failed' && styles.stepFailed
                ]}>
                  {stepStatus === 'completed' ? (
                    <Ionicons name="checkmark" size={16} color={theme.colors.white} />
                  ) : stepStatus === 'current' && currentStatus !== 'failed' ? (
                    <ActivityIndicator size="small" color={theme.colors.white} />
                  ) : step.key === 'failed' && currentStatus === 'failed' ? (
                    <Ionicons name="close" size={16} color={theme.colors.white} />
                  ) : (
                    <Ionicons 
                      name={step.icon} 
                      size={16} 
                      color={stepStatus === 'current' ? theme.colors.white : theme.colors.textSecondary} 
                    />
                  )}
                </View>
                <Text style={[
                  styles.stepLabel,
                  stepStatus === 'current' && styles.stepLabelCurrent,
                  currentStatus === 'failed' && step.key === 'failed' && styles.stepLabelFailed
                ]}>
                  {step.label}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderPaymentSummary = () => (
    <View style={styles.summaryContainer}>
      <Text style={styles.summaryTitle}>Payment Summary</Text>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Amount</Text>
        <Text style={styles.summaryValue}>{formatCurrency(amount)}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Biller</Text>
        <Text style={styles.summaryValue}>{billerName}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Reference</Text>
        <Text style={styles.summaryValue}>{paymentReference}</Text>
      </View>
      
      {payment && (
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Date & Time</Text>
          <Text style={styles.summaryValue}>{formatDateTime(payment.createdAt)}</Text>
        </View>
      )}
    </View>
  );

  const renderStatusHistory = () => {
    if (!statusHistory || statusHistory.length === 0) return null;
    
    return (
      <View style={styles.historyContainer}>
        <Text style={styles.historyTitle}>Status History</Text>
        
        {statusHistory.map((entry, index) => (
          <View key={index} style={styles.historyItem}>
            <View style={styles.historyIcon}>
              <Ionicons 
                name={getStatusIcon(entry.newStatus)} 
                size={16} 
                color={getStatusColor(entry.newStatus)} 
              />
            </View>
            <View style={styles.historyContent}>
              <Text style={styles.historyStatus}>
                {getStatusLabel(entry.newStatus)}
              </Text>
              <Text style={styles.historyTime}>
                {formatDateTime(entry.timestamp)}
              </Text>
              {entry.reason && (
                <Text style={styles.historyReason}>{entry.reason}</Text>
              )}
            </View>
          </View>
        ))}
      </View>
    );
  };

  const getStatusIcon = (status) => {
    const icons = {
      pending: 'time-outline',
      processing: 'sync-outline',
      completed: 'checkmark-circle-outline',
      failed: 'close-circle-outline',
      cancelled: 'ban-outline'
    };
    return icons[status] || 'help-circle-outline';
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: theme.colors.warning,
      processing: theme.colors.info,
      completed: theme.colors.success,
      failed: theme.colors.error,
      cancelled: theme.colors.textSecondary
    };
    return colors[status] || theme.colors.textSecondary;
  };

  const getStatusLabel = (status) => {
    const labels = {
      pending: 'Payment Initiated',
      processing: 'Processing Payment',
      completed: 'Payment Completed',
      failed: 'Payment Failed',
      cancelled: 'Payment Cancelled'
    };
    return labels[status] || status;
  };

  const renderActionButtons = () => {
    if (currentStatus === 'completed') {
      return (
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={() => navigation.navigate('BillPayment')}
          >
            <Text style={styles.secondaryButtonText}>Make Another Payment</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => navigation.replace('PaymentReceipt', { paymentId })}
          >
            <Text style={styles.primaryButtonText}>View Receipt</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (currentStatus === 'failed') {
      return (
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={() => navigation.navigate('BillPayment')}
          >
            <Text style={styles.secondaryButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.primaryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={styles.fullWidthButton}
          onPress={() => navigation.navigate('BillPayment')}
        >
          <Text style={styles.secondaryButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading payment status...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.navigate('BillPayment')}>
          <Ionicons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Status</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderProgressIndicator()}
        {renderPaymentSummary()}
        {renderStatusHistory()}
      </ScrollView>

      {renderActionButtons()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  progressContainer: {
    marginBottom: 32,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    marginBottom: 24,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepCompleted: {
    backgroundColor: theme.colors.success,
  },
  stepCurrent: {
    backgroundColor: theme.colors.primary,
  },
  stepFailed: {
    backgroundColor: theme.colors.error,
  },
  stepLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  stepLabelCurrent: {
    color: theme.colors.text,
    fontWeight: '500',
  },
  stepLabelFailed: {
    color: theme.colors.error,
    fontWeight: '500',
  },
  summaryContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
    marginLeft: 16,
  },
  historyContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  historyIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  historyContent: {
    flex: 1,
  },
  historyStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  historyTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  historyReason: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
    fontStyle: 'italic',
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginLeft: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  secondaryButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  fullWidthButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
});

export default PaymentStatusTrackingScreen;
