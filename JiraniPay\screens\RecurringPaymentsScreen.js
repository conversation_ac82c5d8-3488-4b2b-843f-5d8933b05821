/**
 * Recurring Payments Management Screen
 * UI for managing recurring payments including setup, editing,
 * pausing/resuming, and cancellation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Modal,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import recurringPaymentsService from '../services/recurringPaymentsService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const RecurringPaymentsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [recurringPayments, setRecurringPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadRecurringPayments();
  }, []);

  const loadRecurringPayments = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await requireAuthentication('load recurring payments');
      if (!userId) {
        console.log('User not authenticated, cannot load recurring payments');
        setRecurringPayments([]);
        return;
      }
      const result = await recurringPaymentsService.getUserRecurringPayments(userId, {
        includeInactive: true
      });

      if (result.success) {
        setRecurringPayments(result.recurringPayments);
      }
    } catch (error) {
      console.error('❌ Error loading recurring payments:', error);
      Alert.alert('Error', 'Failed to load recurring payments. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadRecurringPayments(true);
  };

  const handlePaymentPress = async (payment) => {
    try {
      const userId = await requireAuthentication('view payment details');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view payment details');
        return;
      }

      const result = await recurringPaymentsService.getRecurringPaymentDetails(payment.id, userId);

      if (result.success) {
        setSelectedPayment(result.recurringPayment);
        setShowDetailsModal(true);
      }
    } catch (error) {
      console.error('❌ Error loading payment details:', error);
      Alert.alert('Error', 'Failed to load payment details');
    }
  };

  const handleToggleStatus = async (payment) => {
    try {
      setActionLoading(true);
      const userId = await requireAuthentication('toggle payment status');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to manage payments');
        return;
      }

      let result;
      if (payment.isPaused) {
        result = await recurringPaymentsService.resumeRecurringPayment(payment.id, userId);
      } else {
        result = await recurringPaymentsService.pauseRecurringPayment(payment.id, userId);
      }

      if (result.success) {
        loadRecurringPayments();
        Alert.alert(
          'Success',
          `Recurring payment ${payment.isPaused ? 'resumed' : 'paused'} successfully`
        );
      }
    } catch (error) {
      console.error('❌ Error toggling payment status:', error);
      Alert.alert('Error', 'Failed to update payment status');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelPayment = (payment) => {
    Alert.alert(
      'Cancel Recurring Payment',
      `Are you sure you want to cancel the recurring payment for ${payment.biller?.name}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          style: 'destructive',
          onPress: () => confirmCancelPayment(payment)
        }
      ]
    );
  };

  const confirmCancelPayment = async (payment) => {
    try {
      setActionLoading(true);
      const userId = await requireAuthentication('cancel payment');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to cancel payments');
        return;
      }

      const result = await recurringPaymentsService.cancelRecurringPayment(
        payment.id,
        userId,
        'User cancelled'
      );

      if (result.success) {
        loadRecurringPayments();
        setShowDetailsModal(false);
        Alert.alert('Success', 'Recurring payment cancelled successfully');
      }
    } catch (error) {
      console.error('❌ Error cancelling payment:', error);
      Alert.alert('Error', 'Failed to cancel recurring payment');
    } finally {
      setActionLoading(false);
    }
  };

  const getFrequencyLabel = (frequency) => {
    const labels = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly'
    };
    return labels[frequency] || frequency;
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  const renderRecurringPaymentItem = ({ item: payment }) => (
    <TouchableOpacity 
      style={[
        styles.paymentCard,
        !payment.isActive && styles.paymentCardInactive
      ]}
      onPress={() => handlePaymentPress(payment)}
    >
      <View style={styles.paymentHeader}>
        <View style={styles.paymentIcon}>
          <Ionicons 
            name={getBillerIcon(payment.biller?.category)} 
            size={20} 
            color={payment.isActive ? theme.colors.primary : theme.colors.textSecondary} 
          />
        </View>
        
        <View style={styles.paymentInfo}>
          <Text style={[
            styles.paymentName,
            !payment.isActive && styles.inactiveText
          ]}>
            {payment.name}
          </Text>
          <Text style={[
            styles.billerName,
            !payment.isActive && styles.inactiveText
          ]}>
            {payment.biller?.name}
          </Text>
          <Text style={[
            styles.frequencyText,
            !payment.isActive && styles.inactiveText
          ]}>
            {getFrequencyLabel(payment.frequency)} • {formatCurrency(payment.amount)}
          </Text>
        </View>

        <View style={styles.paymentStatus}>
          <Switch
            value={payment.isActive && !payment.isPaused}
            onValueChange={() => handleToggleStatus(payment)}
            disabled={!payment.isActive || actionLoading}
            trackColor={{ 
              false: theme.colors.border, 
              true: theme.colors.success + '40' 
            }}
            thumbColor={
              payment.isActive && !payment.isPaused 
                ? theme.colors.success 
                : theme.colors.textSecondary
            }
          />
        </View>
      </View>

      <View style={styles.paymentFooter}>
        <View style={styles.nextPaymentInfo}>
          <Ionicons name="calendar-outline" size={14} color={theme.colors.textSecondary} />
          <Text style={styles.nextPaymentText}>
            Next: {payment.isActive && !payment.isPaused 
              ? formatDate(payment.nextPaymentDate)
              : payment.isPaused ? 'Paused' : 'Cancelled'
            }
          </Text>
        </View>
        
        <View style={styles.statusBadge}>
          <View style={[
            styles.statusDot,
            { backgroundColor: getStatusColor(payment) }
          ]} />
          <Text style={[styles.statusText, { color: getStatusColor(payment) }]}>
            {getStatusLabel(payment)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const getStatusColor = (payment) => {
    if (!payment.isActive) return theme.colors.error;
    if (payment.isPaused) return theme.colors.warning;
    return theme.colors.success;
  };

  const getStatusLabel = (payment) => {
    if (!payment.isActive) return 'Cancelled';
    if (payment.isPaused) return 'Paused';
    return 'Active';
  };

  const renderDetailsModal = () => {
    if (!selectedPayment) return null;

    return (
      <Modal
        visible={showDetailsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailsModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowDetailsModal(false)}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Recurring Payment Details</Text>
            <TouchableOpacity 
              onPress={() => handleCancelPayment(selectedPayment)}
              disabled={!selectedPayment.isActive}
            >
              <Text style={[
                styles.cancelText,
                !selectedPayment.isActive && styles.disabledText
              ]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Payment Information</Text>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Name</Text>
                <Text style={styles.detailValue}>{selectedPayment.name}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Biller</Text>
                <Text style={styles.detailValue}>{selectedPayment.biller?.name}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Account Number</Text>
                <Text style={styles.detailValue}>{selectedPayment.accountNumber}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Amount</Text>
                <Text style={styles.detailValue}>
                  {formatCurrency(selectedPayment.amount, selectedPayment.currency)}
                </Text>
              </View>
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Schedule</Text>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Frequency</Text>
                <Text style={styles.detailValue}>{getFrequencyLabel(selectedPayment.frequency)}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Start Date</Text>
                <Text style={styles.detailValue}>{formatDate(selectedPayment.startDate)}</Text>
              </View>
              
              {selectedPayment.endDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>End Date</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedPayment.endDate)}</Text>
                </View>
              )}
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Next Payment</Text>
                <Text style={styles.detailValue}>
                  {selectedPayment.isActive && !selectedPayment.isPaused 
                    ? formatDate(selectedPayment.nextPaymentDate)
                    : 'N/A'
                  }
                </Text>
              </View>
            </View>

            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Statistics</Text>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Payments Made</Text>
                <Text style={styles.detailValue}>{selectedPayment.paymentCount || 0}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Failed Attempts</Text>
                <Text style={styles.detailValue}>{selectedPayment.failedAttempts || 0}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status</Text>
                <Text style={[styles.detailValue, { color: getStatusColor(selectedPayment) }]}>
                  {getStatusLabel(selectedPayment)}
                </Text>
              </View>
            </View>

            {selectedPayment.paymentHistory && selectedPayment.paymentHistory.length > 0 && (
              <View style={styles.detailSection}>
                <Text style={styles.detailSectionTitle}>Recent Payments</Text>
                {selectedPayment.paymentHistory.slice(0, 5).map((payment, index) => (
                  <View key={index} style={styles.historyItem}>
                    <Text style={styles.historyDate}>{formatDate(payment.createdAt)}</Text>
                    <Text style={styles.historyAmount}>
                      {formatCurrency(payment.amount)}
                    </Text>
                    <Text style={[
                      styles.historyStatus,
                      { color: getStatusColor({ isActive: payment.status === 'completed' }) }
                    ]}>
                      {payment.status}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="repeat-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Recurring Payments</Text>
      <Text style={styles.emptyDescription}>
        Set up recurring payments to automatically pay your bills on schedule
      </Text>
      <TouchableOpacity 
        style={styles.setupButton}
        onPress={() => navigation.navigate('BillPayment')}
      >
        <Text style={styles.setupButtonText}>Set Up Recurring Payment</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading recurring payments...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Recurring Payments</Text>
        <TouchableOpacity onPress={() => navigation.navigate('BillPayment')}>
          <Ionicons name="add" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Payments List */}
      <FlatList
        data={recurringPayments}
        renderItem={renderRecurringPaymentItem}
        keyExtractor={(item) => item.id}
        style={styles.paymentsList}
        contentContainerStyle={recurringPayments.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {renderDetailsModal()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  paymentsList: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  paymentCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  paymentCardInactive: {
    opacity: 0.7,
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerName: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  frequencyText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  paymentStatus: {
    alignItems: 'flex-end',
  },
  inactiveText: {
    color: theme.colors.border,
  },
  paymentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  nextPaymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextPaymentText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  setupButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  setupButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  cancelText: {
    fontSize: 16,
    color: theme.colors.error,
    fontWeight: '500',
  },
  disabledText: {
    color: theme.colors.border,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  detailSection: {
    marginBottom: 24,
  },
  detailSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'right',
    flex: 1,
    marginLeft: 16,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  historyDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  historyAmount: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
  },
  historyStatus: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
});

export default RecurringPaymentsScreen;
