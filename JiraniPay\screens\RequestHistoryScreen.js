import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import currencyService from '../services/currencyService';
import moneyRequestService from '../services/moneyRequestService';
import { formatRelativeTime } from '../utils/dateUtils';

/**
 * RequestHistoryScreen - View sent and received money requests
 */
const RequestHistoryScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [requests, setRequests] = useState([]);
  const [selectedTab, setSelectedTab] = useState('all'); // all, sent, received

  useEffect(() => {
    loadRequestHistory();
  }, []);

  const loadRequestHistory = async () => {
    try {
      setLoading(true);
      console.log('📋 Loading money request history...');

      // Load real request history from database
      const result = await moneyRequestService.getMoneyRequestHistory({
        limit: 50,
        offset: 0
      });

      if (result.success) {
        setRequests(result.data || []);
        console.log(`✅ Loaded ${result.data?.length || 0} money requests`);
      } else {
        console.error('❌ Failed to load request history:', result.error);
        Alert.alert(
          'Error',
          'Failed to load request history. Please try again.',
          [{ text: 'OK' }]
        );
        setRequests([]);
      }
    } catch (error) {
      console.error('❌ Error loading request history:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRequestHistory();
    setRefreshing(false);
  };

  const getFilteredRequests = () => {
    switch (selectedTab) {
      case 'sent':
        return requests.filter(req => req.type === 'sent');
      case 'received':
        return requests.filter(req => req.type === 'received');
      default:
        return requests;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return Colors.accent.amber;
      case 'approved':
        return Colors.status.success;
      case 'declined':
        return Colors.status.error;
      default:
        return Colors.neutral.warmGray;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'approved':
        return 'checkmark-circle';
      case 'declined':
        return 'close-circle';
      default:
        return 'help-circle-outline';
    }
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Request History</Text>
        <Text style={styles.headerSubtitle}>Sent & received requests</Text>
      </View>
      <View style={styles.headerRight} />
    </View>
  );

  const renderTabs = () => (
    <View style={styles.tabsContainer}>
      {[
        { id: 'all', title: 'All', count: requests.length },
        { id: 'sent', title: 'Sent', count: requests.filter(r => r.type === 'sent').length },
        { id: 'received', title: 'Received', count: requests.filter(r => r.type === 'received').length }
      ].map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
          onPress={() => setSelectedTab(tab.id)}
        >
          <Text style={[styles.tabText, selectedTab === tab.id && styles.activeTabText]}>
            {tab.title}
          </Text>
          {tab.count > 0 && (
            <View style={[styles.tabBadge, selectedTab === tab.id && styles.activeTabBadge]}>
              <Text style={[styles.tabBadgeText, selectedTab === tab.id && styles.activeTabBadgeText]}>
                {tab.count}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderRequestItem = (request) => (
    <View key={request.id} style={styles.requestItem}>
      <View style={styles.requestHeader}>
        <View style={styles.contactInfo}>
          <View style={styles.contactAvatar}>
            <Text style={styles.contactAvatarText}>{request.contact.avatar}</Text>
          </View>
          <View style={styles.contactDetails}>
            <Text style={styles.contactName}>{request.contact.name}</Text>
            <Text style={styles.requestType}>
              {request.type === 'sent' ? 'Request sent' : 'Request received'}
            </Text>
          </View>
        </View>
        <View style={styles.requestStatus}>
          <Ionicons 
            name={getStatusIcon(request.status)} 
            size={20} 
            color={getStatusColor(request.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(request.status) }]}>
            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
          </Text>
        </View>
      </View>

      <View style={styles.requestDetails}>
        <Text style={styles.requestAmount}>
          {currencyService.formatCurrency(request.amount)}
        </Text>
        <Text style={styles.requestReason}>{request.reason.title}</Text>
        {request.note && (
          <Text style={styles.requestNote}>"{request.note}"</Text>
        )}
        <Text style={styles.requestTime}>{formatTimeAgo(request.createdAt)}</Text>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={64} color={Colors.neutral.warmGray} />
      <Text style={styles.emptyStateTitle}>No requests found</Text>
      <Text style={styles.emptyStateSubtitle}>
        {selectedTab === 'sent' 
          ? 'You haven\'t sent any money requests yet'
          : selectedTab === 'received'
          ? 'You haven\'t received any money requests yet'
          : 'Start requesting money from your contacts'
        }
      </Text>
      <TouchableOpacity 
        style={styles.newRequestButton}
        onPress={() => navigation.navigate('RequestMoney')}
      >
        <Ionicons name="add" size={20} color={Colors.neutral.white} />
        <Text style={styles.newRequestButtonText}>New Request</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.neutral.cream} />
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderTabs()}
        
        <View style={styles.requestsList}>
          {getFilteredRequests().length > 0 ? (
            getFilteredRequests().map(renderRequestItem)
          ) : (
            renderEmptyState()
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: Colors.neutral.white,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  activeTab: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  tabText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.neutral.white,
  },
  tabBadge: {
    marginLeft: 8,
    backgroundColor: Colors.neutral.creamDark,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  activeTabBadge: {
    backgroundColor: Colors.neutral.white,
  },
  tabBadgeText: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
    fontWeight: 'bold',
  },
  activeTabBadgeText: {
    color: Colors.primary.main,
  },
  requestsList: {
    paddingHorizontal: 20,
  },
  requestItem: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  requestType: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  requestStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  requestDetails: {
    alignItems: 'flex-start',
  },
  requestAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  requestReason: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '500',
    marginBottom: 4,
  },
  requestNote: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    fontStyle: 'italic',
    marginBottom: 8,
  },
  requestTime: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  newRequestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  newRequestButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
});

export default RequestHistoryScreen;
