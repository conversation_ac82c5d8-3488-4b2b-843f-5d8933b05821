/**
 * Savings Account Creation Screen
 * Comprehensive screen for creating savings accounts with goal setting,
 * automatic transfers, and progress tracking configuration
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  Switch,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsAccountCreationScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Form state
  const [formData, setFormData] = useState({
    accountName: '',
    accountType: 'general',
    description: '',
    targetAmount: '',
    targetDate: null,
    monthlyTarget: '',
    initialDeposit: '',
    autoTransferEnabled: false,
    autoTransferAmount: '',
    autoTransferFrequency: 'monthly',
    autoTransferSourceAccount: 'wallet'
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showAccountTypeModal, setShowAccountTypeModal] = useState(false);
  const [showFrequencyModal, setShowFrequencyModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Account type options
  const accountTypes = [
    { key: 'general', label: 'General Savings', icon: 'wallet', description: 'Flexible savings for any purpose', color: '#4ECDC4' },
    { key: 'goal', label: 'Goal-Based Savings', icon: 'flag', description: 'Save for specific goals with targets', color: '#45B7D1' },
    { key: 'emergency', label: 'Emergency Fund', icon: 'shield-checkmark', description: 'Build your emergency safety net', color: '#FF6B35' },
    { key: 'vacation', label: 'Vacation Fund', icon: 'airplane', description: 'Save for your dream vacation', color: '#96CEB4' },
    { key: 'education', label: 'Education Fund', icon: 'school', description: 'Invest in education and learning', color: '#FECA57' },
    { key: 'retirement', label: 'Retirement Savings', icon: 'time', description: 'Long-term retirement planning', color: '#6C5CE7' }
  ];

  // Transfer frequency options
  const frequencyOptions = [
    { key: 'daily', label: 'Daily', description: 'Transfer every day' },
    { key: 'weekly', label: 'Weekly', description: 'Transfer every week' },
    { key: 'monthly', label: 'Monthly', description: 'Transfer every month' }
  ];

  const validateForm = () => {
    const errors = {};

    if (!formData.accountName.trim()) {
      errors.accountName = 'Account name is required';
    } else if (formData.accountName.trim().length < 2) {
      errors.accountName = 'Account name must be at least 2 characters';
    }

    if (formData.targetAmount && (isNaN(formData.targetAmount) || parseFloat(formData.targetAmount) <= 0)) {
      errors.targetAmount = 'Target amount must be a valid positive number';
    }

    if (formData.monthlyTarget && (isNaN(formData.monthlyTarget) || parseFloat(formData.monthlyTarget) <= 0)) {
      errors.monthlyTarget = 'Monthly target must be a valid positive number';
    }

    if (formData.initialDeposit && (isNaN(formData.initialDeposit) || parseFloat(formData.initialDeposit) < 0)) {
      errors.initialDeposit = 'Initial deposit must be a valid non-negative number';
    }

    if (formData.autoTransferEnabled) {
      if (!formData.autoTransferAmount || isNaN(formData.autoTransferAmount) || parseFloat(formData.autoTransferAmount) <= 0) {
        errors.autoTransferAmount = 'Auto transfer amount is required and must be positive';
      }
    }

    // Goal-based validation
    if (formData.accountType === 'goal' || formData.accountType === 'emergency' || formData.accountType === 'vacation') {
      if (!formData.targetAmount) {
        errors.targetAmount = 'Target amount is required for goal-based savings';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateAccount = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before proceeding');
      return;
    }

    try {
      setLoading(true);
      const userId = await requireAuthentication('create savings account');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to create a savings account');
        return;
      }

      const accountData = {
        accountName: formData.accountName.trim(),
        accountType: formData.accountType,
        description: formData.description.trim(),
        targetAmount: formData.targetAmount ? parseFloat(formData.targetAmount) : null,
        targetDate: formData.targetDate ? formData.targetDate.toISOString().split('T')[0] : null,
        monthlyTarget: formData.monthlyTarget ? parseFloat(formData.monthlyTarget) : null,
        initialDeposit: formData.initialDeposit ? parseFloat(formData.initialDeposit) : 0,
        autoTransferEnabled: formData.autoTransferEnabled,
        autoTransferAmount: formData.autoTransferEnabled && formData.autoTransferAmount ? 
          parseFloat(formData.autoTransferAmount) : null,
        autoTransferFrequency: formData.autoTransferEnabled ? formData.autoTransferFrequency : null,
        autoTransferSourceAccount: formData.autoTransferEnabled ? formData.autoTransferSourceAccount : null,
        currency: 'UGX'
      };

      const result = await savingsAccountService.createSavingsAccount(userId, accountData);

      if (result.success) {
        Alert.alert(
          'Success',
          'Your savings account has been created successfully!',
          [
            {
              text: 'View Account',
              onPress: () => navigation.navigate('SavingsAccountDetails', { 
                accountId: result.account.id 
              })
            },
            {
              text: 'Create Another',
              onPress: () => {
                // Reset form
                setFormData({
                  accountName: '',
                  accountType: 'general',
                  description: '',
                  targetAmount: '',
                  targetDate: null,
                  monthlyTarget: '',
                  initialDeposit: '',
                  autoTransferEnabled: false,
                  autoTransferAmount: '',
                  autoTransferFrequency: 'monthly',
                  autoTransferSourceAccount: 'wallet'
                });
                setValidationErrors({});
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Creation Failed', result.error || 'Failed to create savings account');
      }
    } catch (error) {
      console.error('❌ Error creating savings account:', error);
      Alert.alert('Error', 'Failed to create savings account. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, targetDate: selectedDate }));
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const getSelectedAccountType = () => {
    return accountTypes.find(type => type.key === formData.accountType);
  };

  const getSelectedFrequency = () => {
    return frequencyOptions.find(freq => freq.key === formData.autoTransferFrequency);
  };

  const renderBasicInformation = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Basic Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Account Name *</Text>
        <TextInput
          style={[styles.textInput, validationErrors.accountName && styles.inputError]}
          value={formData.accountName}
          onChangeText={(value) => updateFormData('accountName', value)}
          placeholder="Enter account name"
          placeholderTextColor={theme.colors.textSecondary}
          maxLength={50}
        />
        {validationErrors.accountName && (
          <Text style={styles.errorText}>{validationErrors.accountName}</Text>
        )}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Account Type</Text>
        <TouchableOpacity 
          style={styles.selectButton}
          onPress={() => setShowAccountTypeModal(true)}
        >
          <View style={styles.selectButtonContent}>
            <View style={[styles.typeIcon, { backgroundColor: getSelectedAccountType().color }]}>
              <Ionicons name={getSelectedAccountType().icon} size={20} color={theme.colors.white} />
            </View>
            <View style={styles.typeInfo}>
              <Text style={styles.selectButtonText}>{getSelectedAccountType().label}</Text>
              <Text style={styles.selectButtonDescription}>{getSelectedAccountType().description}</Text>
            </View>
          </View>
          <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Description (Optional)</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          value={formData.description}
          onChangeText={(value) => updateFormData('description', value)}
          placeholder="Describe your savings purpose"
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          numberOfLines={3}
          maxLength={200}
        />
      </View>
    </View>
  );

  const renderGoalSettings = () => {
    const selectedType = getSelectedAccountType();
    const showGoalSettings = ['goal', 'emergency', 'vacation', 'education', 'retirement'].includes(formData.accountType);

    if (!showGoalSettings) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Goal Settings</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Target Amount *</Text>
          <TextInput
            style={[styles.textInput, validationErrors.targetAmount && styles.inputError]}
            value={formData.targetAmount}
            onChangeText={(value) => updateFormData('targetAmount', value)}
            placeholder="Enter target amount"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
          />
          {validationErrors.targetAmount && (
            <Text style={styles.errorText}>{validationErrors.targetAmount}</Text>
          )}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Target Date (Optional)</Text>
          <TouchableOpacity 
            style={styles.selectButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.selectButtonText}>
              {formData.targetDate ? formatDate(formData.targetDate) : 'Select target date'}
            </Text>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Monthly Target (Optional)</Text>
          <TextInput
            style={[styles.textInput, validationErrors.monthlyTarget && styles.inputError]}
            value={formData.monthlyTarget}
            onChangeText={(value) => updateFormData('monthlyTarget', value)}
            placeholder="Enter monthly savings target"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
          />
          {validationErrors.monthlyTarget && (
            <Text style={styles.errorText}>{validationErrors.monthlyTarget}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderInitialDeposit = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Initial Deposit</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Amount (Optional)</Text>
        <TextInput
          style={[styles.textInput, validationErrors.initialDeposit && styles.inputError]}
          value={formData.initialDeposit}
          onChangeText={(value) => updateFormData('initialDeposit', value)}
          placeholder="Enter initial deposit amount"
          placeholderTextColor={theme.colors.textSecondary}
          keyboardType="numeric"
        />
        {validationErrors.initialDeposit && (
          <Text style={styles.errorText}>{validationErrors.initialDeposit}</Text>
        )}
        <Text style={styles.helperText}>
          You can start with any amount or add funds later
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Create Savings Account</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderBasicInformation()}
        {renderGoalSettings()}
        {renderInitialDeposit()}
      </ScrollView>

      {/* Create Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.createButton, loading && styles.createButtonDisabled]}
          onPress={handleCreateAccount}
          disabled={loading}
        >
          <Text style={styles.createButtonText}>
            {loading ? 'Creating Account...' : 'Create Savings Account'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.targetDate || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}

      {/* Account Type Selection Modal */}
      <Modal
        visible={showAccountTypeModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAccountTypeModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAccountTypeModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Account Type</Text>
            <TouchableOpacity onPress={() => setShowAccountTypeModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {accountTypes.map((type) => (
              <TouchableOpacity
                key={type.key}
                style={[
                  styles.typeOption,
                  formData.accountType === type.key && styles.typeOptionSelected
                ]}
                onPress={() => updateFormData('accountType', type.key)}
              >
                <View style={styles.typeOptionContent}>
                  <View style={[styles.typeIcon, { backgroundColor: type.color }]}>
                    <Ionicons name={type.icon} size={24} color={theme.colors.white} />
                  </View>
                  <View style={styles.typeOptionText}>
                    <Text style={[
                      styles.typeOptionLabel,
                      formData.accountType === type.key && styles.typeOptionLabelSelected
                    ]}>
                      {type.label}
                    </Text>
                    <Text style={styles.typeOptionDescription}>{type.description}</Text>
                  </View>
                </View>
                {formData.accountType === type.key && (
                  <Ionicons name="checkmark" size={24} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: 12,
    color: theme.colors.error,
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  selectButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectButtonText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  selectButtonDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  typeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  typeInfo: {
    flex: 1,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  createButtonDisabled: {
    opacity: 0.7,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  typeOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  typeOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeOptionText: {
    marginLeft: 12,
    flex: 1,
  },
  typeOptionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  typeOptionLabelSelected: {
    color: theme.colors.primary,
  },
  typeOptionDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default SavingsAccountCreationScreen;
