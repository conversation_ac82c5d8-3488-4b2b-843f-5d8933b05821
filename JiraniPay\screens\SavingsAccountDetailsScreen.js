/**
 * Savings Account Details Screen
 * Comprehensive screen for viewing savings account details with
 * progress tracking, transaction history, and account management
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate, formatDateTime } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsAccountDetailsScreen = ({ navigation, route }) => {
  const { accountId } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [account, setAccount] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAccountDetails();
  }, [accountId]);

  const loadAccountDetails = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view account details');
        navigation.goBack();
        return;
      }

      const result = await savingsAccountService.getSavingsAccountDetails(accountId, userId);
      
      if (result.success) {
        setAccount(result.account);
      } else {
        Alert.alert('Error', result.error || 'Failed to load account details');
        navigation.goBack();
      }
    } catch (error) {
      console.error('❌ Error loading account details:', error);
      Alert.alert('Error', 'Failed to load account details');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadAccountDetails(true);
  };

  const handleDeposit = () => {
    navigation.navigate('SavingsDeposit', { accountId, accountName: account.accountName });
  };

  const handleWithdraw = () => {
    navigation.navigate('SavingsWithdraw', { accountId, accountName: account.accountName });
  };

  const handleTransactionHistory = () => {
    navigation.navigate('SavingsTransactionHistory', { accountId, accountName: account.accountName });
  };

  const getAccountTypeIcon = (accountType) => {
    const icons = {
      general: 'wallet',
      goal: 'flag',
      emergency: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      retirement: 'time'
    };
    return icons[accountType] || 'wallet';
  };

  const getAccountTypeColor = (accountType) => {
    const colors = {
      general: '#4ECDC4',
      goal: '#45B7D1',
      emergency: '#FF6B35',
      vacation: '#96CEB4',
      education: '#FECA57',
      retirement: '#6C5CE7'
    };
    return colors[accountType] || '#4ECDC4';
  };

  const renderAccountHeader = () => (
    <View style={styles.accountHeader}>
      <View style={styles.accountInfo}>
        <View style={[styles.accountIcon, { backgroundColor: getAccountTypeColor(account.accountType) }]}>
          <Ionicons name={getAccountTypeIcon(account.accountType)} size={24} color={theme.colors.white} />
        </View>
        <View style={styles.accountDetails}>
          <Text style={styles.accountName}>{account.accountName}</Text>
          <Text style={styles.accountType}>{account.accountType.charAt(0).toUpperCase() + account.accountType.slice(1)} Savings</Text>
          <Text style={styles.accountNumber}>Account: {account.accountNumber}</Text>
        </View>
      </View>
      
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceLabel}>Current Balance</Text>
        <Text style={styles.balanceAmount}>{formatCurrency(account.currentBalance, account.currency)}</Text>
        <Text style={styles.availableBalance}>Available: {formatCurrency(account.availableBalance, account.currency)}</Text>
      </View>
    </View>
  );

  const renderProgressCard = () => {
    if (!account.targetAmount) return null;

    const progressPercentage = account.progressPercentage || 0;
    const remainingAmount = account.targetAmount - account.currentBalance;

    return (
      <View style={styles.progressCard}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>Savings Goal Progress</Text>
          <Text style={styles.progressPercentage}>{progressPercentage.toFixed(1)}%</Text>
        </View>
        
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View 
              style={[
                styles.progressBarFill, 
                { width: `${Math.min(progressPercentage, 100)}%` }
              ]} 
            />
          </View>
        </View>
        
        <View style={styles.progressDetails}>
          <View style={styles.progressItem}>
            <Text style={styles.progressItemLabel}>Target</Text>
            <Text style={styles.progressItemValue}>{formatCurrency(account.targetAmount, account.currency)}</Text>
          </View>
          <View style={styles.progressItem}>
            <Text style={styles.progressItemLabel}>Remaining</Text>
            <Text style={styles.progressItemValue}>{formatCurrency(Math.max(remainingAmount, 0), account.currency)}</Text>
          </View>
          {account.targetDate && (
            <View style={styles.progressItem}>
              <Text style={styles.progressItemLabel}>Target Date</Text>
              <Text style={styles.progressItemValue}>{formatDate(account.targetDate)}</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity style={styles.actionButton} onPress={handleDeposit}>
        <View style={[styles.actionIcon, { backgroundColor: theme.colors.success + '20' }]}>
          <Ionicons name="add" size={20} color={theme.colors.success} />
        </View>
        <Text style={styles.actionText}>Deposit</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton} onPress={handleWithdraw}>
        <View style={[styles.actionIcon, { backgroundColor: theme.colors.warning + '20' }]}>
          <Ionicons name="remove" size={20} color={theme.colors.warning} />
        </View>
        <Text style={styles.actionText}>Withdraw</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton} onPress={handleTransactionHistory}>
        <View style={[styles.actionIcon, { backgroundColor: theme.colors.primary + '20' }]}>
          <Ionicons name="list" size={20} color={theme.colors.primary} />
        </View>
        <Text style={styles.actionText}>History</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('SavingsSettings', { accountId })}>
        <View style={[styles.actionIcon, { backgroundColor: theme.colors.textSecondary + '20' }]}>
          <Ionicons name="settings" size={20} color={theme.colors.textSecondary} />
        </View>
        <Text style={styles.actionText}>Settings</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAnalytics = () => {
    if (!account.analytics) return null;

    return (
      <View style={styles.analyticsCard}>
        <Text style={styles.cardTitle}>Account Analytics</Text>
        
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>{formatCurrency(account.analytics.totalDeposits, account.currency)}</Text>
            <Text style={styles.analyticsLabel}>Total Deposits</Text>
          </View>
          
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>{formatCurrency(account.totalInterestEarned, account.currency)}</Text>
            <Text style={styles.analyticsLabel}>Interest Earned</Text>
          </View>
          
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>{account.analytics.transactionCount}</Text>
            <Text style={styles.analyticsLabel}>Transactions</Text>
          </View>
          
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>{account.analytics.savingsRate?.toFixed(1)}%</Text>
            <Text style={styles.analyticsLabel}>Savings Rate</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderRecentTransactions = () => {
    if (!account.recentTransactions || account.recentTransactions.length === 0) {
      return (
        <View style={styles.transactionsCard}>
          <Text style={styles.cardTitle}>Recent Transactions</Text>
          <View style={styles.emptyTransactions}>
            <Ionicons name="receipt-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyTransactionsText}>No transactions yet</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.transactionsCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Recent Transactions</Text>
          <TouchableOpacity onPress={handleTransactionHistory}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {account.recentTransactions.slice(0, 5).map((transaction) => (
          <View key={transaction.id} style={styles.transactionItem}>
            <View style={styles.transactionIcon}>
              <Ionicons 
                name={getTransactionIcon(transaction.transactionType)} 
                size={20} 
                color={getTransactionColor(transaction.transactionType)} 
              />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionDescription}>{transaction.description}</Text>
              <Text style={styles.transactionDate}>{formatDateTime(transaction.createdAt)}</Text>
            </View>
            <Text style={[
              styles.transactionAmount,
              { color: getTransactionColor(transaction.transactionType) }
            ]}>
              {transaction.transactionType === 'withdrawal' ? '-' : '+'}
              {formatCurrency(Math.abs(transaction.amount), transaction.currency)}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const getTransactionIcon = (type) => {
    const icons = {
      deposit: 'arrow-down',
      withdrawal: 'arrow-up',
      interest: 'trending-up',
      transfer: 'swap-horizontal',
      fee: 'remove-circle',
      bonus: 'gift'
    };
    return icons[type] || 'swap-horizontal';
  };

  const getTransactionColor = (type) => {
    const colors = {
      deposit: theme.colors.success,
      withdrawal: theme.colors.warning,
      interest: theme.colors.primary,
      transfer: theme.colors.info,
      fee: theme.colors.error,
      bonus: theme.colors.success
    };
    return colors[type] || theme.colors.textSecondary;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading account details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!account) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={theme.colors.error} />
          <Text style={styles.errorText}>Account not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Account Details</Text>
        <TouchableOpacity onPress={() => navigation.navigate('SavingsSettings', { accountId })}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderAccountHeader()}
        {renderProgressCard()}
        {renderQuickActions()}
        {renderAnalytics()}
        {renderRecentTransactions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 16,
  },
  accountHeader: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  accountIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountDetails: {
    flex: 1,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountType: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  accountNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  balanceContainer: {
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  availableBalance: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  progressCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
  progressDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressItem: {
    alignItems: 'center',
  },
  progressItemLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  progressItemValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
  },
  analyticsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  analyticsItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  analyticsValue: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  analyticsLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  transactionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  emptyTransactions: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTransactionsText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default SavingsAccountDetailsScreen;
