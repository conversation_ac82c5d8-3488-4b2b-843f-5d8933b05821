/**
 * Savings Goals Screen
 * Screen for setting and managing savings goals with progress tracking
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import financialPlanningService from '../services/financialPlanningService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsGoalsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [goals, setGoals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadGoals();
  }, []);

  const loadGoals = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view goals');
        navigation.goBack();
        return;
      }

      const result = await financialPlanningService.getUserFinancialGoals(userId, { 
        goalType: 'savings',
        isActive: true 
      });

      if (result.success) {
        setGoals(result.goals);
      } else {
        Alert.alert('Error', result.error || 'Failed to load goals');
      }

    } catch (error) {
      console.error('❌ Error loading goals:', error);
      Alert.alert('Error', 'Failed to load goals');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadGoals(true);
  };

  const handleCreateGoal = () => {
    navigation.navigate('FinancialGoalCreation', { goalType: 'savings' });
  };

  const handleGoalPress = (goal) => {
    navigation.navigate('FinancialGoalDetails', { goalId: goal.id });
  };

  const getGoalIcon = (goalType) => {
    const icons = {
      savings: 'wallet',
      emergency_fund: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      home_purchase: 'home'
    };
    return icons[goalType] || 'flag';
  };

  const getGoalColor = (goalType) => {
    const colors = {
      savings: '#4ECDC4',
      emergency_fund: '#96CEB4',
      vacation: '#FECA57',
      education: '#A8E6CF',
      home_purchase: '#FFB6C1'
    };
    return colors[goalType] || '#4ECDC4';
  };

  const renderGoalCard = ({ item: goal }) => (
    <TouchableOpacity 
      style={styles.goalCard}
      onPress={() => handleGoalPress(goal)}
    >
      <View style={styles.goalHeader}>
        <View style={[styles.goalIcon, { backgroundColor: getGoalColor(goal.goalType) }]}>
          <Ionicons name={getGoalIcon(goal.goalType)} size={20} color={theme.colors.white} />
        </View>
        <View style={styles.goalInfo}>
          <Text style={styles.goalName}>{goal.goalName}</Text>
          <Text style={styles.goalType}>
            {goal.goalType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Text>
        </View>
        <Text style={styles.goalPercentage}>{goal.progressPercentage.toFixed(0)}%</Text>
      </View>
      
      <View style={styles.goalProgress}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressText}>
            {formatCurrency(goal.currentAmount, 'UGX')} of {formatCurrency(goal.targetAmount, 'UGX')}
          </Text>
          {goal.targetDate && (
            <Text style={styles.targetDate}>Target: {formatDate(goal.targetDate)}</Text>
          )}
        </View>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${Math.min(goal.progressPercentage, 100)}%`,
                backgroundColor: getGoalColor(goal.goalType)
              }
            ]} 
          />
        </View>
      </View>

      {goal.recommendedMonthly > 0 && (
        <View style={styles.recommendationBadge}>
          <Ionicons name="bulb" size={12} color={theme.colors.warning} />
          <Text style={styles.recommendationText}>
            Save {formatCurrency(goal.recommendedMonthly, 'UGX')}/month to reach goal
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="flag-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Savings Goals Yet</Text>
      <Text style={styles.emptyDescription}>
        Set your first savings goal and start working towards your financial dreams
      </Text>
      <TouchableOpacity style={styles.createButton} onPress={handleCreateGoal}>
        <Text style={styles.createButtonText}>Create Your First Goal</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading goals...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Savings Goals</Text>
        <TouchableOpacity onPress={handleCreateGoal}>
          <Ionicons name="add" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {goals.length > 0 ? (
        <FlatList
          data={goals}
          renderItem={renderGoalCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <ScrollView 
          contentContainerStyle={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderEmptyState()}
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  goalCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  goalIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  goalInfo: {
    flex: 1,
  },
  goalName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  goalType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  goalPercentage: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  goalProgress: {
    marginBottom: 12,
  },
  progressInfo: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 2,
  },
  targetDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  recommendationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.warning + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  recommendationText: {
    fontSize: 10,
    color: theme.colors.warning,
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SavingsGoalsScreen;
