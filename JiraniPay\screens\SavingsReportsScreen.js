/**
 * Savings Reports Screen
 * Screen for viewing detailed savings analytics and reports
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Share,
  Linking,
  Clipboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import walletService from '../services/walletService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsReportsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [summary, setSummary] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const periods = [
    { key: 'week', label: 'This Week' },
    { key: 'month', label: 'This Month' },
    { key: 'quarter', label: 'This Quarter' },
    { key: 'year', label: 'This Year' }
  ];

  useEffect(() => {
    loadReportsData();
    loadUserPreferences();
  }, [selectedPeriod]);

  const loadUserPreferences = async () => {
    try {
      const userId = await getCurrentUserId();
      if (!userId) return;

      // Load saved preferences
      const [
        defaultPeriod,
        defaultFormat,
        currencyPref,
        namingConvention,
        saveLocation,
        includeCharts
      ] = await Promise.all([
        AsyncStorage.getItem(`report_default_period_${userId}`),
        AsyncStorage.getItem(`report_default_format_${userId}`),
        AsyncStorage.getItem(`report_currency_${userId}`),
        AsyncStorage.getItem(`report_naming_${userId}`),
        AsyncStorage.getItem(`report_save_location_${userId}`),
        AsyncStorage.getItem(`report_include_charts_${userId}`)
      ]);

      // Apply preferences if they exist
      if (defaultPeriod && periods.some(p => p.key === defaultPeriod)) {
        setSelectedPeriod(defaultPeriod);
      }

      // Store preferences in state for use in export functions
      // You can add state variables for these if needed
      console.log('📋 Loaded user preferences:', {
        defaultPeriod,
        defaultFormat,
        currencyPref,
        namingConvention,
        saveLocation,
        includeCharts
      });

    } catch (error) {
      console.error('❌ Error loading user preferences:', error);
    }
  };

  const loadReportsData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view reports');
        navigation.goBack();
        return;
      }

      // Load real data from services
      const [summaryResult, accountsResult, walletResult] = await Promise.all([
        savingsAccountService.getSavingsSummary(userId),
        savingsAccountService.getUserSavingsAccounts(userId, { isActive: true }),
        walletService.getFinancialInsights(userId)
      ]);

      if (summaryResult.success) {
        setSummary(summaryResult.summary);
      } else {
        // Calculate summary from accounts if service doesn't provide it
        if (accountsResult.success) {
          const calculatedSummary = calculateSummaryFromAccounts(accountsResult.accounts);
          setSummary(calculatedSummary);
        }
      }

      if (accountsResult.success) {
        setAccounts(accountsResult.accounts);
      }

    } catch (error) {
      console.error('❌ Error loading reports data:', error);
      Alert.alert('Error', 'Failed to load reports data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateSummaryFromAccounts = (accounts) => {
    const totalSavings = accounts.reduce((sum, account) => sum + account.currentBalance, 0);
    const totalInterest = accounts.reduce((sum, account) => sum + (account.totalInterestEarned || 0), 0);
    const totalAccounts = accounts.length;
    const goalAccounts = accounts.filter(account => account.targetAmount > 0).length;

    return {
      totalSavings,
      totalInterest,
      totalAccounts,
      goalAccounts
    };
  };

  const onRefresh = () => {
    loadReportsData(true);
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              selectedPeriod === period.key && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period.key)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period.key && styles.periodButtonTextActive
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderSummaryCards = () => {
    if (!summary) return null;

    const cards = [
      {
        title: 'Total Savings',
        value: formatCurrency(summary.totalSavings || 0, 'UGX'),
        icon: 'wallet',
        color: '#4ECDC4',
        change: '+12.5%'
      },
      {
        title: 'Interest Earned',
        value: formatCurrency(summary.totalInterest || 0, 'UGX'),
        icon: 'trending-up',
        color: '#45B7D1',
        change: '+8.2%'
      },
      {
        title: 'Active Accounts',
        value: summary.totalAccounts || 0,
        icon: 'layers',
        color: '#96CEB4',
        change: '+2'
      },
      {
        title: 'Goal Accounts',
        value: summary.goalAccounts || 0,
        icon: 'flag',
        color: '#FECA57',
        change: '+1'
      }
    ];

    return (
      <View style={styles.summaryGrid}>
        {cards.map((card, index) => (
          <View key={index} style={styles.summaryCard}>
            <View style={styles.summaryCardHeader}>
              <View style={[styles.summaryIcon, { backgroundColor: card.color }]}>
                <Ionicons name={card.icon} size={16} color={theme.colors.white} />
              </View>
              <Text style={styles.summaryChange}>{card.change}</Text>
            </View>
            <Text style={styles.summaryValue}>{card.value}</Text>
            <Text style={styles.summaryTitle}>{card.title}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderAccountPerformance = () => (
    <View style={styles.performanceCard}>
      <Text style={styles.cardTitle}>Account Performance</Text>
      
      {accounts.map((account) => (
        <View key={account.id} style={styles.accountPerformanceItem}>
          <View style={styles.accountPerformanceHeader}>
            <Text style={styles.accountPerformanceName}>{account.accountName}</Text>
            <Text style={styles.accountPerformanceBalance}>
              {formatCurrency(account.currentBalance, account.currency)}
            </Text>
          </View>
          
          <View style={styles.accountPerformanceDetails}>
            <View style={styles.performanceMetric}>
              <Text style={styles.performanceMetricLabel}>Interest Earned</Text>
              <Text style={[styles.performanceMetricValue, { color: theme.colors.success }]}>
                +{formatCurrency(account.totalInterestEarned, account.currency)}
              </Text>
            </View>
            
            {account.targetAmount && (
              <View style={styles.performanceMetric}>
                <Text style={styles.performanceMetricLabel}>Goal Progress</Text>
                <Text style={styles.performanceMetricValue}>
                  {account.progressPercentage.toFixed(1)}%
                </Text>
              </View>
            )}
          </View>
          
          {account.targetAmount && (
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${Math.min(account.progressPercentage, 100)}%`,
                    backgroundColor: '#4ECDC4'
                  }
                ]} 
              />
            </View>
          )}
        </View>
      ))}
    </View>
  );

  const renderInsights = () => (
    <View style={styles.insightsCard}>
      <Text style={styles.cardTitle}>Savings Insights</Text>
      
      <View style={styles.insightItem}>
        <View style={styles.insightIcon}>
          <Ionicons name="trending-up" size={16} color={theme.colors.success} />
        </View>
        <View style={styles.insightContent}>
          <Text style={styles.insightTitle}>Great Progress!</Text>
          <Text style={styles.insightDescription}>
            You've saved 15% more this month compared to last month
          </Text>
        </View>
      </View>
      
      <View style={styles.insightItem}>
        <View style={styles.insightIcon}>
          <Ionicons name="bulb" size={16} color={theme.colors.warning} />
        </View>
        <View style={styles.insightContent}>
          <Text style={styles.insightTitle}>Optimization Tip</Text>
          <Text style={styles.insightDescription}>
            Consider increasing your emergency fund to 6 months of expenses
          </Text>
        </View>
      </View>
      
      <View style={styles.insightItem}>
        <View style={styles.insightIcon}>
          <Ionicons name="star" size={16} color={theme.colors.primary} />
        </View>
        <View style={styles.insightContent}>
          <Text style={styles.insightTitle}>Achievement</Text>
          <Text style={styles.insightDescription}>
            You've maintained consistent savings for 3 months straight!
          </Text>
        </View>
      </View>
    </View>
  );

  const handleExportReport = () => {
    if (!summary || !accounts || accounts.length === 0) {
      Alert.alert(
        'No Data Available',
        'Please create savings accounts first to generate reports.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Export Report',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'PDF',
          onPress: () => exportToPDF()
        },
        {
          text: 'Excel',
          onPress: () => exportToExcel()
        }
      ]
    );
  };

  const exportToPDF = async () => {
    try {
      setLoading(true);

      console.log('📄 Starting PDF export...');
      console.log('Summary data:', summary);
      console.log('Accounts data:', accounts?.length || 0, 'accounts');

      // Generate HTML content for PDF
      const htmlContent = generateReportHTML();
      console.log('✅ HTML content generated');

      // Create PDF
      console.log('📄 Creating PDF...');
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });
      console.log('✅ PDF created at:', uri);

      // Try to share the PDF directly
      try {
        console.log('📤 Checking sharing availability...');
        const isAvailable = await Sharing.isAvailableAsync();
        console.log('Sharing available:', isAvailable);

        if (isAvailable) {
          console.log('📤 Sharing PDF...');
          await Sharing.shareAsync(uri, {
            mimeType: 'application/pdf',
            dialogTitle: 'Save or Share Savings Report'
          });

          Alert.alert(
            'Export Successful',
            'PDF report has been generated and shared successfully!'
          );
        } else {
          throw new Error('Sharing not available');
        }
      } catch (shareError) {
        console.log('❌ Sharing failed, trying alternative approach:', shareError);

        // Alternative: Copy to clipboard as text
        const reportText = generateReportText();
        await Clipboard.setString(reportText);

        Alert.alert(
          'Export Alternative',
          'PDF sharing not available. Report summary has been copied to clipboard instead.',
          [
            { text: 'OK' },
            { text: 'Try Again', onPress: () => exportToPDF() }
          ]
        );
      }

    } catch (error) {
      console.error('❌ Error exporting PDF:', error);
      Alert.alert(
        'Export Failed',
        `Failed to export PDF report: ${error.message}. Please check console for details.`
      );
    } finally {
      setLoading(false);
    }
  };

  const exportToExcel = async () => {
    try {
      setLoading(true);

      console.log('📊 Starting Excel export...');
      console.log('Summary data:', summary);
      console.log('Accounts data:', accounts?.length || 0, 'accounts');

      // Generate CSV content (Excel-compatible)
      const csvContent = generateCSVContent();
      console.log('✅ CSV content generated');

      // Save to device storage
      const fileName = `savings_report_${new Date().toISOString().split('T')[0]}.csv`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      console.log('💾 Writing file to:', fileUri);
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });
      console.log('✅ File written successfully');

      // Try to share the CSV file directly
      try {
        console.log('📤 Checking sharing availability...');
        const isAvailable = await Sharing.isAvailableAsync();
        console.log('Sharing available:', isAvailable);

        if (isAvailable) {
          console.log('📤 Sharing CSV...');
          await Sharing.shareAsync(fileUri, {
            mimeType: 'text/csv',
            dialogTitle: 'Save or Share Savings Report (Excel)'
          });

          Alert.alert(
            'Export Successful',
            'Excel report has been generated and shared successfully!'
          );
        } else {
          throw new Error('Sharing not available');
        }
      } catch (shareError) {
        console.log('❌ Sharing failed, trying alternative approach:', shareError);

        // Alternative: Copy CSV content to clipboard
        await Clipboard.setString(csvContent);

        Alert.alert(
          'Export Alternative',
          'Excel sharing not available. Report data has been copied to clipboard as CSV format.',
          [
            { text: 'OK' },
            { text: 'Try Again', onPress: () => exportToExcel() }
          ]
        );
      }

    } catch (error) {
      console.error('❌ Error exporting Excel:', error);
      Alert.alert(
        'Export Failed',
        `Failed to export Excel report: ${error.message}. Please check console for details.`
      );
    } finally {
      setLoading(false);
    }
  };

  const generateReportHTML = () => {
    const currentDate = new Date().toLocaleDateString();
    const periodLabel = periods.find(p => p.key === selectedPeriod)?.label || 'This Month';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>JiraniPay Savings Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { background: #f5f5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
            .accounts { margin-bottom: 20px; }
            .account { border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; border-radius: 5px; }
            .metric { display: inline-block; margin-right: 20px; }
            .metric-value { font-weight: bold; color: #4ECDC4; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>JiraniPay Savings Report</h1>
            <p>Period: ${periodLabel}</p>
            <p>Generated: ${currentDate}</p>
          </div>

          <div class="summary">
            <h2>Summary</h2>
            <div class="metric">
              <span>Total Savings: </span>
              <span class="metric-value">${formatCurrency(summary?.totalSavings || 0, 'UGX')}</span>
            </div>
            <div class="metric">
              <span>Interest Earned: </span>
              <span class="metric-value">${formatCurrency(summary?.totalInterest || 0, 'UGX')}</span>
            </div>
            <div class="metric">
              <span>Active Accounts: </span>
              <span class="metric-value">${summary?.totalAccounts || 0}</span>
            </div>
            <div class="metric">
              <span>Goal Accounts: </span>
              <span class="metric-value">${summary?.goalAccounts || 0}</span>
            </div>
          </div>

          <div class="accounts">
            <h2>Account Details</h2>
            ${accounts.map(account => `
              <div class="account">
                <h3>${account.accountName}</h3>
                <p><strong>Type:</strong> ${account.accountType}</p>
                <p><strong>Balance:</strong> ${formatCurrency(account.currentBalance, account.currency)}</p>
                <p><strong>Interest Earned:</strong> ${formatCurrency(account.totalInterestEarned || 0, account.currency)}</p>
                ${account.targetAmount ? `<p><strong>Goal Progress:</strong> ${account.progressPercentage?.toFixed(1) || 0}%</p>` : ''}
                <p><strong>Created:</strong> ${formatDate(account.createdAt)}</p>
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;
  };

  const generateCSVContent = () => {
    let csv = 'JiraniPay Savings Report\n';
    csv += `Generated: ${new Date().toLocaleDateString()}\n`;
    csv += `Period: ${periods.find(p => p.key === selectedPeriod)?.label || 'This Month'}\n\n`;

    csv += 'SUMMARY\n';
    csv += 'Metric,Value\n';
    csv += `Total Savings,${summary?.totalSavings || 0}\n`;
    csv += `Interest Earned,${summary?.totalInterest || 0}\n`;
    csv += `Active Accounts,${summary?.totalAccounts || 0}\n`;
    csv += `Goal Accounts,${summary?.goalAccounts || 0}\n\n`;

    csv += 'ACCOUNT DETAILS\n';
    csv += 'Account Name,Type,Balance,Interest Earned,Goal Progress,Created Date\n';

    accounts.forEach(account => {
      csv += `${account.accountName},${account.accountType},${account.currentBalance},${account.totalInterestEarned || 0},${account.progressPercentage?.toFixed(1) || 0}%,${formatDate(account.createdAt)}\n`;
    });

    return csv;
  };

  const generateReportText = () => {
    const currentDate = new Date().toLocaleDateString();
    const periodLabel = periods.find(p => p.key === selectedPeriod)?.label || 'This Month';

    let text = `📊 JIRANIPAY SAVINGS REPORT\n`;
    text += `📅 Period: ${periodLabel}\n`;
    text += `📆 Generated: ${currentDate}\n\n`;

    text += `💰 SUMMARY\n`;
    text += `Total Savings: ${formatCurrency(summary?.totalSavings || 0, 'UGX')}\n`;
    text += `Interest Earned: ${formatCurrency(summary?.totalInterest || 0, 'UGX')}\n`;
    text += `Active Accounts: ${summary?.totalAccounts || 0}\n`;
    text += `Goal Accounts: ${summary?.goalAccounts || 0}\n\n`;

    text += `🏦 ACCOUNT DETAILS\n`;
    accounts.forEach((account, index) => {
      text += `${index + 1}. ${account.accountName}\n`;
      text += `   Type: ${account.accountType}\n`;
      text += `   Balance: ${formatCurrency(account.currentBalance, account.currency)}\n`;
      text += `   Interest: ${formatCurrency(account.totalInterestEarned || 0, account.currency)}\n`;
      if (account.targetAmount) {
        text += `   Goal Progress: ${account.progressPercentage?.toFixed(1) || 0}%\n`;
      }
      text += `   Created: ${formatDate(account.createdAt)}\n\n`;
    });

    return text;
  };

  const handleShareReport = () => {
    Alert.alert(
      'Share Report',
      'How would you like to share this report?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Email',
          onPress: () => shareViaEmail()
        },
        {
          text: 'WhatsApp',
          onPress: () => shareViaWhatsApp()
        },
        {
          text: 'Copy Summary',
          onPress: () => copyReportSummary()
        }
      ]
    );
  };

  const shareViaEmail = async () => {
    try {
      const subject = `JiraniPay Savings Report - ${new Date().toLocaleDateString()}`;
      const body = `Hi,\n\nPlease find attached your JiraniPay savings report.\n\nSummary:\n- Total Savings: ${formatCurrency(summary?.totalSavings || 0, 'UGX')}\n- Interest Earned: ${formatCurrency(summary?.totalInterest || 0, 'UGX')}\n- Active Accounts: ${summary?.totalAccounts || 0}\n\nBest regards,\nJiraniPay Team`;

      const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

      const canOpen = await Linking.canOpenURL(emailUrl);
      if (canOpen) {
        await Linking.openURL(emailUrl);
      } else {
        Alert.alert('Email Not Available', 'No email app found on this device');
      }
    } catch (error) {
      console.error('❌ Error sharing via email:', error);
      Alert.alert('Share Failed', 'Failed to open email app');
    }
  };

  const shareViaWhatsApp = async () => {
    try {
      const message = `📊 *JiraniPay Savings Report*\n\n💰 Total Savings: ${formatCurrency(summary?.totalSavings || 0, 'UGX')}\n📈 Interest Earned: ${formatCurrency(summary?.totalInterest || 0, 'UGX')}\n🏦 Active Accounts: ${summary?.totalAccounts || 0}\n🎯 Goal Accounts: ${summary?.goalAccounts || 0}\n\nGenerated: ${new Date().toLocaleDateString()}`;

      const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;

      const canOpen = await Linking.canOpenURL(whatsappUrl);
      if (canOpen) {
        await Linking.openURL(whatsappUrl);
      } else {
        // Fallback to regular share
        await Share.share({
          message: message,
          title: 'JiraniPay Savings Report'
        });
      }
    } catch (error) {
      console.error('❌ Error sharing via WhatsApp:', error);
      Alert.alert('Share Failed', 'Failed to share via WhatsApp');
    }
  };

  const copyReportSummary = async () => {
    try {
      const summary_text = `JiraniPay Savings Report\nTotal Savings: ${formatCurrency(summary?.totalSavings || 0, 'UGX')}\nInterest Earned: ${formatCurrency(summary?.totalInterest || 0, 'UGX')}\nActive Accounts: ${summary?.totalAccounts || 0}\nGoal Accounts: ${summary?.goalAccounts || 0}\nGenerated: ${new Date().toLocaleDateString()}`;

      await Clipboard.setString(summary_text);
      Alert.alert('Copied!', 'Report summary copied to clipboard');
    } catch (error) {
      console.error('❌ Error copying to clipboard:', error);
      Alert.alert('Copy Failed', 'Failed to copy report summary');
    }
  };

  const handleDetailedAnalytics = () => {
    navigation.navigate('SavingsAnalytics');
  };

  const handleScheduleReport = () => {
    navigation.navigate('SavingsReportScheduling', {
      currentPeriod: selectedPeriod,
      summary: summary,
      accounts: accounts
    });
  };

  const handleReportSettings = () => {
    Alert.alert(
      'Report Settings',
      'Choose an option:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Report Preferences',
          onPress: () => showReportPreferences()
        },
        {
          text: 'Export Settings',
          onPress: () => showExportSettings()
        },
        {
          text: 'Schedule Settings',
          onPress: () => navigation.navigate('SavingsReportScheduling', {
            currentPeriod: selectedPeriod,
            summary: summary,
            accounts: accounts
          })
        },
        {
          text: 'Clear Cache',
          onPress: () => clearReportCache()
        }
      ]
    );
  };

  const showReportPreferences = () => {
    Alert.alert(
      'Report Preferences',
      'Configure your report preferences:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Default Period',
          onPress: () => showPeriodSelector()
        },
        {
          text: 'Currency Display',
          onPress: () => showCurrencyOptions()
        },
        {
          text: 'Include Charts',
          onPress: () => toggleChartsInReports()
        }
      ]
    );
  };

  const showExportSettings = () => {
    Alert.alert(
      'Export Settings',
      'Configure export preferences:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Default Format',
          onPress: () => showFormatSelector()
        },
        {
          text: 'File Naming',
          onPress: () => showFileNamingOptions()
        },
        {
          text: 'Auto-Save Location',
          onPress: () => showSaveLocationOptions()
        }
      ]
    );
  };

  const showPeriodSelector = () => {
    Alert.alert(
      'Default Report Period',
      'Select your preferred default period:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'This Week', onPress: () => saveDefaultPeriod('week') },
        { text: 'This Month', onPress: () => saveDefaultPeriod('month') },
        { text: 'This Quarter', onPress: () => saveDefaultPeriod('quarter') },
        { text: 'This Year', onPress: () => saveDefaultPeriod('year') }
      ]
    );
  };

  const showCurrencyOptions = () => {
    Alert.alert(
      'Currency Display',
      'Choose currency display format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'UGX (Ugandan Shilling)', onPress: () => saveCurrencyPreference('UGX') },
        { text: 'USD (US Dollar)', onPress: () => saveCurrencyPreference('USD') },
        { text: 'EUR (Euro)', onPress: () => saveCurrencyPreference('EUR') },
        { text: 'Auto (Account Currency)', onPress: () => saveCurrencyPreference('auto') }
      ]
    );
  };

  const showFormatSelector = () => {
    Alert.alert(
      'Default Export Format',
      'Choose your preferred export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'PDF', onPress: () => saveDefaultFormat('pdf') },
        { text: 'Excel/CSV', onPress: () => saveDefaultFormat('excel') },
        { text: 'Text Summary', onPress: () => saveDefaultFormat('text') }
      ]
    );
  };

  const showFileNamingOptions = () => {
    Alert.alert(
      'File Naming Convention',
      'Choose file naming format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Date Only (2024-01-15)', onPress: () => saveNamingConvention('date') },
        { text: 'Report + Date (savings_report_2024-01-15)', onPress: () => saveNamingConvention('report_date') },
        { text: 'Custom Prefix', onPress: () => showCustomPrefixInput() }
      ]
    );
  };

  const showSaveLocationOptions = () => {
    Alert.alert(
      'Auto-Save Location',
      'Choose where to save exported files:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Downloads Folder', onPress: () => saveSaveLocation('downloads') },
        { text: 'Documents Folder', onPress: () => saveSaveLocation('documents') },
        { text: 'App Storage Only', onPress: () => saveSaveLocation('app') },
        { text: 'Always Ask', onPress: () => saveSaveLocation('ask') }
      ]
    );
  };

  const toggleChartsInReports = () => {
    Alert.alert(
      'Include Charts in Reports',
      'Would you like to include visual charts in your reports?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Yes, Include Charts', onPress: () => saveChartsPreference(true) },
        { text: 'No, Text Only', onPress: () => saveChartsPreference(false) }
      ]
    );
  };

  const showCustomPrefixInput = () => {
    Alert.prompt(
      'Custom File Prefix',
      'Enter a custom prefix for your report files:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Save', onPress: (prefix) => saveCustomPrefix(prefix) }
      ],
      'plain-text',
      'my_savings_report'
    );
  };

  const clearReportCache = async () => {
    try {
      // Clear any cached report data
      const userId = await getCurrentUserId();
      await AsyncStorage.removeItem(`savings_report_cache_${userId}`);

      Alert.alert(
        'Cache Cleared',
        'Report cache has been cleared successfully. Fresh data will be loaded on next report generation.'
      );
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
      Alert.alert('Error', 'Failed to clear cache');
    }
  };

  // Preference saving functions
  const saveDefaultPeriod = async (period) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_default_period_${userId}`, period);
      Alert.alert('Saved', `Default period set to ${period}`);
    } catch (error) {
      console.error('❌ Error saving period preference:', error);
    }
  };

  const saveCurrencyPreference = async (currency) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_currency_${userId}`, currency);
      Alert.alert('Saved', `Currency display set to ${currency}`);
    } catch (error) {
      console.error('❌ Error saving currency preference:', error);
    }
  };

  const saveDefaultFormat = async (format) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_default_format_${userId}`, format);
      Alert.alert('Saved', `Default export format set to ${format.toUpperCase()}`);
    } catch (error) {
      console.error('❌ Error saving format preference:', error);
    }
  };

  const saveNamingConvention = async (convention) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_naming_${userId}`, convention);
      Alert.alert('Saved', 'File naming convention updated');
    } catch (error) {
      console.error('❌ Error saving naming preference:', error);
    }
  };

  const saveSaveLocation = async (location) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_save_location_${userId}`, location);
      Alert.alert('Saved', `Auto-save location set to ${location}`);
    } catch (error) {
      console.error('❌ Error saving location preference:', error);
    }
  };

  const saveChartsPreference = async (includeCharts) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_include_charts_${userId}`, includeCharts.toString());
      Alert.alert('Saved', `Charts ${includeCharts ? 'will be' : 'will not be'} included in reports`);
    } catch (error) {
      console.error('❌ Error saving charts preference:', error);
    }
  };

  const saveCustomPrefix = async (prefix) => {
    if (!prefix || prefix.trim().length === 0) {
      Alert.alert('Invalid Input', 'Please enter a valid prefix');
      return;
    }

    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`report_custom_prefix_${userId}`, prefix.trim());
      Alert.alert('Saved', `Custom prefix set to "${prefix.trim()}"`);
    } catch (error) {
      console.error('❌ Error saving custom prefix:', error);
    }
  };

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>

      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleExportReport}
        >
          <Ionicons name="download" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Export Report</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleShareReport}
        >
          <Ionicons name="share" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Share Report</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleDetailedAnalytics}
        >
          <Ionicons name="analytics" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Detailed Analytics</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleScheduleReport}
        >
          <Ionicons name="calendar" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Schedule Reports</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading reports...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Savings Reports</Text>
        <TouchableOpacity onPress={handleReportSettings}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderSummaryCards()}
        {renderAccountPerformance()}
        {renderInsights()}
        {renderQuickActions()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  periodSelector: {
    marginBottom: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: theme.colors.white,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  summaryCard: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  summaryCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryChange: {
    fontSize: 12,
    color: theme.colors.success,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  summaryTitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  performanceCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  accountPerformanceItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  accountPerformanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  accountPerformanceName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  accountPerformanceBalance: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  accountPerformanceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  performanceMetric: {
    alignItems: 'center',
  },
  performanceMetricLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  performanceMetricValue: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  insightsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  insightIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  insightDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  actionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    width: '48%',
    paddingVertical: 12,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default SavingsReportsScreen;
