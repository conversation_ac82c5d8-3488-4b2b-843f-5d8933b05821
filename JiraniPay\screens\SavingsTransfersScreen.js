/**
 * Savings Transfers Screen
 * Screen for transferring money between savings accounts and wallet
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import walletService from '../services/walletService';
import { formatCurrency } from '../utils/currencyUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsTransfersScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [accounts, setAccounts] = useState([]);
  const [walletBalance, setWalletBalance] = useState(0);
  const [transferAmount, setTransferAmount] = useState('');
  const [selectedFromAccount, setSelectedFromAccount] = useState(null);
  const [selectedToAccount, setSelectedToAccount] = useState(null);
  const [showFromModal, setShowFromModal] = useState(false);
  const [showToModal, setShowToModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [description, setDescription] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to transfer funds');
        navigation.goBack();
        return;
      }

      // Load savings accounts and wallet balance
      const [accountsResult, walletResult] = await Promise.all([
        savingsAccountService.getUserSavingsAccounts(userId, { isActive: true }),
        walletService.getWalletBalance(userId)
      ]);

      if (accountsResult.success) {
        setAccounts(accountsResult.accounts);
      }

      if (walletResult.success) {
        setWalletBalance(walletResult.data.balance);
      }

    } catch (error) {
      console.error('❌ Error loading transfer data:', error);
      Alert.alert('Error', 'Failed to load transfer data');
    }
  };

  const handleTransfer = async () => {
    try {
      if (!transferAmount || parseFloat(transferAmount) <= 0) {
        Alert.alert('Invalid Amount', 'Please enter a valid transfer amount');
        return;
      }

      if (!selectedFromAccount || !selectedToAccount) {
        Alert.alert('Select Accounts', 'Please select both source and destination accounts');
        return;
      }

      if (selectedFromAccount.id === selectedToAccount.id) {
        Alert.alert('Invalid Transfer', 'Cannot transfer to the same account');
        return;
      }

      const amount = parseFloat(transferAmount);

      // Check sufficient balance
      if (selectedFromAccount.type === 'wallet' && amount > walletBalance) {
        Alert.alert('Insufficient Balance', 'Wallet balance is insufficient for this transfer');
        return;
      }

      if (selectedFromAccount.type === 'savings' && amount > selectedFromAccount.availableBalance) {
        Alert.alert('Insufficient Balance', 'Savings account balance is insufficient for this transfer');
        return;
      }

      setLoading(true);

      const userId = await getCurrentUserId();
      let result;

      if (selectedFromAccount.type === 'wallet' && selectedToAccount.type === 'savings') {
        // Wallet to Savings
        result = await savingsAccountService.processDeposit(
          userId,
          selectedToAccount.id,
          amount,
          description || 'Transfer from wallet',
          'wallet'
        );
      } else if (selectedFromAccount.type === 'savings' && selectedToAccount.type === 'wallet') {
        // Savings to Wallet
        result = await savingsAccountService.processWithdrawal(
          userId,
          selectedFromAccount.id,
          amount,
          description || 'Transfer to wallet'
        );
      } else {
        // Savings to Savings (future implementation)
        Alert.alert('Coming Soon', 'Savings to savings transfers will be available soon!');
        return;
      }

      if (result.success) {
        Alert.alert(
          'Transfer Successful',
          `Successfully transferred ${formatCurrency(amount, 'UGX')}`,
          [
            {
              text: 'OK',
              onPress: () => {
                setTransferAmount('');
                setDescription('');
                setSelectedFromAccount(null);
                setSelectedToAccount(null);
                loadData(); // Refresh balances
              }
            }
          ]
        );
      } else {
        Alert.alert('Transfer Failed', result.error || 'Failed to process transfer');
      }

    } catch (error) {
      console.error('❌ Error processing transfer:', error);
      Alert.alert('Error', 'Failed to process transfer');
    } finally {
      setLoading(false);
    }
  };

  const getAccountOptions = (excludeType = null) => {
    const options = [];
    
    // Add wallet option
    if (excludeType !== 'wallet') {
      options.push({
        id: 'wallet',
        type: 'wallet',
        name: 'Wallet',
        balance: walletBalance,
        availableBalance: walletBalance,
        icon: 'wallet',
        color: '#4ECDC4'
      });
    }

    // Add savings accounts
    if (excludeType !== 'savings') {
      accounts.forEach(account => {
        options.push({
          ...account,
          type: 'savings',
          name: account.accountName,
          balance: account.currentBalance,
          icon: 'piggy-bank',
          color: '#45B7D1'
        });
      });
    }

    return options;
  };

  const renderAccountSelector = (title, selectedAccount, onSelect, showModal, setShowModal, excludeType = null) => (
    <View style={styles.selectorContainer}>
      <Text style={styles.selectorLabel}>{title}</Text>
      <TouchableOpacity 
        style={styles.selectorButton}
        onPress={() => setShowModal(true)}
      >
        {selectedAccount ? (
          <View style={styles.selectedAccountContent}>
            <View style={[styles.accountIcon, { backgroundColor: selectedAccount.color }]}>
              <Ionicons name={selectedAccount.icon} size={16} color={theme.colors.white} />
            </View>
            <View style={styles.accountInfo}>
              <Text style={styles.accountName}>{selectedAccount.name}</Text>
              <Text style={styles.accountBalance}>
                Balance: {formatCurrency(selectedAccount.balance, 'UGX')}
              </Text>
            </View>
          </View>
        ) : (
          <Text style={styles.selectorPlaceholder}>Select {title.toLowerCase()}</Text>
        )}
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select {title}</Text>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {getAccountOptions(excludeType).map((account) => (
              <TouchableOpacity
                key={account.id}
                style={[
                  styles.accountOption,
                  selectedAccount?.id === account.id && styles.accountOptionSelected
                ]}
                onPress={() => {
                  onSelect(account);
                  setShowModal(false);
                }}
              >
                <View style={[styles.accountIcon, { backgroundColor: account.color }]}>
                  <Ionicons name={account.icon} size={20} color={theme.colors.white} />
                </View>
                <View style={styles.accountOptionInfo}>
                  <Text style={styles.accountOptionName}>{account.name}</Text>
                  <Text style={styles.accountOptionBalance}>
                    Balance: {formatCurrency(account.balance, 'UGX')}
                  </Text>
                </View>
                {selectedAccount?.id === account.id && (
                  <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Transfer Funds</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.transferCard}>
          <Text style={styles.cardTitle}>Transfer Details</Text>
          
          {/* From Account */}
          {renderAccountSelector(
            'From Account',
            selectedFromAccount,
            setSelectedFromAccount,
            showFromModal,
            setShowFromModal
          )}

          {/* Transfer Icon */}
          <View style={styles.transferIconContainer}>
            <View style={styles.transferIcon}>
              <Ionicons name="arrow-down" size={20} color={theme.colors.primary} />
            </View>
          </View>

          {/* To Account */}
          {renderAccountSelector(
            'To Account',
            selectedToAccount,
            setSelectedToAccount,
            showToModal,
            setShowToModal,
            selectedFromAccount?.type
          )}

          {/* Amount Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Transfer Amount</Text>
            <TextInput
              style={styles.amountInput}
              value={transferAmount}
              onChangeText={setTransferAmount}
              placeholder="Enter amount"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          {/* Description Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={styles.textInput}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter transfer description"
              placeholderTextColor={theme.colors.textSecondary}
              maxLength={100}
            />
          </View>

          {/* Transfer Summary */}
          {transferAmount && selectedFromAccount && selectedToAccount && (
            <View style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Transfer Summary</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Amount:</Text>
                <Text style={styles.summaryValue}>{formatCurrency(parseFloat(transferAmount), 'UGX')}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>From:</Text>
                <Text style={styles.summaryValue}>{selectedFromAccount.name}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>To:</Text>
                <Text style={styles.summaryValue}>{selectedToAccount.name}</Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Transfer Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.transferButton,
            (!transferAmount || !selectedFromAccount || !selectedToAccount || loading) && styles.transferButtonDisabled
          ]}
          onPress={handleTransfer}
          disabled={!transferAmount || !selectedFromAccount || !selectedToAccount || loading}
        >
          <Text style={styles.transferButtonText}>
            {loading ? 'Processing...' : 'Transfer Funds'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  transferCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  selectorLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  selectorButton: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedAccountContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  accountBalance: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  selectorPlaceholder: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  transferIconContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  transferIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  amountInput: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
    textAlign: 'center',
  },
  textInput: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  summaryCard: {
    backgroundColor: theme.colors.primary + '10',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  transferButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  transferButtonDisabled: {
    opacity: 0.5,
  },
  transferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  accountOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  accountOptionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  accountOptionName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountOptionBalance: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default SavingsTransfersScreen;
