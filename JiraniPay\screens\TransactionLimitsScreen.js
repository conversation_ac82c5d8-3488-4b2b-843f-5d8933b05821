/**
 * Transaction Limits Screen
 * Displays user's transaction limits, usage, and verification level information
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import transactionLimitsService from '../services/transactionLimitsService';
import { formatCurrency } from '../utils/currencyUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const { width } = Dimensions.get('window');

const TransactionLimitsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [limitStatus, setLimitStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadLimitStatus();
  }, []);

  const loadLimitStatus = async () => {
    try {
      setLoading(true);
      // Get current user ID from auth context
      const userId = 'current-user-id'; // Replace with actual user ID from auth
      const status = await transactionLimitsService.getLimitStatus(userId);
      setLimitStatus(status);
    } catch (error) {
      console.error('❌ Error loading limit status:', error);
      Alert.alert('Error', 'Failed to load transaction limits. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLimitStatus();
    setRefreshing(false);
  };

  const handleUpgradeVerification = () => {
    Alert.alert(
      'Upgrade Verification',
      'Increase your transaction limits by completing higher verification levels.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Upgrade', 
          onPress: () => navigation.navigate('AccountVerification')
        }
      ]
    );
  };

  const renderLimitCard = (title, usage, icon, color) => (
    <View style={[styles.limitCard, { borderLeftColor: color }]}>
      <View style={styles.limitHeader}>
        <View style={styles.limitTitleContainer}>
          <Ionicons name={icon} size={20} color={color} />
          <Text style={styles.limitTitle}>{title}</Text>
        </View>
        <Text style={styles.limitPercentage}>
          {usage.percentage.toFixed(1)}%
        </Text>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${Math.min(usage.percentage, 100)}%`,
                backgroundColor: color
              }
            ]} 
          />
        </View>
      </View>
      
      <View style={styles.limitDetails}>
        <Text style={styles.limitUsed}>
          Used: {formatCurrency(usage.used)}
        </Text>
        <Text style={styles.limitTotal}>
          of {formatCurrency(usage.limit)}
        </Text>
      </View>
      
      <Text style={styles.limitRemaining}>
        Remaining: {formatCurrency(usage.remaining)}
      </Text>
    </View>
  );

  const renderVerificationLevel = () => {
    if (!limitStatus) return null;

    const levelColors = {
      basic: '#ffc107',
      enhanced: '#17a2b8',
      premium: '#28a745'
    };

    const levelIcons = {
      basic: 'shield-outline',
      enhanced: 'shield-half',
      premium: 'shield-checkmark'
    };

    return (
      <View style={styles.verificationCard}>
        <View style={styles.verificationHeader}>
          <Ionicons 
            name={levelIcons[limitStatus.verificationLevel]} 
            size={24} 
            color={levelColors[limitStatus.verificationLevel]} 
          />
          <Text style={styles.verificationTitle}>
            {limitStatus.verificationLevel.charAt(0).toUpperCase() + 
             limitStatus.verificationLevel.slice(1)} Verification
          </Text>
        </View>
        
        <Text style={styles.verificationDescription}>
          {limitStatus.limits.description}
        </Text>
        
        {limitStatus.verificationLevel !== 'premium' && (
          <TouchableOpacity 
            style={styles.upgradeButton}
            onPress={handleUpgradeVerification}
          >
            <Ionicons name="arrow-up-circle" size={20} color={theme.colors.white} />
            <Text style={styles.upgradeButtonText}>Upgrade Verification</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderRecommendations = () => {
    if (!limitStatus?.recommendations?.length) return null;

    return (
      <View style={styles.recommendationsCard}>
        <Text style={styles.recommendationsTitle}>Recommendations</Text>
        {limitStatus.recommendations.map((rec, index) => (
          <View key={index} style={styles.recommendationItem}>
            <View style={styles.recommendationIcon}>
              <Ionicons 
                name={rec.type === 'warning' ? 'warning' : 'information-circle'} 
                size={16} 
                color={rec.type === 'warning' ? '#ffc107' : '#17a2b8'} 
              />
            </View>
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>{rec.title}</Text>
              <Text style={styles.recommendationMessage}>{rec.message}</Text>
              {rec.action && (
                <Text style={styles.recommendationAction}>{rec.action}</Text>
              )}
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading transaction limits...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Transaction Limits</Text>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.navigate('SecuritySettings')}
        >
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Verification Level */}
        {renderVerificationLevel()}

        {/* Transaction Limits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Usage</Text>
          
          {limitStatus && (
            <>
              {renderLimitCard(
                'Daily Limit',
                limitStatus.usage.daily,
                'today',
                '#dc3545'
              )}
              
              {renderLimitCard(
                'Weekly Limit',
                limitStatus.usage.weekly,
                'calendar',
                '#ffc107'
              )}
              
              {renderLimitCard(
                'Monthly Limit',
                limitStatus.usage.monthly,
                'calendar-outline',
                '#28a745'
              )}
            </>
          )}
        </View>

        {/* Single Transaction Limit */}
        {limitStatus && (
          <View style={styles.singleLimitCard}>
            <View style={styles.singleLimitHeader}>
              <Ionicons name="card" size={20} color={theme.colors.primary} />
              <Text style={styles.singleLimitTitle}>Single Transaction Limit</Text>
            </View>
            <Text style={styles.singleLimitAmount}>
              {formatCurrency(limitStatus.limits.single)}
            </Text>
            <Text style={styles.singleLimitDescription}>
              Maximum amount per transaction
            </Text>
          </View>
        )}

        {/* Recommendations */}
        {renderRecommendations()}

        {/* Help Section */}
        <View style={styles.helpCard}>
          <Text style={styles.helpTitle}>Need Higher Limits?</Text>
          <Text style={styles.helpDescription}>
            Complete verification to increase your transaction limits and unlock more features.
          </Text>
          <TouchableOpacity 
            style={styles.helpButton}
            onPress={() => navigation.navigate('FAQ')}
          >
            <Text style={styles.helpButtonText}>Learn More</Text>
            <Ionicons name="arrow-forward" size={16} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  verificationCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  verificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  verificationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 10,
  },
  verificationDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 15,
  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    padding: 12,
  },
  upgradeButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 15,
  },
  limitCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  limitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  limitTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  limitTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  limitPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  progressContainer: {
    marginBottom: 10,
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  limitDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  limitUsed: {
    fontSize: 12,
    color: theme.colors.text,
    fontWeight: '500',
  },
  limitTotal: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  limitRemaining: {
    fontSize: 12,
    color: theme.colors.success,
    fontWeight: '500',
  },
  singleLimitCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  singleLimitHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  singleLimitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  singleLimitAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 5,
  },
  singleLimitDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  recommendationsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  recommendationItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  recommendationIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  recommendationMessage: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  recommendationAction: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  helpCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  helpDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 15,
    lineHeight: 20,
  },
  helpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderRadius: 8,
    padding: 12,
  },
  helpButtonText: {
    color: theme.colors.primary,
    fontWeight: '600',
    marginRight: 8,
  },
});

export default TransactionLimitsScreen;
