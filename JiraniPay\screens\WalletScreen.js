import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import TransactionDetailModal from '../components/TransactionDetailModal';
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import databaseService from '../services/databaseService';
import walletService from '../services/walletService';
// Temporarily disabled until dependencies are installed
// import biometricService from '../services/biometricService';
// import notificationService from '../services/notificationService';
import analyticsService from '../services/analyticsService';
import currencyService from '../services/currencyService';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import { useLanguage } from '../contexts/LanguageContext';

const WalletScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [walletData, setWalletData] = useState(null);
  const [userPreferences, setUserPreferences] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [recentTransactions, setRecentTransactions] = useState([]);

  // Use theme, currency, and language contexts
  const { theme } = useTheme();
  const { userCurrency, convertAndFormat } = useCurrencyContext();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Transaction Detail Modal State
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [transactionModalVisible, setTransactionModalVisible] = useState(false);

  useEffect(() => {
    initializeServices();
  }, []);

  const initializeServices = async () => {
    try {
      // Initialize available services
      await Promise.all([
        analyticsService.initialize(),
        currencyService.initialize()
      ]);

      // Load wallet data
      await loadWalletData();
    } catch (error) {
      console.error('❌ Error initializing wallet services:', error);
      // Still load wallet data even if services fail
      await loadWalletData();
    }
  };

  const loadWalletData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        
        // Load user preferences (non-currency preferences)
        try {
          const preferencesResult = await databaseService.getUserPreferences(currentUser.id);
          if (preferencesResult.success) {
            setUserPreferences(preferencesResult.data);
          } else {
            // Set default preferences (currency handled by CurrencyContext)
            setUserPreferences({
              notifications_enabled: true,
            });
          }
        } catch (error) {
          console.log('⚠️ Preferences loading failed (expected in development)');
          setUserPreferences({
            notifications_enabled: true,
          });
        }
        
        // Load actual wallet data from wallet service
        try {
          const wallet = await walletService.getWalletBalance(currentUser.id);
          if (wallet.success) {
            setWalletData({
              balance: wallet.balance || 0,
              currency: wallet.currency || 'UGX',
              // Add additional wallet metadata
              daily_limit: 500000,
              monthly_limit: 2000000,
              spent_today: 0,
              spent_this_month: 0,
              last_transaction: new Date().toISOString(),
            });
            console.log('✅ Wallet data loaded in WalletScreen:', { balance: wallet.balance, currency: wallet.currency });
          } else {
            console.log('⚠️ Wallet not found, attempting to create wallet...');

            // Try to create wallet if it doesn't exist
            try {
              const user = authService.getCurrentUser();
              if (user) {
                const createResult = await walletService.createWallet(user.id);
                if (createResult.success) {
                  console.log('✅ Wallet created successfully, setting data');
                  setWalletData({
                    balance: createResult.data.balance || 0,
                    currency: createResult.data.currency || 'UGX',
                    account_number: createResult.data.account_number,
                    status: 'active',
                    daily_limit: 500000,
                    monthly_limit: 2000000,
                    spent_today: 0,
                    spent_this_month: 0,
                    last_transaction: new Date().toISOString(),
                  });
                } else {
                  console.error('❌ Failed to create wallet:', createResult.error);
                  setWalletData({
                    balance: 0,
                    currency: 'UGX',
                    account_number: 'Creation Failed',
                    status: 'error',
                    daily_limit: 500000,
                    monthly_limit: 2000000,
                    spent_today: 0,
                    spent_this_month: 0,
                    last_transaction: new Date().toISOString(),
                  });
                }
              } else {
                console.error('❌ No user found for wallet creation');
                setWalletData({
                  balance: 0,
                  currency: 'UGX',
                  account_number: 'Login Required',
                  status: 'error',
                  daily_limit: 500000,
                  monthly_limit: 2000000,
                  spent_today: 0,
                  spent_this_month: 0,
                  last_transaction: new Date().toISOString(),
                });
              }
            } catch (createError) {
              console.error('❌ Error creating wallet:', createError);
              setWalletData({
                balance: 0,
                currency: 'UGX',
                account_number: 'Not Available',
                status: 'error',
                daily_limit: 500000,
                monthly_limit: 2000000,
                spent_today: 0,
                spent_this_month: 0,
                last_transaction: new Date().toISOString(),
              });
            }
          }
        } catch (walletError) {
          console.error('❌ Error loading wallet from service:', walletError);
          setWalletData({
            balance: 0,
            currency: 'UGX',
            account_number: 'Error Loading',
            status: 'error',
            daily_limit: 500000,
            monthly_limit: 2000000,
            spent_today: 0,
            spent_this_month: 0,
            last_transaction: new Date().toISOString(),
          });
        }
        
        // Load recent transactions from service
        await loadRecentTransactions();
      }
    } catch (error) {
      console.error('❌ Error loading wallet data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentTransactions = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser?.id) {
        console.log('⚠️ No user ID available for transactions in WalletScreen');
        return;
      }

      // Fix parameter order: (limit, userId) not (userId, limit)
      const transactions = await walletService.getRecentTransactions(5, currentUser.id);
      if (transactions.success && transactions.data) {
        // Convert service data format to component format for compatibility
        const formattedTransactions = transactions.data.map(transaction => ({
          id: transaction.id,
          type: transaction.transaction_type === 'deposit' ? 'credit' : 'debit',
          amount: transaction.amount,
          description: transaction.description,
          timestamp: new Date(transaction.created_at),
          status: transaction.status,
          reference: transaction.reference_number
        }));
        setRecentTransactions(formattedTransactions);
        console.log('✅ Recent transactions loaded in WalletScreen:', formattedTransactions.length);
      } else {
        // Handle case where no transactions are returned
        console.log('ℹ️ No transactions found for user');
        setRecentTransactions([]);
      }
    } catch (error) {
      console.error('❌ Error loading recent transactions in WalletScreen:', error);
      // Set empty array to prevent map errors
      setRecentTransactions([]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadWalletData(),
      loadRecentTransactions()
    ]);
    setRefreshing(false);
  };

  const toggleBalanceVisibility = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setBalanceVisible(!balanceVisible);
  };

  const formatCurrency = (amount, currency = null) => {
    try {
      // Use the enhanced currency service with user's preferred currency
      const targetCurrency = currency || userPreferences?.preferred_currency || 'UGX';
      return currencyService.formatAmountWithCurrency(amount, targetCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false
      });
    } catch (error) {
      console.error('❌ Error formatting currency:', error);
      // Fallback to simple formatting
      const targetCurrency = currency || userPreferences?.preferred_currency || 'UGX';
      const currencySymbols = {
        'UGX': 'UGX',
        'KES': 'KSh',
        'TZS': 'TSh',
        'RWF': 'RWF',
        'BIF': 'BIF',
        'ETB': 'ETB'
      };

      const symbol = currencySymbols[targetCurrency] || targetCurrency;
      return `${symbol} ${amount.toLocaleString()}`;
    }
  };

  // Convert UGX amounts to user's preferred currency and format
  const formatCurrencyFromUGX = (ugxAmount) => {
    try {
      const targetCurrency = userPreferences?.preferred_currency || 'UGX';
      return currencyService.convertFromUGXAndFormat(ugxAmount, targetCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false
      });
    } catch (error) {
      console.error('❌ Error converting currency from UGX:', error);
      // Fallback to UGX formatting
      return `UGX ${ugxAmount.toLocaleString()}`;
    }
  };

  const getTransactionIcon = (type) => {
    return type === 'credit' ? 'arrow-down-circle' : 'arrow-up-circle';
  };

  const getTransactionColor = (type) => {
    return type === 'credit' ? Colors.status.success : Colors.accent.coral;
  };

  const handleTopUp = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      // TODO: Add biometric authentication when dependencies are installed

      if (navigation && navigation.navigate) {
        navigation.navigate('TopUp');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error, showing top-up options:', error);
      Alert.alert(t('common.topUpWalletAction'), t('common.addMoneyToYourJiranipayWallet'),
        [
          { text: t('common.cancel'), style: 'cancel' },
          {
            text: t('common.mobileMoneyOption'),
            onPress: () => {
              Alert.alert(t('common.mobileMoneyTopup'), t('common.toTopUpViaMobileMoney')
              );
            }
          },
          {
            text: t('common.bankTransferOption'),
            onPress: () => {
              Alert.alert(t('common.bankTransferDetails'), t('common.transferToStanbicBank')
              );
            }
          }
        ]
      );
    }
  };

  const handleSendMoney = () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      if (navigation && navigation.navigate) {
        navigation.navigate('SendMoney');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('common.sendMoney'), t('common.sendMoneyToContacts')
      );

      // Fallback navigation attempt
      setTimeout(() => {
        try {
          navigation.navigate('SendMoney');
        } catch (fallbackError) {
          console.error('Fallback navigation failed:', fallbackError);
        }
      }, 1000);
    }
  };

  const handleTransactionHistory = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('TransactionHistory');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('common.transactionHistory'), t('dashboard.alerts.transactionHistoryDesc'));
    }
  };

  const handleSavings = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('SavingsDashboard');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('savings'), t('common.accessYourSavingsAccountsAndGoals'));
    }
  };

  const handleWalletSettings = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('WalletSettings');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('common.walletSettings'), t('common.manageYourWallet'));
    }
  };

  const handleAnalytics = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('Analytics');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('common.analytics'), t('common.viewYourSpendingInsights'));
    }
  };

  const handleReceiveQR = () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      if (navigation && navigation.navigate) {
        navigation.navigate('QRGenerator', { type: 'receive' });
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('common.receiveViaQr'), t('common.generateQrCodesToReceivePayments')
      );

      // Fallback navigation attempt
      setTimeout(() => {
        try {
          navigation.navigate('QRGenerator', { type: 'receive' });
        } catch (fallbackError) {
          console.error('Fallback navigation failed:', fallbackError);
        }
      }, 1000);
    }
  };

  const handleTransactionPress = (transaction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Convert wallet screen transaction format to service format for modal compatibility
    const formattedTransaction = {
      id: transaction.id,
      transaction_type: transaction.type === 'credit' ? 'deposit' : 'bill_payment',
      amount: transaction.amount,
      currency: 'UGX',
      description: transaction.description,
      created_at: transaction.timestamp.toISOString(),
      reference_number: transaction.reference,
      status: transaction.status,
      provider: transaction.provider || null,
      category: transaction.category || null,
      account_number: transaction.account_number || null
    };
    setSelectedTransaction(formattedTransaction);
    setTransactionModalVisible(true);
  };

  const handleCloseTransactionModal = () => {
    setTransactionModalVisible(false);
    setSelectedTransaction(null);
  };

  const renderWalletCard = () => (
    <LinearGradient
      colors={Colors.gradients.sunset}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.walletCard}
    >
      <View style={styles.walletHeader}>
        <View>
          <Text style={styles.walletTitle}>{t('wallet.walletBalance')}</Text>
          <Text style={styles.accountNumber}>{t('wallet.account')}: {walletData?.account_number}</Text>
        </View>
        <TouchableOpacity onPress={toggleBalanceVisibility} style={styles.visibilityButton}>
          <Ionicons 
            name={balanceVisible ? 'eye-outline' : 'eye-off-outline'} 
            size={24} 
            color={Colors.neutral.white} 
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceAmount}>
          {balanceVisible
            ? convertAndFormat(walletData?.balance || 0)
            : '••••••'
          }
        </Text>
        <Text style={styles.balanceSubtext}>{t('common.availableBalance')}</Text>
      </View>
      
      <View style={styles.walletActions}>
        <TouchableOpacity style={styles.actionButton} onPress={handleTopUp}>
          <Ionicons name="add-circle-outline" size={20} color={Colors.neutral.white} />
          <Text style={styles.actionText}>{t('common.topUp')}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleSendMoney}>
          <Ionicons name="send-outline" size={20} color={Colors.neutral.white} />
          <Text style={styles.actionText}>{t('common.send')}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleTransactionHistory}>
          <Ionicons name="list-outline" size={20} color={Colors.neutral.white} />
          <Text style={styles.actionText}>{t('common.history')}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleSavings}>
          <Ionicons name="wallet-outline" size={20} color={Colors.neutral.white} />
          <Text style={styles.actionText}>{t('savings')}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleWalletSettings}>
          <Ionicons name="settings-outline" size={20} color={Colors.neutral.white} />
          <Text style={styles.actionText}>{t('common.settings')}</Text>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  const renderSpendingLimits = () => (
    <View style={styles.compactSection}>
      <Text style={styles.compactSectionTitle}>{t('common.spendingLimits')}</Text>

      <View style={styles.limitsContainer}>
        <View style={styles.compactLimitCard}>
          <View style={styles.compactLimitHeader}>
            <Ionicons name="calendar-outline" size={16} color={Colors.primary.main} />
            <Text style={styles.compactLimitTitle}>{t('common.daily')}</Text>
          </View>
          <Text style={styles.compactLimitAmount}>
            {formatCurrencyFromUGX(walletData?.spent_today || 0)}
          </Text>
          <Text style={styles.compactLimitTotal}>
            of {formatCurrencyFromUGX(walletData?.daily_limit || 500000)}
          </Text>
          <View style={styles.compactProgressBar}>
            <View
              style={[
                styles.compactProgressFill,
                {
                  width: `${Math.min((walletData?.spent_today || 0) / (walletData?.daily_limit || 500000) * 100, 100)}%`,
                  backgroundColor: (walletData?.spent_today || 0) / (walletData?.daily_limit || 500000) > 0.8
                    ? Colors.status.error
                    : Colors.primary.main
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.compactLimitCard}>
          <View style={styles.compactLimitHeader}>
            <Ionicons name="calendar-outline" size={16} color={Colors.secondary.savanna} />
            <Text style={styles.compactLimitTitle}>{t('common.monthly')}</Text>
          </View>
          <Text style={styles.compactLimitAmount}>
            {formatCurrencyFromUGX(walletData?.spent_this_month || 0)}
          </Text>
          <Text style={styles.compactLimitTotal}>
            of {formatCurrencyFromUGX(walletData?.monthly_limit || 2000000)}
          </Text>
          <View style={styles.compactProgressBar}>
            <View
              style={[
                styles.compactProgressFill,
                {
                  width: `${Math.min((walletData?.spent_this_month || 0) / (walletData?.monthly_limit || 2000000) * 100, 100)}%`,
                  backgroundColor: (walletData?.spent_this_month || 0) / (walletData?.monthly_limit || 2000000) > 0.8
                    ? Colors.status.error
                    : Colors.secondary.savanna
                }
              ]}
            />
          </View>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('common.loadingWallet')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t('common.myWallet')}</Text>
        <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
          <Ionicons name="refresh-outline" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderWalletCard()}
        {renderSpendingLimits()}
        
        {/* Recent Transactions */}
        <View style={styles.compactSection}>
          <View style={styles.compactSectionHeader}>
            <Text style={styles.compactSectionTitle}>{t('wallet.recentTransactions')}</Text>
            <TouchableOpacity onPress={handleTransactionHistory}>
              <Text style={styles.viewAllText}>{t('wallet.viewAll')}</Text>
            </TouchableOpacity>
          </View>

          {recentTransactions.length > 0 ? (
            recentTransactions.slice(0, 3).map((transaction) => (
              <TouchableOpacity
                key={transaction.id}
                style={styles.compactTransactionItem}
                onPress={() => handleTransactionPress(transaction)}
                activeOpacity={0.7}
              >
                <View style={styles.compactTransactionLeft}>
                  <View style={[styles.compactTransactionIcon, { backgroundColor: getTransactionColor(transaction.type) + '20' }]}>
                    <Ionicons
                      name={getTransactionIcon(transaction.type)}
                      size={16}
                      color={getTransactionColor(transaction.type)}
                    />
                  </View>
                  <View style={styles.compactTransactionDetails}>
                    <Text style={styles.compactTransactionDescription}>{transaction.description}</Text>
                    <Text style={styles.compactTransactionTime}>
                      {transaction.timestamp.toLocaleDateString()}
                    </Text>
                  </View>
                </View>
                <Text style={[
                  styles.compactTransactionAmount,
                  { color: getTransactionColor(transaction.type) }
                ]}>
                  {transaction.type === 'credit' ? '+' : '-'}{convertAndFormat(transaction.amount)}
                </Text>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyTransactionsContainer}>
              <Ionicons name="receipt-outline" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.emptyTransactionsTitle}>{t('common.noTransactionsYet')}</Text>
              <Text style={styles.emptyTransactionsSubtitle}>
                {t('common.startUsingYourJiranipayWallet')}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Transaction Detail Modal */}
      <TransactionDetailModal
        visible={transactionModalVisible}
        onClose={handleCloseTransactionModal}
        transaction={selectedTransaction}
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  refreshButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  walletCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  walletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  walletTitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  accountNumber: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.7,
    marginTop: 4,
  },
  visibilityButton: {
    padding: 4,
  },
  balanceContainer: {
    marginBottom: 24,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 4,
  },
  balanceSubtext: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.8,
  },
  walletActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
  },
  actionText: {
    fontSize: 12,
    color: Colors.neutral.white,
    marginTop: 4,
    fontWeight: '500',
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  compactSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    padding: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  compactSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  compactSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  viewAllText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  limitCard: {
    backgroundColor: Colors.neutral.creamLight,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  limitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  compactLimitCard: {
    backgroundColor: Colors.neutral.creamLight,
    padding: 12,
    borderRadius: 10,
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  compactLimitHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  compactLimitTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginLeft: 4,
  },
  compactLimitAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    textAlign: 'center',
    marginBottom: 2,
  },
  compactLimitTotal: {
    fontSize: 10,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginBottom: 8,
  },
  compactProgressBar: {
    height: 4,
    backgroundColor: Colors.neutral.creamDark,
    borderRadius: 2,
    overflow: 'hidden',
    width: '100%',
  },
  compactProgressFill: {
    height: '100%',
    borderRadius: 2,
  },
  limitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  limitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  limitAmount: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    fontWeight: '500',
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.neutral.creamDark,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  compactTransactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactTransactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  compactTransactionIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  transactionDetails: {
    flex: 1,
  },
  compactTransactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  compactTransactionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
    marginBottom: 1,
  },
  transactionTime: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  compactTransactionTime: {
    fontSize: 10,
    color: Colors.neutral.warmGray,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  compactTransactionAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  bottomSpacing: {
    height: 20,
  },
  emptyTransactionsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  emptyTransactionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 12,
    marginBottom: 4,
  },
  emptyTransactionsSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default WalletScreen;
