/**
 * Analytics Export Service
 * Handles PDF and Excel export of analytics reports with charts
 */

import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

class AnalyticsExportService {
  constructor() {
    this.exportFormats = {
      PDF: 'pdf',
      EXCEL: 'excel',
      CSV: 'csv'
    };
  }

  /**
   * Export analytics report as PDF
   */
  async exportToPDF(analyticsData, options = {}) {
    try {
      const {
        title = 'Financial Analytics Report',
        period = 'month',
        includeCharts = true,
        userInfo = {}
      } = options;

      console.log('📄 Generating PDF analytics report...');

      // Generate HTML content
      const htmlContent = this.generatePDFHTML(analyticsData, {
        title,
        period,
        includeCharts,
        userInfo
      });

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
        width: 612, // Letter size width in points
        height: 792, // Letter size height in points
        margins: {
          left: 40,
          top: 40,
          right: 40,
          bottom: 40
        }
      });

      console.log('✅ PDF generated successfully:', uri);

      return {
        success: true,
        uri,
        fileName: `analytics_report_${new Date().toISOString().split('T')[0]}.pdf`,
        format: 'pdf'
      };
    } catch (error) {
      console.error('❌ Error generating PDF:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Export analytics data as Excel/CSV
   */
  async exportToExcel(analyticsData, options = {}) {
    try {
      const {
        title = 'Financial Analytics Report',
        period = 'month',
        format = 'csv'
      } = options;

      console.log('📊 Generating Excel/CSV analytics report...');

      // Generate CSV content
      const csvContent = this.generateCSVContent(analyticsData, { title, period });

      // Save to file
      const fileName = `analytics_report_${new Date().toISOString().split('T')[0]}.${format}`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      console.log('✅ Excel/CSV generated successfully:', fileUri);

      return {
        success: true,
        uri: fileUri,
        fileName,
        format
      };
    } catch (error) {
      console.error('❌ Error generating Excel/CSV:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Share exported report
   */
  async shareReport(exportResult, options = {}) {
    try {
      if (!exportResult.success) {
        throw new Error('Export failed, cannot share');
      }

      const {
        dialogTitle = 'Share Analytics Report',
        mimeType = exportResult.format === 'pdf' ? 'application/pdf' : 'text/csv'
      } = options;

      const isAvailable = await Sharing.isAvailableAsync();
      
      if (!isAvailable) {
        Alert.alert(
          'Export Complete',
          `Report saved successfully!\nLocation: ${exportResult.fileName}`,
          [{ text: 'OK' }]
        );
        return { success: true, shared: false };
      }

      await Sharing.shareAsync(exportResult.uri, {
        mimeType,
        dialogTitle,
        UTI: exportResult.format === 'pdf' ? 'com.adobe.pdf' : 'public.comma-separated-values-text'
      });

      return { success: true, shared: true };
    } catch (error) {
      console.error('❌ Error sharing report:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate PDF HTML content
   */
  generatePDFHTML(analyticsData, options) {
    const { title, period, userInfo } = options;
    const { summary, wallet, transactions, savings, investments, insights } = analyticsData;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            color: #2C3E50;
            line-height: 1.6;
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #E67E22;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .header h1 {
            color: #E67E22;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
          }
          .header p {
            color: #7F8C8D;
            margin: 10px 0 0 0;
            font-size: 14px;
          }
          .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
          }
          .summary-card {
            background: linear-gradient(135deg, #E67E22, #F39C12);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
          }
          .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
          }
          .summary-card .value {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
          }
          .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
          }
          .section h2 {
            color: #E67E22;
            border-bottom: 2px solid #F39C12;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 20px;
          }
          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
          }
          .metric-item {
            background: #F8F9FA;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #E67E22;
          }
          .metric-label {
            font-size: 12px;
            color: #7F8C8D;
            margin-bottom: 5px;
            font-weight: 500;
          }
          .metric-value {
            font-size: 18px;
            font-weight: 700;
            color: #2C3E50;
          }
          .insights-list {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #27AE60;
          }
          .insight-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #E9ECEF;
          }
          .insight-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
          }
          .insight-title {
            font-weight: 600;
            color: #2C3E50;
            margin-bottom: 5px;
          }
          .insight-description {
            color: #7F8C8D;
            font-size: 14px;
          }
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E9ECEF;
            text-align: center;
            color: #7F8C8D;
            font-size: 12px;
          }
          .page-break {
            page-break-before: always;
          }
          @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${title}</h1>
          <p>Period: ${period.charAt(0).toUpperCase() + period.slice(1)} | Generated: ${new Date().toLocaleDateString()}</p>
          ${userInfo.name ? `<p>Account: ${userInfo.name}</p>` : ''}
        </div>

        <!-- Summary Section -->
        <div class="section">
          <h2>Financial Summary</h2>
          <div class="summary-grid">
            <div class="summary-card">
              <h3>Net Worth</h3>
              <p class="value">UGX ${(summary.totalNetWorth || 0).toLocaleString()}</p>
            </div>
            <div class="summary-card">
              <h3>Monthly Income</h3>
              <p class="value">UGX ${(summary.monthlyIncome || 0).toLocaleString()}</p>
            </div>
            <div class="summary-card">
              <h3>Monthly Expenses</h3>
              <p class="value">UGX ${(summary.monthlyExpenses || 0).toLocaleString()}</p>
            </div>
            <div class="summary-card">
              <h3>Savings Rate</h3>
              <p class="value">${(summary.savingsRate || 0).toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <!-- Wallet Section -->
        <div class="section">
          <h2>Wallet Overview</h2>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">Current Balance</div>
              <div class="metric-value">UGX ${(wallet.currentBalance || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Available Balance</div>
              <div class="metric-value">UGX ${(wallet.availableBalance || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Pending Balance</div>
              <div class="metric-value">UGX ${(wallet.pendingBalance || 0).toLocaleString()}</div>
            </div>
          </div>
        </div>

        <!-- Transactions Section -->
        <div class="section">
          <h2>Transaction Analysis</h2>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">Total Transactions</div>
              <div class="metric-value">${transactions.totalTransactions || 0}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Spent</div>
              <div class="metric-value">UGX ${(transactions.totalSpent || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Received</div>
              <div class="metric-value">UGX ${(transactions.totalReceived || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Net Flow</div>
              <div class="metric-value">UGX ${(transactions.netFlow || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Average Transaction</div>
              <div class="metric-value">UGX ${(transactions.averageTransaction || 0).toLocaleString()}</div>
            </div>
          </div>
        </div>

        ${savings.totalAccounts > 0 ? `
        <!-- Savings Section -->
        <div class="section">
          <h2>Savings Overview</h2>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">Total Accounts</div>
              <div class="metric-value">${savings.totalAccounts}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Balance</div>
              <div class="metric-value">UGX ${(savings.totalBalance || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Deposits</div>
              <div class="metric-value">UGX ${(savings.totalDeposits || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Withdrawals</div>
              <div class="metric-value">UGX ${(savings.totalWithdrawals || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Net Savings</div>
              <div class="metric-value">UGX ${(savings.netSavings || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Savings Rate</div>
              <div class="metric-value">${(savings.savingsRate || 0).toFixed(1)}%</div>
            </div>
          </div>
        </div>
        ` : ''}

        ${investments.totalPortfolios > 0 ? `
        <!-- Investments Section -->
        <div class="section">
          <h2>Investment Overview</h2>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">Total Portfolios</div>
              <div class="metric-value">${investments.totalPortfolios}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Value</div>
              <div class="metric-value">UGX ${(investments.totalValue || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Invested</div>
              <div class="metric-value">UGX ${(investments.totalInvested || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Gain/Loss</div>
              <div class="metric-value">UGX ${(investments.totalGainLoss || 0).toLocaleString()}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Return</div>
              <div class="metric-value">${(investments.totalReturn || 0).toFixed(2)}%</div>
            </div>
          </div>
        </div>
        ` : ''}

        ${insights && insights.length > 0 ? `
        <!-- Insights Section -->
        <div class="section">
          <h2>Financial Insights</h2>
          <div class="insights-list">
            ${insights.map(insight => `
              <div class="insight-item">
                <div class="insight-title">${insight.title}</div>
                <div class="insight-description">${insight.description}</div>
              </div>
            `).join('')}
          </div>
        </div>
        ` : ''}

        <div class="footer">
          <p>Generated by JiraniPay Analytics | ${new Date().toLocaleString()}</p>
          <p>This report contains confidential financial information. Please handle with care.</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate CSV content for analytics data
   */
  generateCSVContent(analyticsData, options) {
    const { title, period } = options;
    const { summary, wallet, transactions, savings, investments, spendingCategories, monthlyTrends } = analyticsData;

    let csvContent = '';

    // Header
    csvContent += `${title}\n`;
    csvContent += `Period: ${period}\n`;
    csvContent += `Generated: ${new Date().toLocaleString()}\n\n`;

    // Summary Section
    csvContent += 'FINANCIAL SUMMARY\n';
    csvContent += 'Metric,Value,Currency\n';
    csvContent += `Net Worth,${summary.totalNetWorth || 0},UGX\n`;
    csvContent += `Monthly Income,${summary.monthlyIncome || 0},UGX\n`;
    csvContent += `Monthly Expenses,${summary.monthlyExpenses || 0},UGX\n`;
    csvContent += `Savings Rate,${(summary.savingsRate || 0).toFixed(2)},%\n`;
    csvContent += `Investment Return,${(summary.investmentReturn || 0).toFixed(2)},%\n\n`;

    // Wallet Section
    csvContent += 'WALLET OVERVIEW\n';
    csvContent += 'Metric,Value,Currency\n';
    csvContent += `Current Balance,${wallet.currentBalance || 0},UGX\n`;
    csvContent += `Available Balance,${wallet.availableBalance || 0},UGX\n`;
    csvContent += `Pending Balance,${wallet.pendingBalance || 0},UGX\n`;
    csvContent += `Daily Limit,${wallet.dailyLimit || 0},UGX\n`;
    csvContent += `Monthly Limit,${wallet.monthlyLimit || 0},UGX\n`;
    csvContent += `Spent Today,${wallet.spentToday || 0},UGX\n`;
    csvContent += `Spent This Month,${wallet.spentThisMonth || 0},UGX\n\n`;

    // Transaction Analysis
    csvContent += 'TRANSACTION ANALYSIS\n';
    csvContent += 'Metric,Value,Currency\n';
    csvContent += `Total Transactions,${transactions.totalTransactions || 0},Count\n`;
    csvContent += `Total Spent,${transactions.totalSpent || 0},UGX\n`;
    csvContent += `Total Received,${transactions.totalReceived || 0},UGX\n`;
    csvContent += `Net Flow,${transactions.netFlow || 0},UGX\n`;
    csvContent += `Average Transaction,${transactions.averageTransaction || 0},UGX\n\n`;

    // Spending by Category
    if (spendingCategories && spendingCategories.length > 0) {
      csvContent += 'SPENDING BY CATEGORY\n';
      csvContent += 'Category,Amount,Currency\n';
      spendingCategories.forEach(category => {
        csvContent += `${category.category},${category.amount},UGX\n`;
      });
      csvContent += '\n';
    }

    // Monthly Trends
    if (monthlyTrends && monthlyTrends.length > 0) {
      csvContent += 'MONTHLY TRENDS\n';
      csvContent += 'Month,Income,Expenses,Net,Currency\n';
      monthlyTrends.forEach(trend => {
        csvContent += `${trend.month},${trend.income || 0},${trend.expenses || 0},${trend.net || 0},UGX\n`;
      });
      csvContent += '\n';
    }

    // Savings Overview
    if (savings.totalAccounts > 0) {
      csvContent += 'SAVINGS OVERVIEW\n';
      csvContent += 'Metric,Value,Currency\n';
      csvContent += `Total Accounts,${savings.totalAccounts},Count\n`;
      csvContent += `Total Balance,${savings.totalBalance || 0},UGX\n`;
      csvContent += `Total Deposits,${savings.totalDeposits || 0},UGX\n`;
      csvContent += `Total Withdrawals,${savings.totalWithdrawals || 0},UGX\n`;
      csvContent += `Net Savings,${savings.netSavings || 0},UGX\n`;
      csvContent += `Savings Rate,${(savings.savingsRate || 0).toFixed(2)},%\n\n`;
    }

    // Investment Overview
    if (investments.totalPortfolios > 0) {
      csvContent += 'INVESTMENT OVERVIEW\n';
      csvContent += 'Metric,Value,Currency\n';
      csvContent += `Total Portfolios,${investments.totalPortfolios},Count\n`;
      csvContent += `Total Value,${investments.totalValue || 0},UGX\n`;
      csvContent += `Total Invested,${investments.totalInvested || 0},UGX\n`;
      csvContent += `Total Gain/Loss,${investments.totalGainLoss || 0},UGX\n`;
      csvContent += `Total Return,${(investments.totalReturn || 0).toFixed(2)},%\n\n`;
    }

    return csvContent;
  }

  /**
   * Export specific chart data
   */
  async exportChartData(chartType, data, options = {}) {
    try {
      const { format = 'pdf' } = options;

      if (format === 'pdf') {
        return await this.exportChartToPDF(chartType, data, options);
      } else {
        return await this.exportChartToCSV(chartType, data, options);
      }
    } catch (error) {
      console.error('❌ Error exporting chart data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Export chart as PDF
   */
  async exportChartToPDF(chartType, data, options) {
    const chartTitles = {
      spending_trend: 'Spending Trends Analysis',
      category_spending: 'Spending by Category',
      monthly_comparison: 'Monthly Comparison',
      savings_progress: 'Savings Goals Progress',
      investment_performance: 'Investment Performance'
    };

    const title = chartTitles[chartType] || 'Chart Export';

    // Generate simplified HTML for chart data
    const htmlContent = this.generateChartHTML(chartType, data, { ...options, title });

    const { uri } = await Print.printToFileAsync({
      html: htmlContent,
      base64: false
    });

    return {
      success: true,
      uri,
      fileName: `${chartType}_${new Date().toISOString().split('T')[0]}.pdf`,
      format: 'pdf'
    };
  }

  /**
   * Export chart as CSV
   */
  async exportChartToCSV(chartType, data, options) {
    const csvContent = this.generateChartCSV(chartType, data, options);

    const fileName = `${chartType}_${new Date().toISOString().split('T')[0]}.csv`;
    const fileUri = `${FileSystem.documentDirectory}${fileName}`;

    await FileSystem.writeAsStringAsync(fileUri, csvContent, {
      encoding: FileSystem.EncodingType.UTF8
    });

    return {
      success: true,
      uri: fileUri,
      fileName,
      format: 'csv'
    };
  }

  /**
   * Generate chart-specific HTML
   */
  generateChartHTML(chartType, data, options) {
    const { title } = options;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #E67E22; text-align: center; }
          .chart-data { margin: 20px 0; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <h1>${title}</h1>
        <p>Generated: ${new Date().toLocaleString()}</p>
        <div class="chart-data">
          ${this.generateChartTable(chartType, data)}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate chart table HTML
   */
  generateChartTable(chartType, data) {
    switch (chartType) {
      case 'spending_trend':
        return this.generateSpendingTrendTable(data);
      case 'category_spending':
        return this.generateCategoryTable(data);
      case 'monthly_comparison':
        return this.generateMonthlyTable(data);
      default:
        return '<p>Chart data not available</p>';
    }
  }

  /**
   * Generate spending trend table
   */
  generateSpendingTrendTable(data) {
    if (!data || data.length === 0) return '<p>No data available</p>';

    let table = '<table><tr><th>Month</th><th>Income (UGX)</th><th>Expenses (UGX)</th><th>Net (UGX)</th></tr>';
    data.forEach(item => {
      table += `<tr>
        <td>${item.month}</td>
        <td>${(item.income || 0).toLocaleString()}</td>
        <td>${(item.expenses || 0).toLocaleString()}</td>
        <td>${(item.net || 0).toLocaleString()}</td>
      </tr>`;
    });
    table += '</table>';
    return table;
  }

  /**
   * Generate category table
   */
  generateCategoryTable(data) {
    if (!data || data.length === 0) return '<p>No data available</p>';

    let table = '<table><tr><th>Category</th><th>Amount (UGX)</th><th>Percentage</th></tr>';
    const total = data.reduce((sum, item) => sum + item.amount, 0);

    data.forEach(item => {
      const percentage = total > 0 ? ((item.amount / total) * 100).toFixed(1) : 0;
      table += `<tr>
        <td>${item.category}</td>
        <td>${item.amount.toLocaleString()}</td>
        <td>${percentage}%</td>
      </tr>`;
    });
    table += '</table>';
    return table;
  }

  /**
   * Generate monthly table
   */
  generateMonthlyTable(data) {
    return this.generateSpendingTrendTable(data);
  }

  /**
   * Generate chart-specific CSV
   */
  generateChartCSV(chartType, data, options) {
    const { title } = options;
    let csvContent = `${title}\nGenerated: ${new Date().toLocaleString()}\n\n`;

    switch (chartType) {
      case 'spending_trend':
      case 'monthly_comparison':
        csvContent += 'Month,Income,Expenses,Net,Currency\n';
        if (data && data.length > 0) {
          data.forEach(item => {
            csvContent += `${item.month},${item.income || 0},${item.expenses || 0},${item.net || 0},UGX\n`;
          });
        }
        break;

      case 'category_spending':
        csvContent += 'Category,Amount,Currency\n';
        if (data && data.length > 0) {
          data.forEach(item => {
            csvContent += `${item.category},${item.amount},UGX\n`;
          });
        }
        break;

      default:
        csvContent += 'No data available for this chart type\n';
    }

    return csvContent;
  }
}

export default new AnalyticsExportService();
