/**
 * Analytics Performance Service
 * Handles performance optimization for analytics data
 * Includes caching, pagination, and query optimization
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import supabase from './supabaseClient';

class AnalyticsPerformanceService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.maxCacheSize = 50; // Maximum number of cached items
    this.queryBatchSize = 100; // Default batch size for queries
    this.persistentCacheKey = 'analytics_persistent_cache';
  }

  /**
   * Initialize performance service
   */
  async initialize() {
    try {
      console.log('🚀 Initializing analytics performance service...');
      
      // Load persistent cache
      await this.loadPersistentCache();
      
      // Set up cache cleanup interval
      this.setupCacheCleanup();
      
      console.log('✅ Analytics performance service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing performance service:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get data with caching and optimization
   */
  async getOptimizedData(key, queryFunction, options = {}) {
    try {
      const {
        useCache = true,
        cacheTTL = this.cacheTimeout,
        forceRefresh = false,
        pagination = null
      } = options;

      // Check cache first
      if (useCache && !forceRefresh) {
        const cached = this.getFromCache(key);
        if (cached) {
          console.log('📊 Cache hit for:', key);
          return { success: true, data: cached, fromCache: true };
        }
      }

      console.log('📊 Cache miss, fetching data for:', key);

      // Execute query with optimization
      let data;
      if (pagination) {
        data = await this.executePaginatedQuery(queryFunction, pagination);
      } else {
        data = await queryFunction();
      }

      // Cache the result
      if (useCache) {
        this.setCache(key, data, cacheTTL);
      }

      return { success: true, data, fromCache: false };
    } catch (error) {
      console.error('❌ Error getting optimized data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Execute paginated query
   */
  async executePaginatedQuery(queryFunction, pagination) {
    const { page = 0, limit = this.queryBatchSize } = pagination;
    const offset = page * limit;

    return await queryFunction({ offset, limit });
  }

  /**
   * Batch multiple queries for efficiency
   */
  async batchQueries(queries) {
    try {
      console.log('📊 Executing batch queries:', queries.length);
      
      const results = await Promise.allSettled(
        queries.map(async (query) => {
          const { key, queryFunction, options = {} } = query;
          return await this.getOptimizedData(key, queryFunction, options);
        })
      );

      const batchResults = {};
      results.forEach((result, index) => {
        const query = queries[index];
        if (result.status === 'fulfilled') {
          batchResults[query.key] = result.value;
        } else {
          batchResults[query.key] = { 
            success: false, 
            error: result.reason?.message || 'Query failed' 
          };
        }
      });

      console.log('✅ Batch queries completed');
      return { success: true, results: batchResults };
    } catch (error) {
      console.error('❌ Error executing batch queries:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Optimize Supabase queries
   */
  optimizeSupabaseQuery(baseQuery, options = {}) {
    const {
      selectFields = '*',
      limit = this.queryBatchSize,
      orderBy = 'created_at',
      orderDirection = 'desc',
      filters = []
    } = options;

    let query = baseQuery.select(selectFields);

    // Apply filters
    filters.forEach(filter => {
      const { column, operator, value } = filter;
      switch (operator) {
        case 'eq':
          query = query.eq(column, value);
          break;
        case 'gte':
          query = query.gte(column, value);
          break;
        case 'lte':
          query = query.lte(column, value);
          break;
        case 'in':
          query = query.in(column, value);
          break;
        case 'like':
          query = query.like(column, value);
          break;
        default:
          console.warn('Unknown filter operator:', operator);
      }
    });

    // Apply ordering and limit
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });
    
    if (limit) {
      query = query.limit(limit);
    }

    return query;
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  setCache(key, data, ttl = this.cacheTimeout) {
    // Implement LRU cache eviction
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });

    // Persist important cache entries
    this.persistCacheEntry(key, data, ttl);
  }

  invalidateCache(pattern = null) {
    if (pattern) {
      // Invalidate cache entries matching pattern
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.cache.clear();
    }
    
    console.log('🗑️ Cache invalidated:', pattern || 'all');
  }

  /**
   * Persistent cache management
   */
  async loadPersistentCache() {
    try {
      const persistentCache = await AsyncStorage.getItem(this.persistentCacheKey);
      if (persistentCache) {
        const parsed = JSON.parse(persistentCache);
        
        // Load non-expired entries
        Object.entries(parsed).forEach(([key, entry]) => {
          if (Date.now() - entry.timestamp < entry.ttl) {
            this.cache.set(key, entry);
          }
        });
        
        console.log('📦 Loaded persistent cache entries:', this.cache.size);
      }
    } catch (error) {
      console.error('❌ Error loading persistent cache:', error);
    }
  }

  async persistCacheEntry(key, data, ttl) {
    try {
      // Only persist important analytics data
      const importantKeys = ['dashboard_analytics', 'monthly_trends', 'spending_categories'];
      const shouldPersist = importantKeys.some(pattern => key.includes(pattern));
      
      if (!shouldPersist) return;

      const persistentCache = await AsyncStorage.getItem(this.persistentCacheKey);
      const cache = persistentCache ? JSON.parse(persistentCache) : {};
      
      cache[key] = {
        data,
        timestamp: Date.now(),
        ttl
      };

      // Limit persistent cache size
      const entries = Object.entries(cache);
      if (entries.length > 10) {
        // Keep only the 10 most recent entries
        const sorted = entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
        const limited = Object.fromEntries(sorted.slice(0, 10));
        await AsyncStorage.setItem(this.persistentCacheKey, JSON.stringify(limited));
      } else {
        await AsyncStorage.setItem(this.persistentCacheKey, JSON.stringify(cache));
      }
    } catch (error) {
      console.error('❌ Error persisting cache entry:', error);
    }
  }

  /**
   * Query optimization helpers
   */
  createOptimizedTransactionQuery(userId, dateRange, options = {}) {
    const baseQuery = supabase
      .from('transactions')
      .select('id, amount, transaction_type, category, status, created_at')
      .eq('user_id', userId);

    const filters = [];
    
    if (dateRange.start) {
      filters.push({ column: 'created_at', operator: 'gte', value: dateRange.start });
    }
    
    if (dateRange.end) {
      filters.push({ column: 'created_at', operator: 'lte', value: dateRange.end });
    }

    return this.optimizeSupabaseQuery(baseQuery, {
      ...options,
      filters
    });
  }

  createOptimizedSavingsQuery(userId, options = {}) {
    const baseQuery = supabase
      .from('savings_transactions')
      .select('id, amount, transaction_type, balance_after, created_at')
      .eq('user_id', userId);

    return this.optimizeSupabaseQuery(baseQuery, options);
  }

  createOptimizedInvestmentQuery(userId, options = {}) {
    const baseQuery = supabase
      .from('investment_transactions')
      .select('id, transaction_type, quantity, price, total_amount, created_at')
      .eq('user_id', userId);

    return this.optimizeSupabaseQuery(baseQuery, options);
  }

  /**
   * Performance monitoring
   */
  startPerformanceTimer(operation) {
    const startTime = Date.now();
    return {
      end: () => {
        const duration = Date.now() - startTime;
        console.log(`⏱️ ${operation} took ${duration}ms`);
        return duration;
      }
    };
  }

  /**
   * Memory management
   */
  setupCacheCleanup() {
    // Clean up expired cache entries every 10 minutes
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp >= entry.ttl) {
          this.cache.delete(key);
        }
      }
      console.log('🧹 Cache cleanup completed, entries:', this.cache.size);
    }, 10 * 60 * 1000);
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const stats = {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      entries: Array.from(this.cache.keys()),
      memoryUsage: this.estimateMemoryUsage()
    };

    return stats;
  }

  estimateMemoryUsage() {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += JSON.stringify(entry.data).length;
    }
    return `${(totalSize / 1024).toFixed(2)} KB`;
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.cache.clear();
    console.log('🧹 Analytics performance service cleaned up');
  }
}

export default new AnalyticsPerformanceService();
