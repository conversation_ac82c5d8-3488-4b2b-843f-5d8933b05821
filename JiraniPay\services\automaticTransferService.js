/**
 * Automatic Transfer Service
 * Service for managing automated transfers to savings accounts
 * with scheduling, failure handling, and retry mechanisms
 */

import { supabase } from './supabaseClient';
import savingsAccountService from './savingsAccountService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';
import { isValidUUID } from '../utils/userUtils';

class AutomaticTransferService {
  constructor() {
    this.processingQueue = new Map();
    this.retryQueue = new Map();
    this.maxRetryAttempts = 3;
    this.retryDelayHours = 24;
    this.processingInterval = 60 * 60 * 1000; // 1 hour
    
    // Start the processor
    this.startProcessor();
  }

  /**
   * Start the automatic transfer processor
   */
  startProcessor() {
    console.log('🔄 Starting automatic transfer processor');
    
    // Process immediately on startup
    this.processScheduledTransfers();
    
    // Set up interval processing
    setInterval(() => {
      this.processScheduledTransfers();
    }, this.processingInterval);
  }

  /**
   * Process all scheduled transfers
   */
  async processScheduledTransfers() {
    try {
      console.log('🔄 Processing scheduled transfers...');

      // Get due transfers
      const dueTransfers = await this.getDueTransfers();
      console.log(`📋 Found ${dueTransfers.length} due transfers`);

      if (dueTransfers.length === 0) {
        return;
      }

      // Process transfers
      for (const transfer of dueTransfers) {
        await this.processTransfer(transfer);
        // Small delay between transfers to prevent overwhelming the system
        await this.delay(1000);
      }

      console.log('✅ Scheduled transfer processing completed');
    } catch (error) {
      console.error('❌ Error in automatic transfer processor:', error);
    }
  }

  /**
   * Get all due automatic transfers
   */
  async getDueTransfers() {
    try {
      const now = new Date().toISOString();
      
      const { data: accounts, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('auto_transfer_enabled', true)
        .eq('is_active', true)
        .eq('is_locked', false)
        .lte('next_auto_transfer', now)
        .order('next_auto_transfer', { ascending: true });

      if (error) {
        console.error('❌ Error fetching due transfers:', error);
        return [];
      }

      return accounts || [];
    } catch (error) {
      console.error('❌ Error getting due transfers:', error);
      return [];
    }
  }

  /**
   * Process individual automatic transfer
   */
  async processTransfer(account) {
    const transferId = `${account.id}_${Date.now()}`;
    
    try {
      console.log(`🔄 Processing automatic transfer for account: ${account.id}`);

      // Check if already processing
      if (this.processingQueue.has(account.id)) {
        console.log(`⏳ Transfer for account ${account.id} already being processed`);
        return;
      }

      // Add to processing queue
      this.processingQueue.set(account.id, {
        startTime: Date.now(),
        transferId,
        status: 'processing'
      });

      // Validate transfer conditions
      const validation = await this.validateTransfer(account);
      if (!validation.isValid) {
        await this.handleTransferValidationFailure(account, validation.error);
        return;
      }

      // Execute the transfer
      const transferResult = await this.executeTransfer(account);
      
      if (transferResult.success) {
        await this.handleTransferSuccess(account, transferResult);
      } else {
        await this.handleTransferFailure(account, transferResult);
      }

    } catch (error) {
      console.error(`❌ Error processing transfer for account ${account.id}:`, error);
      await this.handleTransferError(account, error);
    } finally {
      // Remove from processing queue
      this.processingQueue.delete(account.id);
    }
  }

  /**
   * Validate transfer conditions
   */
  async validateTransfer(account) {
    try {
      // Check if account is still active and not locked
      if (!account.is_active || account.is_locked) {
        return {
          isValid: false,
          error: 'Account is not active or is locked'
        };
      }

      // Check if auto transfer is still enabled
      if (!account.auto_transfer_enabled) {
        return {
          isValid: false,
          error: 'Auto transfer is disabled'
        };
      }

      // Check if transfer amount is valid
      if (!account.auto_transfer_amount || account.auto_transfer_amount <= 0) {
        return {
          isValid: false,
          error: 'Invalid transfer amount'
        };
      }

      // Check source account balance (if applicable)
      if (account.auto_transfer_source_account === 'wallet') {
        // TODO: Check wallet balance
        // For now, assume wallet has sufficient balance
      }

      return {
        isValid: true
      };
    } catch (error) {
      console.error('❌ Error validating transfer:', error);
      return {
        isValid: false,
        error: 'Validation failed'
      };
    }
  }

  /**
   * Execute the automatic transfer
   */
  async executeTransfer(account) {
    try {
      console.log(`💰 Executing transfer: ${account.auto_transfer_amount} to account ${account.id}`);

      // Process the deposit
      const result = await savingsAccountService.processDeposit(
        account.user_id,
        account.id,
        account.auto_transfer_amount,
        'Automatic transfer',
        account.auto_transfer_source_account
      );

      return result;
    } catch (error) {
      console.error('❌ Error executing transfer:', error);
      return {
        success: false,
        error: error.message || 'Transfer execution failed'
      };
    }
  }

  /**
   * Handle successful transfer
   */
  async handleTransferSuccess(account, transferResult) {
    try {
      console.log(`✅ Transfer successful for account: ${account.id}`);

      // Calculate next transfer date
      const nextTransferDate = this.calculateNextTransferDate(
        account.next_auto_transfer,
        account.auto_transfer_frequency
      );

      // Update account with next transfer date and reset failure count
      await supabase
        .from('savings_accounts')
        .update({
          next_auto_transfer: nextTransferDate.toISOString(),
          metadata: {
            ...account.metadata,
            last_auto_transfer: new Date().toISOString(),
            auto_transfer_failures: 0,
            last_transfer_amount: account.auto_transfer_amount
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);

      // Send success notification
      await this.sendTransferNotification(account, 'success', {
        amount: account.auto_transfer_amount,
        newBalance: transferResult.newBalance,
        nextTransferDate,
        transactionId: transferResult.transaction?.id
      });

      // Log transfer success
      await this.logTransferEvent(account.id, 'success', {
        amount: account.auto_transfer_amount,
        transactionId: transferResult.transaction?.id,
        nextTransferDate
      });

    } catch (error) {
      console.error('❌ Error handling transfer success:', error);
    }
  }

  /**
   * Handle transfer failure
   */
  async handleTransferFailure(account, transferResult) {
    try {
      console.log(`❌ Transfer failed for account: ${account.id}`);

      const currentFailures = (account.metadata?.auto_transfer_failures || 0) + 1;
      const shouldRetry = currentFailures < this.maxRetryAttempts;

      if (shouldRetry) {
        // Schedule retry
        const retryDate = new Date();
        retryDate.setHours(retryDate.getHours() + this.retryDelayHours);

        await supabase
          .from('savings_accounts')
          .update({
            next_auto_transfer: retryDate.toISOString(),
            metadata: {
              ...account.metadata,
              auto_transfer_failures: currentFailures,
              last_failure_reason: transferResult.error,
              last_failure_date: new Date().toISOString()
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', account.id);

        // Send retry notification
        await this.sendTransferNotification(account, 'retry', {
          attempt: currentFailures,
          maxAttempts: this.maxRetryAttempts,
          retryDate,
          error: transferResult.error
        });
      } else {
        // Max retries reached, disable auto transfer
        await supabase
          .from('savings_accounts')
          .update({
            auto_transfer_enabled: false,
            metadata: {
              ...account.metadata,
              auto_transfer_failures: currentFailures,
              auto_transfer_disabled_reason: 'max_retries_reached',
              auto_transfer_disabled_date: new Date().toISOString(),
              last_failure_reason: transferResult.error
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', account.id);

        // Send failure notification
        await this.sendTransferNotification(account, 'disabled', {
          attempts: currentFailures,
          error: transferResult.error
        });
      }

      // Log transfer failure
      await this.logTransferEvent(account.id, 'failure', {
        error: transferResult.error,
        attempt: currentFailures,
        willRetry: shouldRetry
      });

    } catch (error) {
      console.error('❌ Error handling transfer failure:', error);
    }
  }

  /**
   * Handle transfer validation failure
   */
  async handleTransferValidationFailure(account, error) {
    try {
      console.log(`❌ Transfer validation failed for account: ${account.id}`);

      // Disable auto transfer due to validation failure
      await supabase
        .from('savings_accounts')
        .update({
          auto_transfer_enabled: false,
          metadata: {
            ...account.metadata,
            auto_transfer_disabled_reason: 'validation_failure',
            auto_transfer_disabled_date: new Date().toISOString(),
            validation_failure_reason: error
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);

      // Send validation failure notification
      await this.sendTransferNotification(account, 'validation_failed', {
        error
      });

      // Log validation failure
      await this.logTransferEvent(account.id, 'validation_failure', {
        error
      });

    } catch (updateError) {
      console.error('❌ Error handling validation failure:', updateError);
    }
  }

  /**
   * Handle transfer processing error
   */
  async handleTransferError(account, error) {
    try {
      console.error(`❌ Transfer error for account: ${account.id}`, error);

      // Disable auto transfer to prevent further issues
      await supabase
        .from('savings_accounts')
        .update({
          auto_transfer_enabled: false,
          metadata: {
            ...account.metadata,
            auto_transfer_disabled_reason: 'processing_error',
            auto_transfer_disabled_date: new Date().toISOString(),
            processing_error: error.message
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);

      // Send error notification
      await this.sendTransferNotification(account, 'error', {
        error: error.message
      });

      // Log processing error
      await this.logTransferEvent(account.id, 'error', {
        error: error.message
      });

    } catch (updateError) {
      console.error('❌ Error handling transfer error:', updateError);
    }
  }

  /**
   * Send transfer notification
   */
  async sendTransferNotification(account, type, data) {
    try {
      const notifications = {
        success: {
          title: 'Automatic Transfer Successful',
          content: `${formatCurrency(data.amount)} has been automatically transferred to your ${account.account_name} account. Next transfer: ${formatDateTime(data.nextTransferDate)}`,
          type: 'auto_transfer_success'
        },
        retry: {
          title: 'Transfer Retry Scheduled',
          content: `Automatic transfer failed (attempt ${data.attempt}/${data.maxAttempts}). Retrying on ${formatDateTime(data.retryDate)}`,
          type: 'auto_transfer_retry'
        },
        disabled: {
          title: 'Automatic Transfer Disabled',
          content: `Your automatic transfer to ${account.account_name} has been disabled after ${data.attempts} failed attempts. Please check your settings.`,
          type: 'auto_transfer_disabled'
        },
        validation_failed: {
          title: 'Transfer Validation Failed',
          content: `Your automatic transfer to ${account.account_name} has been disabled due to validation issues. Please review your settings.`,
          type: 'auto_transfer_validation_failed'
        },
        error: {
          title: 'Transfer Processing Error',
          content: `Your automatic transfer to ${account.account_name} has been disabled due to a processing error. Please contact support.`,
          type: 'auto_transfer_error'
        }
      };

      const notification = notifications[type];
      if (notification) {
        await enhancedNotificationService.sendNotification(account.user_id, {
          ...notification,
          data: {
            accountId: account.id,
            accountName: account.account_name,
            ...data
          }
        });
      }
    } catch (error) {
      console.error('❌ Error sending transfer notification:', error);
    }
  }

  /**
   * Log transfer event
   */
  async logTransferEvent(accountId, eventType, data) {
    try {
      await supabase
        .from('auto_transfer_logs')
        .insert({
          account_id: accountId,
          event_type: eventType,
          event_data: data,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging transfer event:', error);
    }
  }

  /**
   * Calculate next transfer date
   */
  calculateNextTransferDate(currentDate, frequency) {
    const date = new Date(currentDate);
    
    switch (frequency) {
      case 'daily':
        date.setDate(date.getDate() + 1);
        break;
      case 'weekly':
        date.setDate(date.getDate() + 7);
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + 1);
        break;
      default:
        date.setMonth(date.getMonth() + 1); // Default to monthly
    }
    
    return date;
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get processing status
   */
  getProcessingStatus() {
    return {
      queueSize: this.processingQueue.size,
      retryQueueSize: this.retryQueue.size,
      activeTransfers: Array.from(this.processingQueue.entries()).map(([accountId, data]) => ({
        accountId,
        transferId: data.transferId,
        startTime: data.startTime,
        status: data.status
      }))
    };
  }

  /**
   * Manually trigger transfer for account
   */
  async triggerTransfer(accountId, userId) {
    try {
      if (!accountId || !isValidUUID(accountId)) {
        return { success: false, error: 'Valid account ID is required' };
      }

      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Get account details
      const { data: account, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single();

      if (error || !account) {
        return { success: false, error: 'Account not found' };
      }

      if (!account.auto_transfer_enabled) {
        return { success: false, error: 'Auto transfer is not enabled for this account' };
      }

      // Process the transfer
      await this.processTransfer(account);

      return { success: true, message: 'Transfer triggered successfully' };
    } catch (error) {
      console.error('❌ Error triggering manual transfer:', error);
      return { success: false, error: 'Failed to trigger transfer' };
    }
  }

  /**
   * Stop the processor
   */
  stop() {
    console.log('🛑 Stopping automatic transfer processor');
    this.processingQueue.clear();
    this.retryQueue.clear();
  }
}

export default new AutomaticTransferService();
