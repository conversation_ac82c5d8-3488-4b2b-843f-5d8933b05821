/**
 * Enhanced Bill Payment Service
 * Comprehensive service for bill payment processing, biller integration,
 * payment validation, and transaction management
 */

import { supabase } from './supabaseClient';
import authService from './authService';
import fraudPreventionService from './fraudPreventionService';
import enhancedNotificationService from './enhancedNotificationService';
import digitalReceiptService from './digitalReceiptService';
import { formatCurrency } from '../utils/currencyUtils';
import { isProductionMode } from '../config/environment';
import productionServices from '../config/productionServices';

// Payment status constants
const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
};

// Biller categories
const BILLER_CATEGORIES = {
  UTILITIES: 'utilities',
  TELECOMMUNICATIONS: 'telecommunications',
  GOVERNMENT: 'government',
  INSURANCE: 'insurance',
  EDUCATION: 'education',
  ENTERTAINMENT: 'entertainment'
};

class BillPaymentService {
  constructor() {
    this.paymentStatus = PAYMENT_STATUS;
    this.billerCategories = BILLER_CATEGORIES;
    this.processingQueue = new Map();
    this.retryQueue = new Map();
    this.supportedProviders = {
      // Utilities - Enhanced with more details
      umeme: {
        name: 'UMEME',
        type: 'electricity',
        country: 'UG',
        apiEndpoint: '/api/bills/umeme',
        accountFormat: /^\d{8,12}$/,
        accountLabel: 'Meter Number',
        accountPlaceholder: 'Enter 8-12 digit meter number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-5 minutes',
        description: 'Pay your UMEME electricity bills instantly'
      },
      nwsc: {
        name: 'NWSC',
        type: 'water',
        country: 'UG',
        apiEndpoint: '/api/bills/nwsc',
        accountFormat: /^\d{6,10}$/,
        accountLabel: 'Account Number',
        accountPlaceholder: 'Enter 6-10 digit account number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-3 minutes',
        description: 'Pay your NWSC water bills quickly and securely'
      },
      kcca: {
        name: 'KCCA',
        type: 'municipal',
        country: 'UG',
        apiEndpoint: '/api/bills/kcca',
        accountFormat: /^[A-Z0-9]{6,12}$/,
        accountLabel: 'Property ID',
        accountPlaceholder: 'Enter property ID (e.g., KCC123456)',
        supportsBillInquiry: false,
        paymentMethods: ['wallet'],
        processingTime: '5-10 minutes',
        description: 'Pay KCCA municipal services and licenses'
      },
      
      // Mobile Networks - Uganda
      mtn: { name: 'MTN Uganda', type: 'mobile', country: 'UG', apiEndpoint: '/api/airtime/mtn' },
      airtel: { name: 'Airtel Uganda', type: 'mobile', country: 'UG', apiEndpoint: '/api/airtime/airtel' },
      utl: { name: 'UTL', type: 'mobile', country: 'UG', apiEndpoint: '/api/airtime/utl' },
      
      // TV & Internet - Enhanced
      dstv: {
        name: 'DStv',
        type: 'tv',
        country: 'UG',
        apiEndpoint: '/api/bills/dstv',
        accountFormat: /^\d{10}$/,
        accountLabel: 'Smartcard Number',
        accountPlaceholder: 'Enter 10-digit smartcard number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-2 minutes',
        description: 'Renew your DStv subscription packages'
      },
      gotv: {
        name: 'GOtv',
        type: 'tv',
        country: 'UG',
        apiEndpoint: '/api/bills/gotv',
        accountFormat: /^\d{10}$/,
        accountLabel: 'IUC Number',
        accountPlaceholder: 'Enter 10-digit IUC number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-2 minutes',
        description: 'Pay for GOtv subscription and enjoy your shows'
      },
      startimes: {
        name: 'StarTimes',
        type: 'tv',
        country: 'UG',
        apiEndpoint: '/api/bills/startimes',
        accountFormat: /^\d{11}$/,
        accountLabel: 'Smartcard Number',
        accountPlaceholder: 'Enter 11-digit smartcard number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-3 minutes',
        description: 'Recharge your StarTimes decoder'
      },
      smile: {
        name: 'Smile Telecom',
        type: 'internet',
        country: 'UG',
        apiEndpoint: '/api/bills/smile',
        accountFormat: /^\d{8,12}$/,
        accountLabel: 'Account Number',
        accountPlaceholder: 'Enter account number',
        supportsBillInquiry: true,
        paymentMethods: ['wallet', 'mobile_money'],
        processingTime: '1-5 minutes',
        description: 'Pay for Smile internet and data services'
      },
      
      // Government Services
      ura: { name: 'URA Tax', type: 'tax', country: 'UG', apiEndpoint: '/api/government/ura' },
      kcca_license: { name: 'KCCA License', type: 'license', country: 'UG', apiEndpoint: '/api/government/kcca' },
      passport: { name: 'Passport Fees', type: 'government', country: 'UG', apiEndpoint: '/api/government/passport' },
      
      // Education
      uneb: { name: 'UNEB', type: 'exam', country: 'UG', apiEndpoint: '/api/education/uneb' },
      makerere: { name: 'Makerere University', type: 'university', country: 'UG', apiEndpoint: '/api/education/makerere' },
      mubs: { name: 'MUBS', type: 'university', country: 'UG', apiEndpoint: '/api/education/mubs' },
      
      // Insurance
      aar: { name: 'AAR Insurance', type: 'health', country: 'UG', apiEndpoint: '/api/insurance/aar' },
      jubilee: { name: 'Jubilee Insurance', type: 'general', country: 'UG', apiEndpoint: '/api/insurance/jubilee' },
      uap: { name: 'UAP Insurance', type: 'motor', country: 'UG', apiEndpoint: '/api/insurance/uap' },
    };
  }

  /**
   * Validate account number for specific provider
   * @param {string} providerId - Provider identifier
   * @param {string} accountNumber - Account number to validate
   * @returns {Object} - Validation result
   */
  async validateAccount(providerId, accountNumber) {
    try {
      const provider = this.supportedProviders[providerId];
      if (!provider) {
        return { success: false, error: 'Provider not supported' };
      }

      // Basic validation rules
      const validationRules = {
        mobile: /^[0-9]{9,10}$/, // 9-10 digits for mobile numbers
        electricity: /^[0-9]{8,15}$/, // 8-15 digits for meter numbers
        water: /^[0-9]{6,12}$/, // 6-12 digits for water accounts
        tv: /^[0-9]{10,15}$/, // 10-15 digits for decoder numbers
        internet: /^[A-Za-z0-9]{6,20}$/, // Alphanumeric for internet accounts
        tax: /^[0-9]{10}$/, // 10 digits for TIN
        license: /^[A-Za-z0-9]{8,15}$/, // Alphanumeric for licenses
        exam: /^[0-9]{8,12}$/, // 8-12 digits for exam registration
        university: /^[A-Za-z0-9]{6,15}$/, // Alphanumeric for student numbers
        insurance: /^[A-Za-z0-9]{8,20}$/, // Alphanumeric for policy numbers
      };

      const rule = validationRules[provider.type];
      if (!rule || !rule.test(accountNumber)) {
        return { 
          success: false, 
          error: `Invalid ${provider.type} account number format` 
        };
      }

      if (isProductionMode()) {
        // PRODUCTION MODE: Call actual provider APIs
        try {
          const credentials = productionServices.getCredentials(providerId);
          if (!credentials) {
            throw new Error(`No production credentials configured for ${provider.name}`);
          }

          const response = await fetch(provider.apiEndpoint + '/validate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${credentials.apiKey}`,
              'X-Client-ID': credentials.clientId || '',
            },
            body: JSON.stringify({
              accountNumber,
              providerId,
              country: provider.country
            })
          });

          if (!response.ok) {
            throw new Error(`Provider API error: ${response.status}`);
          }

          const data = await response.json();
          return {
            success: true,
            data: {
              accountNumber,
              accountName: data.accountName || 'Account Holder',
              provider: provider.name,
              type: provider.type,
              validated: true,
              balance: data.balance || null,
              dueAmount: data.dueAmount || null,
            }
          };
        } catch (error) {
          console.error(`Production validation error for ${provider.name}:`, error);
          return {
            success: false,
            error: `Unable to validate account with ${provider.name}. Please try again.`
          };
        }
      } else {
        // DEVELOPMENT MODE: Simulate validation
        console.log(`🔧 Development Mode: Validating ${provider.name} account ${accountNumber}`);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock validation response
        return {
          success: true,
          data: {
            accountNumber,
            accountName: `Mock Account Holder`,
            provider: provider.name,
            type: provider.type,
            validated: true,
          }
        };
      }
    } catch (error) {
      console.error('Account validation error:', error);
      return { success: false, error: 'Validation failed' };
    }
  }

  /**
   * Inquire about outstanding bills
   * @param {string} providerId - Provider ID
   * @param {string} accountNumber - Account number
   * @returns {Object} - Bill inquiry result
   */
  async inquireBill(providerId, accountNumber) {
    try {
      const provider = this.supportedProviders[providerId];
      if (!provider) {
        return { success: false, error: 'Provider not supported' };
      }

      if (!provider.supportsBillInquiry) {
        return {
          success: false,
          error: 'Bill inquiry not supported for this provider. You can proceed with payment.'
        };
      }

      // Validate account number first
      const validation = await this.validateAccount(providerId, accountNumber);
      if (!validation.success) {
        return { success: false, error: validation.error };
      }

      // Check if we should use mock data (only in development with explicit flag)
      const useMockData = (process.env.NODE_ENV === 'development' || __DEV__) &&
                          process.env.ENABLE_MOCK_BILLS === 'true';

      if (useMockData) {
        console.log('🔧 Development Mode: Using mock bill inquiry (ENABLE_MOCK_BILLS=true)');

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        const mockBills = {
          umeme: {
            customerName: 'John Doe',
            accountNumber: accountNumber,
            outstandingAmount: Math.floor(Math.random() * 200000) + 50000,
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            lastPayment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            units: Math.floor(Math.random() * 100) + 50,
            tariff: 'Domestic',
            meterType: 'Prepaid',
          },
          nwsc: {
            customerName: 'Jane Smith',
            accountNumber: accountNumber,
            outstandingAmount: Math.floor(Math.random() * 150000) + 30000,
            dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
            lastPayment: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
            consumption: Math.floor(Math.random() * 50) + 20,
            category: 'Residential',
            connectionType: 'Metered',
          },
          dstv: {
            customerName: 'Mike Johnson',
            accountNumber: accountNumber,
            outstandingAmount: Math.floor(Math.random() * 100000) + 25000,
            dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            package: 'Compact Plus',
            status: 'Active',
            nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          },
          gotv: {
            customerName: 'Sarah Wilson',
            accountNumber: accountNumber,
            outstandingAmount: Math.floor(Math.random() * 50000) + 15000,
            dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            package: 'GOtv Plus',
            status: 'Active',
          },
          smile: {
            customerName: 'David Brown',
            accountNumber: accountNumber,
            outstandingAmount: Math.floor(Math.random() * 80000) + 20000,
            dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
            package: 'SmileVoice 4G',
            dataBalance: Math.floor(Math.random() * 10) + 5,
          }
        };

        const billData = mockBills[providerId] || {
          customerName: 'Customer',
          accountNumber: accountNumber,
          outstandingAmount: Math.floor(Math.random() * 100000) + 20000,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        };

        return {
          success: true,
          data: {
            provider: provider.name,
            ...billData,
            currency: 'UGX',
            inquiryTime: new Date().toISOString(),
            minimumPayment: Math.floor(billData.outstandingAmount * 0.1),
          }
        };
      }

      // Production implementation would call actual provider APIs
      return { success: false, error: 'Bill inquiry service temporarily unavailable' };
    } catch (error) {
      console.error('Bill inquiry error:', error);
      return { success: false, error: 'Failed to inquire bill details' };
    }
  }

  /**
   * Get available data bundles for mobile providers
   * @param {string} providerId - Mobile provider ID
   * @returns {Object} - Available bundles
   */
  async getDataBundles(providerId) {
    try {
      const provider = this.supportedProviders[providerId];
      if (!provider || provider.type !== 'mobile') {
        return { success: false, error: 'Invalid mobile provider' };
      }

      // Mock data bundles for development
      const mockBundles = {
        mtn: [
          { id: 'daily_100mb', name: 'Daily 100MB', price: 1000, validity: '24 hours' },
          { id: 'weekly_1gb', name: 'Weekly 1GB', price: 5000, validity: '7 days' },
          { id: 'monthly_5gb', name: 'Monthly 5GB', price: 20000, validity: '30 days' },
          { id: 'monthly_10gb', name: 'Monthly 10GB', price: 35000, validity: '30 days' },
        ],
        airtel: [
          { id: 'daily_150mb', name: 'Daily 150MB', price: 1000, validity: '24 hours' },
          { id: 'weekly_1_5gb', name: 'Weekly 1.5GB', price: 5000, validity: '7 days' },
          { id: 'monthly_6gb', name: 'Monthly 6GB', price: 20000, validity: '30 days' },
          { id: 'monthly_12gb', name: 'Monthly 12GB', price: 35000, validity: '30 days' },
        ],
        utl: [
          { id: 'daily_100mb', name: 'Daily 100MB', price: 800, validity: '24 hours' },
          { id: 'weekly_1gb', name: 'Weekly 1GB', price: 4000, validity: '7 days' },
          { id: 'monthly_5gb', name: 'Monthly 5GB', price: 18000, validity: '30 days' },
        ],
      };

      return {
        success: true,
        data: mockBundles[providerId] || []
      };
    } catch (error) {
      console.error('Error fetching data bundles:', error);
      return { success: false, error: 'Failed to fetch bundles' };
    }
  }

  /**
   * Enhanced bill payment processing with comprehensive validation and tracking
   */
  async processBillPayment(userId, paymentData) {
    try {
      console.log('💳 Processing enhanced bill payment:', {
        userId,
        biller: paymentData.billerId || paymentData.providerId,
        amount: paymentData.amount
      });

      // Validate payment data
      const validation = await this.validatePaymentData(paymentData);
      if (!validation.isValid) {
        throw new Error(`Payment validation failed: ${validation.errors.join(', ')}`);
      }

      // Get biller information from new database structure
      const biller = await this.getBillerById(paymentData.billerId || paymentData.providerId);
      if (!biller) {
        // Fallback to legacy provider structure
        const provider = this.supportedProviders[paymentData.providerId];
        if (!provider) {
          throw new Error('Biller not found or not available');
        }
        return this.processLegacyPayment(userId, paymentData, provider);
      }

      // Verify account if required
      let accountVerification = { verified: true, accountName: paymentData.accountName };
      if (biller.validation_rules?.require_verification) {
        accountVerification = await this.verifyBillAccount(
          biller,
          paymentData.accountNumber
        );
      }

      // Calculate fees using new fee structure
      const feeCalculation = this.calculatePaymentFee(biller, paymentData.amount);

      // Create payment record in new bill_payments table
      const payment = await this.createPaymentRecord(
        userId,
        biller,
        paymentData,
        feeCalculation,
        accountVerification
      );

      // Fraud detection check
      const fraudCheck = await this.performFraudCheck(userId, payment);
      if (!fraudCheck.allowed) {
        await this.updatePaymentStatus(payment.id, this.paymentStatus.FAILED, {
          error_code: 'FRAUD_DETECTED',
          error_message: fraudCheck.reason
        });
        throw new Error('Payment blocked by fraud detection');
      }

      // Process payment based on biller type
      const processingResult = await this.executePayment(payment, biller);

      // Update payment status
      await this.updatePaymentStatus(
        payment.id,
        processingResult.success ? this.paymentStatus.COMPLETED : this.paymentStatus.FAILED,
        processingResult
      );

      // Send notifications and generate receipt
      if (processingResult.success) {
        await this.handleSuccessfulPayment(userId, payment, biller);
      } else {
        await this.handleFailedPayment(userId, payment, processingResult.error);
      }

      console.log('✅ Enhanced bill payment processed:', {
        paymentId: payment.id,
        status: processingResult.success ? 'completed' : 'failed'
      });

      return {
        success: processingResult.success,
        payment: {
          id: payment.id,
          reference: payment.reference,
          status: processingResult.success ? this.paymentStatus.COMPLETED : this.paymentStatus.FAILED,
          amount: payment.amount,
          fee: payment.fee,
          totalAmount: payment.total_amount,
          biller: biller.display_name,
          accountNumber: payment.account_number,
          accountName: accountVerification.accountName
        },
        error: processingResult.error
      };
    } catch (error) {
      console.error('❌ Error processing enhanced bill payment:', error);
      throw error;
    }
  }

  /**
   * Legacy payment processing for backward compatibility
   */
  async processLegacyPayment(userId, paymentData, provider) {
    try {
      const {
        providerId,
        accountNumber,
        amount,
        category,
        bundleId = null, // For data bundles
      } = paymentData;

      const provider = this.supportedProviders[providerId];
      if (!provider) {
        return { success: false, error: 'Provider not supported' };
      }

      // Validate amount
      if (!amount || amount <= 0) {
        return { success: false, error: 'Invalid amount' };
      }

      // Generate transaction reference
      const reference = `BP${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

      if (isProductionMode()) {
        // PRODUCTION MODE: Process real payment
        try {
          const credentials = productionServices.getCredentials(providerId);
          if (!credentials) {
            throw new Error(`No production credentials configured for ${provider.name}`);
          }

          // Call production payment API
          const paymentResponse = await fetch(provider.apiEndpoint + '/pay', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${credentials.apiKey}`,
              'X-Client-ID': credentials.clientId || '',
              'X-Transaction-ID': reference,
            },
            body: JSON.stringify({
              accountNumber,
              amount,
              currency: 'UGX',
              reference,
              bundleId,
              metadata: {
                userId: user.id,
                category,
                timestamp: new Date().toISOString(),
              }
            })
          });

          if (!paymentResponse.ok) {
            throw new Error(`Payment API error: ${paymentResponse.status}`);
          }

          const paymentResult = await paymentResponse.json();

          // Create transaction record
          const transaction = {
            id: reference,
            user_id: user.id,
            type: 'bill_payment',
            amount: amount,
            currency: 'UGX',
            description: `${provider.name} - ${accountNumber}`,
            provider: provider.name,
            provider_id: providerId,
            account_number: accountNumber,
            category: category,
            bundle_id: bundleId,
            status: paymentResult.status || 'completed',
            reference: reference,
            external_reference: paymentResult.providerReference,
            created_at: new Date().toISOString(),
            metadata: {
              provider_type: provider.type,
              country: provider.country,
              processed_at: new Date().toISOString(),
              provider_response: paymentResult,
            }
          };

          // Save transaction to database
          const { data, error } = await supabase
            .from('transactions')
            .insert([transaction])
            .select()
            .single();

          if (error) {
            console.error('Database error:', error);
            return { success: false, error: 'Payment processed but failed to save record' };
          }

          return {
            success: true,
            data: {
              transaction: data,
              reference: reference,
              provider: provider.name,
              amount: amount,
              status: paymentResult.status || 'completed',
              providerReference: paymentResult.providerReference,
            }
          };
        } catch (error) {
          console.error(`Production payment error for ${provider.name}:`, error);

          // Log failed transaction attempt
          const failedTransaction = {
            id: reference,
            user_id: user.id,
            type: 'bill_payment',
            amount: amount,
            currency: 'UGX',
            description: `${provider.name} - ${accountNumber}`,
            provider: provider.name,
            provider_id: providerId,
            account_number: accountNumber,
            category: category,
            status: 'failed',
            reference: reference,
            created_at: new Date().toISOString(),
            metadata: {
              error: error.message,
              provider_type: provider.type,
              country: provider.country,
            }
          };

          await supabase.from('transactions').insert([failedTransaction]);

          return {
            success: false,
            error: `Payment to ${provider.name} failed. Please try again or contact support.`
          };
        }
      } else {
        // DEVELOPMENT MODE: Simulate payment processing
        console.log(`🔧 Development Mode: Processing ${provider.name} payment`);
        console.log(`💰 Amount: UGX ${amount.toLocaleString()}`);
        console.log(`📱 Account: ${accountNumber}`);
        console.log(`🔗 Reference: ${reference}`);

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock successful payment
        const transaction = {
          id: reference,
          user_id: user.id,
          type: 'bill_payment',
          amount: amount,
          currency: 'UGX',
          description: `${provider.name} - ${accountNumber}`,
          provider: provider.name,
          provider_id: providerId,
          account_number: accountNumber,
          category: category,
          bundle_id: bundleId,
          status: 'completed',
          reference: reference,
          created_at: new Date().toISOString(),
          metadata: {
            provider_type: provider.type,
            country: provider.country,
            processed_at: new Date().toISOString(),
          }
        };

        // Save transaction to database
        const { data, error } = await supabase
          .from('transactions')
          .insert([transaction])
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          return { success: false, error: 'Failed to save transaction' };
        }

        return {
          success: true,
          data: {
            transaction: data,
            reference: reference,
            provider: provider.name,
            amount: amount,
            status: 'completed',
          }
        };
      }
    } catch (error) {
      console.error('Bill payment error:', error);
      return { success: false, error: 'Payment processing failed' };
    }
  }

  /**
   * Get payment history for user
   * @param {number} limit - Number of transactions to fetch
   * @returns {Object} - Payment history
   */
  async getPaymentHistory(limit = 20) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('type', 'bill_payment')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Database error:', error);
        return { success: false, error: 'Failed to fetch payment history' };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error fetching payment history:', error);
      return { success: false, error: 'Failed to fetch payment history' };
    }
  }

  /**
   * Get supported providers by category
   * @param {string} category - Bill category
   * @returns {Array} - List of providers
   */
  getSupportedProviders(category) {
    const categoryMap = {
      utilities: ['umeme', 'nwsc', 'kcca'],
      airtime: ['mtn', 'airtel', 'utl'],
      tv_internet: ['dstv', 'gotv', 'startimes', 'smile'],
      government: ['ura', 'kcca_license', 'passport'],
      education: ['uneb', 'makerere', 'mubs'],
      insurance: ['aar', 'jubilee', 'uap'],
    };

    const providerIds = categoryMap[category] || [];
    return providerIds.map(id => ({
      id,
      ...this.supportedProviders[id]
    }));
  }

  /**
   * Check if provider supports real-time validation
   * @param {string} providerId - Provider ID
   * @returns {boolean} - Whether real-time validation is supported
   */
  supportsRealTimeValidation(providerId) {
    // In production, this would check actual provider capabilities
    const realTimeProviders = ['mtn', 'airtel', 'umeme', 'dstv'];
    return realTimeProviders.includes(providerId);
  }

  /**
   * Get provider-specific payment limits
   * @param {string} providerId - Provider ID
   * @returns {Object} - Payment limits
   */
  getPaymentLimits(providerId) {
    const limits = {
      // Mobile providers
      mtn: { min: 500, max: 500000, daily: 1000000 },
      airtel: { min: 500, max: 500000, daily: 1000000 },
      utl: { min: 500, max: 300000, daily: 500000 },
      
      // Utilities
      umeme: { min: 1000, max: 2000000, daily: 5000000 },
      nwsc: { min: 1000, max: 1000000, daily: 2000000 },
      
      // TV & Internet
      dstv: { min: 5000, max: 500000, daily: 1000000 },
      gotv: { min: 2000, max: 200000, daily: 500000 },
      
      // Default limits
      default: { min: 1000, max: 1000000, daily: 2000000 },
    };

    return limits[providerId] || limits.default;
  }

  /**
   * Enhanced validation for new bill payment system
   */
  async validatePaymentData(paymentData) {
    const errors = [];

    // Required fields
    const billerId = paymentData.billerId || paymentData.providerId;
    if (!billerId) errors.push('Biller ID is required');
    if (!paymentData.accountNumber) errors.push('Account number is required');
    if (!paymentData.amount || paymentData.amount <= 0) errors.push('Valid amount is required');

    // Get biller for validation
    if (billerId) {
      try {
        const biller = await this.getBillerById(billerId);
        if (biller) {
          // Amount validation
          if (paymentData.amount < biller.min_amount) {
            errors.push(`Minimum amount is ${formatCurrency(biller.min_amount)}`);
          }
          if (paymentData.amount > biller.max_amount) {
            errors.push(`Maximum amount is ${formatCurrency(biller.max_amount)}`);
          }

          // Account number validation
          if (biller.account_number_format) {
            const regex = new RegExp(biller.account_number_format);
            if (!regex.test(paymentData.accountNumber)) {
              errors.push('Invalid account number format');
            }
          }

          if (biller.account_number_length) {
            if (paymentData.accountNumber.length !== biller.account_number_length) {
              errors.push(`Account number must be ${biller.account_number_length} digits`);
            }
          }

          if (biller.account_number_prefix) {
            if (!paymentData.accountNumber.startsWith(biller.account_number_prefix)) {
              errors.push(`Account number must start with ${biller.account_number_prefix}`);
            }
          }
        }
      } catch (error) {
        console.error('Error validating biller:', error);
        // Continue with legacy validation
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get biller by ID from new database structure
   */
  async getBillerById(billerId) {
    try {
      const { data: biller, error } = await supabase
        .from('billers')
        .select(`
          *,
          category:bill_categories(*)
        `)
        .eq('id', billerId)
        .eq('is_active', true)
        .eq('is_available', true)
        .single();

      if (error) {
        console.log('Biller not found in new structure, using legacy');
        return null;
      }
      return biller;
    } catch (error) {
      console.error('❌ Error getting biller:', error);
      return null;
    }
  }

  /**
   * Verify bill account with biller API
   */
  async verifyBillAccount(biller, accountNumber) {
    try {
      console.log('🔍 Verifying bill account:', { biller: biller.code, accountNumber });

      // Mock verification for demo - in production, integrate with biller APIs
      const mockVerification = {
        verified: true,
        accountName: `Account Holder ${accountNumber.slice(-4)}`,
        accountStatus: 'active',
        outstandingBalance: Math.floor(Math.random() * 100000) + 10000
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return mockVerification;
    } catch (error) {
      console.error('❌ Error verifying account:', error);
      return {
        verified: false,
        error: 'Account verification failed'
      };
    }
  }

  /**
   * Calculate payment fee based on biller fee structure
   */
  calculatePaymentFee(biller, amount) {
    let fee = 0;

    switch (biller.fee_type) {
      case 'fixed':
        fee = biller.fee_amount || 0;
        break;

      case 'percentage':
        fee = (amount * (biller.fee_percentage || 0)) / 100;
        break;

      case 'tiered':
        // Implement tiered fee structure
        const tiers = biller.fee_structure?.tiers || [];
        for (const tier of tiers) {
          if (amount >= tier.min_amount && amount <= tier.max_amount) {
            fee = tier.fee;
            break;
          }
        }
        break;

      default:
        fee = 0;
    }

    return {
      fee: Math.round(fee * 100) / 100, // Round to 2 decimal places
      totalAmount: amount + fee,
      feeType: biller.fee_type
    };
  }

  /**
   * Create payment record in new bill_payments table
   */
  async createPaymentRecord(userId, biller, paymentData, feeCalculation, accountVerification) {
    try {
      const reference = this.generatePaymentReference();

      const paymentRecord = {
        user_id: userId,
        biller_id: biller.id,
        account_number: paymentData.accountNumber,
        account_name: accountVerification.accountName,
        amount: paymentData.amount,
        fee: feeCalculation.fee,
        total_amount: feeCalculation.totalAmount,
        currency: paymentData.currency || 'UGX',
        reference,
        payment_method: paymentData.paymentMethod || 'wallet',
        status: this.paymentStatus.PENDING,
        account_verified: accountVerification.verified,
        verification_data: accountVerification,
        metadata: {
          biller_name: biller.display_name,
          category: biller.category?.name,
          user_agent: paymentData.userAgent,
          ip_address: paymentData.ipAddress
        },
        created_at: new Date().toISOString()
      };

      const { data: payment, error } = await supabase
        .from('bill_payments')
        .insert(paymentRecord)
        .select()
        .single();

      if (error) throw error;

      return payment;
    } catch (error) {
      console.error('❌ Error creating payment record:', error);
      throw error;
    }
  }

  /**
   * Generate payment reference
   */
  generatePaymentReference() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `BP${timestamp.slice(-8)}${random}`;
  }
}

// Create and export singleton instance
const billPaymentService = new BillPaymentService();
export default billPaymentService;
