/**
 * Biller Management Service
 * Manages supported billers, payment categories, fee structures,
 * validation rules, and API integrations with error handling
 */

import { supabase } from './supabaseClient';
import { isValidUUID } from '../utils/userUtils';

// Biller status constants
const BILLER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  MAINTENANCE: 'maintenance',
  DEPRECATED: 'deprecated'
};

// Fee types
const FEE_TYPES = {
  FIXED: 'fixed',
  PERCENTAGE: 'percentage',
  TIERED: 'tiered'
};

class BillerManagementService {
  constructor() {
    this.billerStatus = BILLER_STATUS;
    this.feeTypes = FEE_TYPES;
    this.billerCache = new Map();
    this.categoryCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get all bill categories
   */
  async getBillCategories() {
    try {
      // Check cache first
      const cacheKey = 'bill_categories';
      const cached = this.categoryCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return { success: true, categories: cached.data };
      }

      console.log('📋 Fetching bill categories');

      const { data: categories, error } = await supabase
        .from('bill_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('❌ Database error fetching categories:', error);
        // Return empty categories if table doesn't exist
        return {
          success: true,
          categories: []
        };
      }

      const formattedCategories = (categories || []).map(category => ({
        id: category.id,
        name: category.name,
        displayName: category.display_name,
        description: category.description,
        icon: category.icon,
        color: category.color,
        sortOrder: category.sort_order
      }));

      // Cache the result
      this.categoryCache.set(cacheKey, {
        data: formattedCategories,
        timestamp: Date.now()
      });

      console.log(`✅ Found ${formattedCategories.length} bill categories`);
      return {
        success: true,
        categories: formattedCategories
      };
    } catch (error) {
      console.error('❌ Error fetching bill categories:', error);
      // Return empty categories instead of throwing
      return {
        success: true,
        categories: []
      };
    }
  }



  /**
   * Get billers by category
   */
  async getBillersByCategory(categoryId, options = {}) {
    try {
      const { includeInactive = false, includeMaintenanceMode = false } = options;

      console.log('🏢 Fetching billers for category:', categoryId);

      let query = supabase
        .from('billers')
        .select(`
          *,
          category:bill_categories(*)
        `)
        .eq('category_id', categoryId)
        .order('name', { ascending: true });

      // Apply status filters
      if (!includeInactive) {
        query = query.eq('is_active', true);
      }
      if (!includeMaintenanceMode) {
        query = query.eq('maintenance_mode', false);
      }

      const { data: billers, error } = await query;

      if (error) throw error;

      return {
        success: true,
        billers: billers.map(biller => this.formatBillerResponse(biller))
      };
    } catch (error) {
      console.error('❌ Error fetching billers by category:', error);
      throw error;
    }
  }

  /**
   * Get all active billers
   */
  async getActiveBillers() {
    try {
      // Check cache first
      const cacheKey = 'active_billers';
      const cached = this.billerCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return { success: true, billers: cached.data };
      }

      console.log('🏢 Fetching all active billers');

      const { data: billers, error } = await supabase
        .from('billers')
        .select(`
          *,
          category:bill_categories(*)
        `)
        .eq('is_active', true)
        .eq('is_available', true)
        .eq('maintenance_mode', false)
        .order('name', { ascending: true });

      if (error) throw error;

      const formattedBillers = billers.map(biller => this.formatBillerResponse(biller));

      // Cache the result
      this.billerCache.set(cacheKey, {
        data: formattedBillers,
        timestamp: Date.now()
      });

      return {
        success: true,
        billers: formattedBillers
      };
    } catch (error) {
      console.error('❌ Error fetching active billers:', error);
      throw error;
    }
  }

  /**
   * Get biller by ID
   */
  async getBillerById(billerId) {
    try {
      console.log('🔍 Fetching biller by ID:', billerId);

      const { data: biller, error } = await supabase
        .from('billers')
        .select(`
          *,
          category:bill_categories(*),
          outages:biller_outages(*)
        `)
        .eq('id', billerId)
        .single();

      if (error) throw error;

      return {
        success: true,
        biller: this.formatBillerResponse(biller, true)
      };
    } catch (error) {
      console.error('❌ Error fetching biller by ID:', error);
      throw error;
    }
  }

  /**
   * Search billers
   */
  async searchBillers(searchQuery, options = {}) {
    try {
      const { categoryId = null, limit = 20 } = options;

      console.log('🔍 Searching billers:', { searchQuery, categoryId });

      let query = supabase
        .from('billers')
        .select(`
          *,
          category:bill_categories(*)
        `)
        .eq('is_active', true)
        .eq('is_available', true)
        .limit(limit);

      // Add search conditions
      if (searchQuery) {
        query = query.or(`name.ilike.%${searchQuery}%,display_name.ilike.%${searchQuery}%,code.ilike.%${searchQuery}%`);
      }

      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      const { data: billers, error } = await query;

      if (error) throw error;

      return {
        success: true,
        billers: billers.map(biller => this.formatBillerResponse(biller)),
        query: searchQuery
      };
    } catch (error) {
      console.error('❌ Error searching billers:', error);
      throw error;
    }
  }

  /**
   * Get biller fee structure
   */
  async getBillerFeeStructure(billerId) {
    try {
      const { data: biller, error } = await supabase
        .from('billers')
        .select('fee_type, fee_amount, fee_percentage, fee_structure, min_amount, max_amount')
        .eq('id', billerId)
        .single();

      if (error) throw error;

      return {
        success: true,
        feeStructure: {
          type: biller.fee_type,
          fixedAmount: biller.fee_amount,
          percentage: biller.fee_percentage,
          structure: biller.fee_structure,
          minAmount: biller.min_amount,
          maxAmount: biller.max_amount
        }
      };
    } catch (error) {
      console.error('❌ Error fetching biller fee structure:', error);
      throw error;
    }
  }

  /**
   * Check biller availability
   */
  async checkBillerAvailability(billerId) {
    try {
      const { data: biller, error } = await supabase
        .from('billers')
        .select('is_active, is_available, maintenance_mode, maintenance_message')
        .eq('id', billerId)
        .single();

      if (error) throw error;

      // Check for active outages
      const { data: outages, error: outageError } = await supabase
        .from('biller_outages')
        .select('*')
        .eq('biller_id', billerId)
        .eq('status', 'active')
        .order('start_time', { ascending: false })
        .limit(1);

      if (outageError) {
        console.error('Error checking outages:', outageError);
      }

      const isAvailable = biller.is_active && 
                         biller.is_available && 
                         !biller.maintenance_mode &&
                         (!outages || outages.length === 0);

      return {
        success: true,
        availability: {
          isAvailable,
          isActive: biller.is_active,
          isOnline: biller.is_available,
          maintenanceMode: biller.maintenance_mode,
          maintenanceMessage: biller.maintenance_message,
          currentOutage: outages && outages.length > 0 ? outages[0] : null
        }
      };
    } catch (error) {
      console.error('❌ Error checking biller availability:', error);
      throw error;
    }
  }

  /**
   * Get biller validation rules
   */
  async getBillerValidationRules(billerId) {
    try {
      const { data: biller, error } = await supabase
        .from('billers')
        .select('account_number_format, account_number_length, account_number_prefix, validation_rules')
        .eq('id', billerId)
        .single();

      if (error) throw error;

      return {
        success: true,
        validationRules: {
          accountNumberFormat: biller.account_number_format,
          accountNumberLength: biller.account_number_length,
          accountNumberPrefix: biller.account_number_prefix,
          customRules: biller.validation_rules || {}
        }
      };
    } catch (error) {
      console.error('❌ Error fetching validation rules:', error);
      throw error;
    }
  }

  /**
   * Get biller outages
   */
  async getBillerOutages(billerId, options = {}) {
    try {
      const { includeResolved = false, limit = 10 } = options;

      let query = supabase
        .from('biller_outages')
        .select('*')
        .eq('biller_id', billerId)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (!includeResolved) {
        query = query.in('status', ['scheduled', 'active']);
      }

      const { data: outages, error } = await query;

      if (error) throw error;

      return {
        success: true,
        outages: outages.map(outage => ({
          id: outage.id,
          title: outage.title,
          description: outage.description,
          severity: outage.severity,
          status: outage.status,
          startTime: outage.start_time,
          endTime: outage.end_time,
          estimatedResolution: outage.estimated_resolution,
          publicMessage: outage.public_message
        }))
      };
    } catch (error) {
      console.error('❌ Error fetching biller outages:', error);
      throw error;
    }
  }

  /**
   * Format biller response
   */
  formatBillerResponse(biller, includeDetails = false) {
    const formatted = {
      id: biller.id,
      code: biller.code,
      name: biller.name,
      displayName: biller.display_name,
      description: biller.description,
      logoUrl: biller.logo_url,
      category: {
        id: biller.category.id,
        name: biller.category.name,
        displayName: biller.category.display_name,
        icon: biller.category.icon,
        color: biller.category.color
      },
      minAmount: biller.min_amount,
      maxAmount: biller.max_amount,
      supportedCurrencies: biller.supported_currencies,
      processingTime: biller.processing_time,
      isActive: biller.is_active,
      isAvailable: biller.is_available,
      maintenanceMode: biller.maintenance_mode,
      maintenanceMessage: biller.maintenance_message
    };

    if (includeDetails) {
      formatted.feeStructure = {
        type: biller.fee_type,
        amount: biller.fee_amount,
        percentage: biller.fee_percentage,
        structure: biller.fee_structure
      };
      formatted.validationRules = {
        accountNumberFormat: biller.account_number_format,
        accountNumberLength: biller.account_number_length,
        accountNumberPrefix: biller.account_number_prefix,
        customRules: biller.validation_rules
      };
      formatted.apiConfig = {
        endpoint: biller.api_endpoint,
        requiresApiKey: biller.api_key_required,
        config: biller.api_config
      };
      formatted.businessHours = biller.business_hours;
      formatted.outages = biller.outages || [];
    }

    return formatted;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.billerCache.clear();
    this.categoryCache.clear();
    console.log('🧹 Biller management cache cleared');
  }

  /**
   * Get popular billers (most used)
   */
  async getPopularBillers(limit = 10) {
    try {
      // Try to get billers with most payments in last 30 days
      const { data: popularBillers, error } = await supabase
        .rpc('get_popular_billers', {
          p_limit: limit,
          p_days: 30
        });

      if (error) {
        // Fallback to all active billers if function doesn't exist
        console.log('Popular billers function not available, using fallback');
        const result = await this.getActiveBillers();
        return {
          success: true,
          billers: result.success ? result.billers.slice(0, limit) : []
        };
      }

      return {
        success: true,
        billers: popularBillers || []
      };
    } catch (error) {
      console.error('❌ Error fetching popular billers:', error);
      // Return fallback instead of throwing
      try {
        const result = await this.getActiveBillers();
        return {
          success: true,
          billers: result.success ? result.billers.slice(0, limit) : []
        };
      } catch (fallbackError) {
        console.error('❌ Fallback also failed:', fallbackError);
        return {
          success: true,
          billers: []
        };
      }
    }
  }

  /**
   * Get recently used billers for user
   */
  async getRecentBillers(userId, limit = 5) {
    try {
      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        console.log('❌ Invalid or missing user ID for recent billers:', userId);
        return {
          success: true,
          billers: []
        };
      }

      console.log('📋 Fetching recent billers for user:', userId);

      const { data: recentPayments, error } = await supabase
        .from('bill_payments')
        .select(`
          biller_id,
          billers(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(limit * 2); // Get more to account for duplicates

      if (error) {
        console.error('❌ Database error fetching recent billers:', error);
        // If table doesn't exist or other DB error, return empty array
        return {
          success: true,
          billers: []
        };
      }

      // Remove duplicates and format
      const uniqueBillers = [];
      const seenBillerIds = new Set();

      for (const payment of recentPayments || []) {
        if (!seenBillerIds.has(payment.biller_id) && uniqueBillers.length < limit) {
          seenBillerIds.add(payment.biller_id);
          if (payment.billers) {
            uniqueBillers.push(this.formatBillerResponse(payment.billers));
          }
        }
      }

      console.log(`✅ Found ${uniqueBillers.length} recent billers`);
      return {
        success: true,
        billers: uniqueBillers
      };
    } catch (error) {
      console.error('❌ Error fetching recent billers:', error);
      // Return empty array instead of throwing error
      return {
        success: true,
        billers: []
      };
    }
  }
}

export default new BillerManagementService();
