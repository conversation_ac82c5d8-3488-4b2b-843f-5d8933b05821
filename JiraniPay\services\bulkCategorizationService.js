/**
 * Bulk Categorization Service
 * Handles bulk operations for transaction categorization
 * Provides review, edit, and bulk update functionality
 */

import supabase from './supabaseClient';
import transactionCategorizationService from './transactionCategorizationService';
import categoryManagementService from './categoryManagementService';

class BulkCategorizationService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.processingQueue = new Map();
  }

  /**
   * Get uncategorized transactions for review
   */
  async getUncategorizedTransactions(userId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        dateFrom,
        dateTo,
        amountMin,
        amountMax,
        searchTerm
      } = options;

      console.log('📋 Getting uncategorized transactions for user:', userId);

      let query = supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .or('category.is.null,category.eq.other')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }
      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }
      if (amountMin !== undefined) {
        query = query.gte('amount', amountMin);
      }
      if (amountMax !== undefined) {
        query = query.lte('amount', amountMax);
      }
      if (searchTerm) {
        query = query.or(
          `description.ilike.%${searchTerm}%,merchant_name.ilike.%${searchTerm}%,reference.ilike.%${searchTerm}%`
        );
      }

      const { data: transactions, error } = await query;

      if (error) throw error;

      // Get categorization suggestions for each transaction
      const transactionsWithSuggestions = await Promise.all(
        (transactions || []).map(async (transaction) => {
          try {
            const suggestionResult = await transactionCategorizationService.categorizeTransaction(
              transaction,
              userId
            );

            return {
              ...transaction,
              suggestions: suggestionResult.success ? {
                primary: suggestionResult.data.category,
                confidence: suggestionResult.data.confidence,
                alternatives: suggestionResult.data.suggestions
              } : null
            };
          } catch (error) {
            console.warn('⚠️ Failed to get suggestions for transaction:', transaction.id);
            return {
              ...transaction,
              suggestions: null
            };
          }
        })
      );

      console.log('✅ Found', transactionsWithSuggestions.length, 'uncategorized transactions');
      return {
        success: true,
        data: {
          transactions: transactionsWithSuggestions,
          hasMore: transactionsWithSuggestions.length === limit
        }
      };
    } catch (error) {
      console.error('❌ Error getting uncategorized transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get transactions by category for review
   */
  async getTransactionsByCategory(userId, categoryId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        dateFrom,
        dateTo,
        confidenceThreshold
      } = options;

      console.log('📋 Getting transactions for category:', categoryId);

      let query = supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .eq('category', categoryId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }
      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }
      if (confidenceThreshold !== undefined) {
        query = query.lte('category_confidence', confidenceThreshold);
      }

      const { data: transactions, error } = await query;

      if (error) throw error;

      // Get alternative suggestions for low-confidence transactions
      const transactionsWithAlternatives = await Promise.all(
        (transactions || []).map(async (transaction) => {
          if ((transaction.category_confidence || 0) < 0.7) {
            try {
              const suggestionResult = await transactionCategorizationService.categorizeTransaction(
                transaction,
                userId
              );

              return {
                ...transaction,
                alternatives: suggestionResult.success ? suggestionResult.data.suggestions : []
              };
            } catch (error) {
              return {
                ...transaction,
                alternatives: []
              };
            }
          }
          return transaction;
        })
      );

      console.log('✅ Found', transactionsWithAlternatives.length, 'transactions for category');
      return {
        success: true,
        data: {
          transactions: transactionsWithAlternatives,
          hasMore: transactionsWithAlternatives.length === limit
        }
      };
    } catch (error) {
      console.error('❌ Error getting transactions by category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process bulk categorization with progress tracking
   */
  async processBulkCategorization(userId, transactionIds, options = {}) {
    try {
      const {
        autoApply = false,
        confidenceThreshold = 0.7,
        onProgress
      } = options;

      const jobId = `bulk_${userId}_${Date.now()}`;
      console.log('🔄 Starting bulk categorization job:', jobId);

      // Initialize progress tracking
      this.processingQueue.set(jobId, {
        status: 'processing',
        total: transactionIds.length,
        processed: 0,
        successful: 0,
        failed: 0,
        startTime: Date.now()
      });

      const results = [];
      const batchSize = 10;

      for (let i = 0; i < transactionIds.length; i += batchSize) {
        const batch = transactionIds.slice(i, i + batchSize);
        
        // Get transactions for this batch
        const { data: transactions, error } = await supabase
          .from('transactions')
          .select('*')
          .eq('user_id', userId)
          .in('id', batch);

        if (error) {
          console.error('❌ Error fetching batch transactions:', error);
          continue;
        }

        // Process each transaction in the batch
        const batchPromises = (transactions || []).map(async (transaction) => {
          try {
            const categorizationResult = await transactionCategorizationService.categorizeTransaction(
              transaction,
              userId
            );

            if (!categorizationResult.success) {
              throw new Error(categorizationResult.error);
            }

            const result = {
              transactionId: transaction.id,
              originalCategory: transaction.category,
              suggestedCategory: categorizationResult.data.category,
              confidence: categorizationResult.data.confidence,
              alternatives: categorizationResult.data.suggestions,
              shouldAutoApply: autoApply && categorizationResult.data.confidence >= confidenceThreshold,
              applied: false
            };

            // Auto-apply if conditions are met
            if (result.shouldAutoApply) {
              const updateResult = await this.applyCategorization(
                userId,
                transaction.id,
                result.suggestedCategory,
                result.confidence
              );
              result.applied = updateResult.success;
            }

            return result;
          } catch (error) {
            console.error('❌ Error processing transaction:', transaction.id, error);
            return {
              transactionId: transaction.id,
              error: error.message,
              applied: false
            };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        
        batchResults.forEach((result, index) => {
          const processed = i + index + 1;
          
          if (result.status === 'fulfilled') {
            results.push(result.value);
            if (!result.value.error) {
              this.processingQueue.get(jobId).successful++;
            } else {
              this.processingQueue.get(jobId).failed++;
            }
          } else {
            results.push({
              transactionId: batch[index],
              error: result.reason?.message || 'Processing failed',
              applied: false
            });
            this.processingQueue.get(jobId).failed++;
          }

          this.processingQueue.get(jobId).processed = processed;
          
          // Call progress callback if provided
          if (onProgress) {
            onProgress({
              jobId,
              processed,
              total: transactionIds.length,
              successful: this.processingQueue.get(jobId).successful,
              failed: this.processingQueue.get(jobId).failed
            });
          }
        });
      }

      // Mark job as completed
      const jobData = this.processingQueue.get(jobId);
      jobData.status = 'completed';
      jobData.endTime = Date.now();
      jobData.duration = jobData.endTime - jobData.startTime;

      console.log('✅ Bulk categorization completed:', jobId);
      return {
        success: true,
        data: {
          jobId,
          results,
          summary: {
            total: transactionIds.length,
            successful: jobData.successful,
            failed: jobData.failed,
            autoApplied: results.filter(r => r.applied).length,
            duration: jobData.duration
          }
        }
      };
    } catch (error) {
      console.error('❌ Error in bulk categorization:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Apply categorization to a transaction
   */
  async applyCategorization(userId, transactionId, categoryId, confidence = 1.0) {
    try {
      const { error } = await supabase
        .from('transactions')
        .update({
          category: categoryId,
          category_confidence: confidence,
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)
        .eq('user_id', userId);

      if (error) throw error;

      // Learn from this categorization
      await transactionCategorizationService.learnFromFeedback(
        transactionId,
        categoryId,
        categoryId, // Same as predicted since it's being applied
        userId
      );

      return { success: true };
    } catch (error) {
      console.error('❌ Error applying categorization:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Bulk apply categorizations
   */
  async bulkApplyCategorizations(userId, categorizations) {
    try {
      console.log('🔄 Bulk applying', categorizations.length, 'categorizations');

      const results = [];
      const batchSize = 20;

      for (let i = 0; i < categorizations.length; i += batchSize) {
        const batch = categorizations.slice(i, i + batchSize);

        const batchPromises = batch.map(async (categorization) => {
          try {
            const result = await this.applyCategorization(
              userId,
              categorization.transactionId,
              categorization.categoryId,
              categorization.confidence || 1.0
            );

            return {
              transactionId: categorization.transactionId,
              success: result.success,
              error: result.error
            };
          } catch (error) {
            return {
              transactionId: categorization.transactionId,
              success: false,
              error: error.message
            };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults.map(result =>
          result.status === 'fulfilled' ? result.value : result.reason
        ));
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      console.log('✅ Bulk apply completed:', successCount, 'success,', failureCount, 'failures');
      return {
        success: true,
        data: {
          total: categorizations.length,
          successCount,
          failureCount,
          results
        }
      };
    } catch (error) {
      console.error('❌ Error in bulk apply:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get categorization job status
   */
  getJobStatus(jobId) {
    const job = this.processingQueue.get(jobId);
    if (!job) {
      return { success: false, error: 'Job not found' };
    }

    return {
      success: true,
      data: {
        jobId,
        status: job.status,
        progress: {
          total: job.total,
          processed: job.processed,
          successful: job.successful,
          failed: job.failed,
          percentage: job.total > 0 ? (job.processed / job.total) * 100 : 0
        },
        timing: {
          startTime: job.startTime,
          endTime: job.endTime,
          duration: job.duration || (Date.now() - job.startTime)
        }
      }
    };
  }

  /**
   * Cancel a running job
   */
  cancelJob(jobId) {
    const job = this.processingQueue.get(jobId);
    if (!job) {
      return { success: false, error: 'Job not found' };
    }

    if (job.status === 'completed') {
      return { success: false, error: 'Job already completed' };
    }

    job.status = 'cancelled';
    job.endTime = Date.now();
    job.duration = job.endTime - job.startTime;

    return { success: true };
  }

  /**
   * Get categorization statistics
   */
  async getCategorizationStats(userId, options = {}) {
    try {
      const { dateFrom, dateTo } = options;

      let query = supabase
        .from('transactions')
        .select('category, category_confidence, amount, created_at')
        .eq('user_id', userId);

      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }
      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }

      const { data: transactions, error } = await query;

      if (error) throw error;

      const stats = {
        total: transactions?.length || 0,
        categorized: 0,
        uncategorized: 0,
        highConfidence: 0,
        mediumConfidence: 0,
        lowConfidence: 0,
        categoryBreakdown: {},
        confidenceDistribution: {
          '0.9-1.0': 0,
          '0.7-0.9': 0,
          '0.5-0.7': 0,
          '0.3-0.5': 0,
          '0.0-0.3': 0
        }
      };

      (transactions || []).forEach(tx => {
        if (tx.category && tx.category !== 'other') {
          stats.categorized++;

          // Category breakdown
          stats.categoryBreakdown[tx.category] = (stats.categoryBreakdown[tx.category] || 0) + 1;

          // Confidence analysis
          const confidence = tx.category_confidence || 0;
          if (confidence >= 0.8) {
            stats.highConfidence++;
          } else if (confidence >= 0.6) {
            stats.mediumConfidence++;
          } else {
            stats.lowConfidence++;
          }

          // Confidence distribution
          if (confidence >= 0.9) {
            stats.confidenceDistribution['0.9-1.0']++;
          } else if (confidence >= 0.7) {
            stats.confidenceDistribution['0.7-0.9']++;
          } else if (confidence >= 0.5) {
            stats.confidenceDistribution['0.5-0.7']++;
          } else if (confidence >= 0.3) {
            stats.confidenceDistribution['0.3-0.5']++;
          } else {
            stats.confidenceDistribution['0.0-0.3']++;
          }
        } else {
          stats.uncategorized++;
        }
      });

      return { success: true, data: stats };
    } catch (error) {
      console.error('❌ Error getting categorization stats:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get similar transactions for pattern analysis
   */
  async getSimilarTransactions(userId, transactionId, options = {}) {
    try {
      const { limit = 10 } = options;

      // Get the reference transaction
      const { data: refTransaction, error: refError } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .eq('user_id', userId)
        .single();

      if (refError || !refTransaction) {
        throw new Error('Reference transaction not found');
      }

      // Find similar transactions
      let query = supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .neq('id', transactionId)
        .limit(limit * 3); // Get more to filter for similarity

      // Add similarity filters
      if (refTransaction.merchant_name) {
        query = query.ilike('merchant_name', `%${refTransaction.merchant_name}%`);
      } else if (refTransaction.description) {
        const keywords = refTransaction.description.split(' ').slice(0, 3);
        const searchPattern = keywords.join('|');
        query = query.or(`description.ilike.%${searchPattern}%`);
      }

      const { data: candidates, error } = await query;

      if (error) throw error;

      // Calculate similarity scores and sort
      const similarTransactions = (candidates || [])
        .map(tx => ({
          ...tx,
          similarity: this.calculateSimilarity(refTransaction, tx)
        }))
        .filter(tx => tx.similarity > 0.3)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      return {
        success: true,
        data: {
          reference: refTransaction,
          similar: similarTransactions
        }
      };
    } catch (error) {
      console.error('❌ Error getting similar transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Calculate similarity between two transactions
   */
  calculateSimilarity(tx1, tx2) {
    let similarity = 0;

    // Merchant name similarity
    if (tx1.merchant_name && tx2.merchant_name) {
      const merchantSim = this.calculateTextSimilarity(
        tx1.merchant_name.toLowerCase(),
        tx2.merchant_name.toLowerCase()
      );
      similarity += merchantSim * 0.4;
    }

    // Description similarity
    if (tx1.description && tx2.description) {
      const descSim = this.calculateTextSimilarity(
        tx1.description.toLowerCase(),
        tx2.description.toLowerCase()
      );
      similarity += descSim * 0.3;
    }

    // Amount similarity
    const amountSim = 1 - Math.abs(Math.abs(tx1.amount) - Math.abs(tx2.amount)) /
      Math.max(Math.abs(tx1.amount), Math.abs(tx2.amount));
    similarity += amountSim * 0.3;

    return Math.min(similarity, 1.0);
  }

  /**
   * Calculate text similarity using Jaccard index
   */
  calculateTextSimilarity(text1, text2) {
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  /**
   * Cleanup completed jobs
   */
  cleanupCompletedJobs() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [jobId, job] of this.processingQueue.entries()) {
      if (job.status === 'completed' && (now - job.endTime) > maxAge) {
        this.processingQueue.delete(jobId);
      }
    }
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.cache.clear();
    this.processingQueue.clear();
  }
}

export default new BulkCategorizationService();
