/**
 * Delivery Status Tracking Service
 * Implements tracking for notification delivery status, retry mechanisms,
 * failed delivery handling, and engagement analytics
 */

import { supabase } from './supabaseClient';
import enhancedNotificationService from './enhancedNotificationService';

// Delivery status constants
const DELIVERY_STATUS = {
  PENDING: 'pending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  BOUNCED: 'bounced',
  EXPIRED: 'expired'
};

// Retry configuration
const RETRY_CONFIG = {
  maxAttempts: 3,
  baseDelay: 5 * 60 * 1000, // 5 minutes
  maxDelay: 60 * 60 * 1000, // 1 hour
  backoffMultiplier: 2,
  retryableErrors: [
    'network_error',
    'timeout',
    'rate_limit',
    'temporary_failure',
    'service_unavailable'
  ]
};

class DeliveryTrackingService {
  constructor() {
    this.deliveryStatus = DELIVERY_STATUS;
    this.retryConfig = RETRY_CONFIG;
    this.retryQueue = new Map();
    this.isProcessingRetries = false;
    this.analyticsBuffer = [];
    this.analyticsFlushInterval = 60000; // 1 minute
    
    // Start background processes
    this.startRetryProcessor();
    this.startAnalyticsProcessor();
  }

  /**
   * Track notification delivery attempt
   */
  async trackDeliveryAttempt(notificationId, channel, status, metadata = {}) {
    try {
      console.log('📊 Tracking delivery attempt:', { 
        notificationId, 
        channel, 
        status 
      });

      const deliveryLog = {
        notification_id: notificationId,
        channel,
        status,
        provider: metadata.provider,
        external_id: metadata.externalId,
        error_message: metadata.error,
        response_data: metadata.responseData || {},
        attempted_at: new Date().toISOString()
      };

      // Add delivery timestamps based on status
      if (status === this.deliveryStatus.DELIVERED) {
        deliveryLog.delivered_at = new Date().toISOString();
      }

      const { data: logEntry, error } = await supabase
        .from('notification_delivery_logs')
        .insert(deliveryLog)
        .select()
        .single();

      if (error) throw error;

      // Update notification status
      await this.updateNotificationStatus(notificationId, status);

      // Handle failed deliveries
      if (status === this.deliveryStatus.FAILED) {
        await this.handleFailedDelivery(notificationId, channel, metadata);
      }

      // Buffer analytics data
      this.bufferAnalyticsData(notificationId, channel, status);

      console.log('✅ Delivery attempt tracked');

      return {
        success: true,
        logEntry
      };
    } catch (error) {
      console.error('❌ Error tracking delivery attempt:', error);
      throw error;
    }
  }

  /**
   * Update notification status
   */
  async updateNotificationStatus(notificationId, status) {
    try {
      // Get current notification
      const { data: notification, error: fetchError } = await supabase
        .from('notifications')
        .select('status, channels')
        .eq('id', notificationId)
        .single();

      if (fetchError) throw fetchError;

      // Determine overall status based on channel deliveries
      let overallStatus = status;
      
      if (notification.channels.length > 1) {
        // For multi-channel notifications, check all delivery logs
        const { data: deliveryLogs, error: logsError } = await supabase
          .from('notification_delivery_logs')
          .select('channel, status')
          .eq('notification_id', notificationId);

        if (!logsError && deliveryLogs) {
          const channelStatuses = deliveryLogs.reduce((acc, log) => {
            acc[log.channel] = log.status;
            return acc;
          }, {});

          // If any channel delivered successfully, mark as delivered
          if (Object.values(channelStatuses).includes(this.deliveryStatus.DELIVERED)) {
            overallStatus = this.deliveryStatus.DELIVERED;
          } else if (Object.values(channelStatuses).every(s => s === this.deliveryStatus.FAILED)) {
            overallStatus = this.deliveryStatus.FAILED;
          } else {
            overallStatus = this.deliveryStatus.SENT;
          }
        }
      }

      // Update notification status
      const { error: updateError } = await supabase
        .from('notifications')
        .update({
          status: overallStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      if (updateError) throw updateError;

    } catch (error) {
      console.error('❌ Error updating notification status:', error);
      // Don't throw - status update failure shouldn't stop delivery tracking
    }
  }

  /**
   * Handle failed delivery
   */
  async handleFailedDelivery(notificationId, channel, metadata) {
    try {
      console.log('🔄 Handling failed delivery:', { notificationId, channel });

      // Get notification details
      const { data: notification, error } = await supabase
        .from('notifications')
        .select('retry_count, max_retries, type, expires_at')
        .eq('id', notificationId)
        .single();

      if (error) throw error;

      // Check if retry is possible
      const canRetry = this.canRetryDelivery(notification, metadata.error);
      
      if (canRetry) {
        await this.scheduleRetry(notificationId, channel, notification);
      } else {
        console.log('❌ Max retries reached or non-retryable error');
        
        // Mark as permanently failed
        await supabase
          .from('notifications')
          .update({
            status: this.deliveryStatus.FAILED,
            updated_at: new Date().toISOString()
          })
          .eq('id', notificationId);
      }

    } catch (error) {
      console.error('❌ Error handling failed delivery:', error);
    }
  }

  /**
   * Check if delivery can be retried
   */
  canRetryDelivery(notification, errorType) {
    // Check retry count
    if (notification.retry_count >= notification.max_retries) {
      return false;
    }

    // Check if notification has expired
    if (notification.expires_at && new Date(notification.expires_at) < new Date()) {
      return false;
    }

    // Check if error is retryable
    if (errorType && !this.retryConfig.retryableErrors.includes(errorType)) {
      return false;
    }

    return true;
  }

  /**
   * Schedule retry for failed delivery
   */
  async scheduleRetry(notificationId, channel, notification) {
    try {
      console.log('⏰ Scheduling retry:', { notificationId, channel });

      // Calculate retry delay with exponential backoff
      const retryDelay = Math.min(
        this.retryConfig.baseDelay * Math.pow(
          this.retryConfig.backoffMultiplier, 
          notification.retry_count
        ),
        this.retryConfig.maxDelay
      );

      const retryAt = new Date(Date.now() + retryDelay);

      // Update retry count
      await supabase
        .from('notifications')
        .update({
          retry_count: notification.retry_count + 1,
          status: 'retrying',
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      // Add to retry queue
      this.retryQueue.set(`${notificationId}_${channel}`, {
        notificationId,
        channel,
        retryAt,
        attempt: notification.retry_count + 1
      });

      console.log('✅ Retry scheduled for:', retryAt);

    } catch (error) {
      console.error('❌ Error scheduling retry:', error);
    }
  }

  /**
   * Start retry processor
   */
  startRetryProcessor() {
    setInterval(async () => {
      if (!this.isProcessingRetries && this.retryQueue.size > 0) {
        await this.processRetryQueue();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Process retry queue
   */
  async processRetryQueue() {
    this.isProcessingRetries = true;

    try {
      const now = new Date();
      const retryItems = Array.from(this.retryQueue.entries())
        .filter(([key, item]) => item.retryAt <= now);

      for (const [key, item] of retryItems) {
        try {
          await this.retryDelivery(item);
          this.retryQueue.delete(key);
        } catch (error) {
          console.error('❌ Error retrying delivery:', error);
          // Keep in queue for next attempt
        }
      }

    } catch (error) {
      console.error('❌ Error processing retry queue:', error);
    } finally {
      this.isProcessingRetries = false;
    }
  }

  /**
   * Retry delivery for specific notification and channel
   */
  async retryDelivery(retryItem) {
    try {
      console.log('🔄 Retrying delivery:', retryItem);

      // Get notification details
      const { data: notification, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('id', retryItem.notificationId)
        .single();

      if (error) throw error;

      // Retry delivery through enhanced notification service
      const result = await enhancedNotificationService.sendThroughChannel(
        retryItem.channel,
        notification.user_id,
        {
          type: notification.type,
          title: notification.title,
          content: notification.content,
          data: notification.data
        },
        notification.id
      );

      console.log('✅ Retry delivery completed:', result.success);

    } catch (error) {
      console.error('❌ Error in retry delivery:', error);
      throw error;
    }
  }

  /**
   * Track engagement events (open, click)
   */
  async trackEngagement(notificationId, eventType, metadata = {}) {
    try {
      console.log('👆 Tracking engagement:', { notificationId, eventType });

      const eventColumn = `${eventType}_at`;
      const updateData = {
        [eventColumn]: new Date().toISOString()
      };

      // Update delivery log
      const { error } = await supabase
        .from('notification_delivery_logs')
        .update(updateData)
        .eq('notification_id', notificationId)
        .eq('status', this.deliveryStatus.DELIVERED);

      if (error) throw error;

      // Buffer analytics data
      this.bufferAnalyticsData(notificationId, metadata.channel, eventType);

      console.log('✅ Engagement tracked');

      return { success: true };
    } catch (error) {
      console.error('❌ Error tracking engagement:', error);
      throw error;
    }
  }

  /**
   * Buffer analytics data for batch processing
   */
  bufferAnalyticsData(notificationId, channel, event) {
    this.analyticsBuffer.push({
      notificationId,
      channel,
      event,
      timestamp: new Date().toISOString(),
      date: new Date().toISOString().split('T')[0]
    });
  }

  /**
   * Start analytics processor
   */
  startAnalyticsProcessor() {
    setInterval(async () => {
      if (this.analyticsBuffer.length > 0) {
        await this.flushAnalyticsBuffer();
      }
    }, this.analyticsFlushInterval);
  }

  /**
   * Flush analytics buffer to database
   */
  async flushAnalyticsBuffer() {
    try {
      if (this.analyticsBuffer.length === 0) return;

      console.log('📊 Flushing analytics buffer:', this.analyticsBuffer.length);

      const buffer = [...this.analyticsBuffer];
      this.analyticsBuffer = [];

      // Group by date, type, and channel
      const grouped = buffer.reduce((acc, item) => {
        // Get notification type from database (simplified for now)
        const key = `${item.date}_${item.channel}_${item.event}`;
        
        if (!acc[key]) {
          acc[key] = {
            date: item.date,
            channel: item.channel,
            event: item.event,
            count: 0
          };
        }
        
        acc[key].count++;
        return acc;
      }, {});

      // Update analytics in database
      for (const analytics of Object.values(grouped)) {
        await this.updateAnalytics(analytics);
      }

      console.log('✅ Analytics buffer flushed');

    } catch (error) {
      console.error('❌ Error flushing analytics buffer:', error);
    }
  }

  /**
   * Update analytics in database
   */
  async updateAnalytics(analytics) {
    try {
      // This is a simplified version - in production, you'd want to
      // get the actual notification type from the notification record
      const notificationType = 'transaction_completed'; // Default

      const { error } = await supabase
        .rpc('update_notification_analytics', {
          p_date: analytics.date,
          p_type: notificationType,
          p_channel: analytics.channel,
          p_event: analytics.event
        });

      if (error) throw error;

    } catch (error) {
      console.error('❌ Error updating analytics:', error);
    }
  }

  /**
   * Get delivery statistics for notification
   */
  async getDeliveryStats(notificationId) {
    try {
      const { data: deliveryLogs, error } = await supabase
        .from('notification_delivery_logs')
        .select('channel, status, attempted_at, delivered_at, opened_at, clicked_at, error_message')
        .eq('notification_id', notificationId)
        .order('attempted_at', { ascending: true });

      if (error) throw error;

      const stats = {
        totalAttempts: deliveryLogs.length,
        channels: {},
        overallStatus: 'pending',
        firstAttempt: null,
        lastAttempt: null,
        deliveryTime: null
      };

      // Process delivery logs
      deliveryLogs.forEach(log => {
        if (!stats.channels[log.channel]) {
          stats.channels[log.channel] = {
            status: log.status,
            attempts: 0,
            lastError: null,
            deliveredAt: null,
            openedAt: null,
            clickedAt: null
          };
        }

        const channelStats = stats.channels[log.channel];
        channelStats.attempts++;
        channelStats.status = log.status;
        channelStats.lastError = log.error_message;
        channelStats.deliveredAt = log.delivered_at;
        channelStats.openedAt = log.opened_at;
        channelStats.clickedAt = log.clicked_at;

        // Update overall stats
        if (!stats.firstAttempt || log.attempted_at < stats.firstAttempt) {
          stats.firstAttempt = log.attempted_at;
        }
        if (!stats.lastAttempt || log.attempted_at > stats.lastAttempt) {
          stats.lastAttempt = log.attempted_at;
        }
        if (log.delivered_at && !stats.deliveryTime) {
          stats.deliveryTime = new Date(log.delivered_at) - new Date(log.attempted_at);
        }
      });

      // Determine overall status
      const channelStatuses = Object.values(stats.channels).map(c => c.status);
      if (channelStatuses.includes('delivered')) {
        stats.overallStatus = 'delivered';
      } else if (channelStatuses.includes('sent')) {
        stats.overallStatus = 'sent';
      } else if (channelStatuses.every(s => s === 'failed')) {
        stats.overallStatus = 'failed';
      }

      return {
        success: true,
        stats
      };
    } catch (error) {
      console.error('❌ Error getting delivery stats:', error);
      throw error;
    }
  }

  /**
   * Get delivery analytics for date range
   */
  async getDeliveryAnalytics(startDate, endDate, filters = {}) {
    try {
      let query = supabase
        .from('notification_analytics')
        .select('*')
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: true });

      // Apply filters
      if (filters.channel) {
        query = query.eq('channel', filters.channel);
      }
      if (filters.notificationType) {
        query = query.eq('notification_type', filters.notificationType);
      }

      const { data: analytics, error } = await query;

      if (error) throw error;

      // Calculate summary statistics
      const summary = analytics.reduce((acc, row) => {
        acc.totalSent += row.total_sent;
        acc.totalDelivered += row.total_delivered;
        acc.totalOpened += row.total_opened;
        acc.totalClicked += row.total_clicked;
        acc.totalFailed += row.total_failed;
        return acc;
      }, {
        totalSent: 0,
        totalDelivered: 0,
        totalOpened: 0,
        totalClicked: 0,
        totalFailed: 0
      });

      // Calculate rates
      summary.deliveryRate = summary.totalSent > 0 ? 
        (summary.totalDelivered / summary.totalSent) * 100 : 0;
      summary.openRate = summary.totalDelivered > 0 ? 
        (summary.totalOpened / summary.totalDelivered) * 100 : 0;
      summary.clickRate = summary.totalOpened > 0 ? 
        (summary.totalClicked / summary.totalOpened) * 100 : 0;

      return {
        success: true,
        analytics,
        summary,
        dateRange: { startDate, endDate }
      };
    } catch (error) {
      console.error('❌ Error getting delivery analytics:', error);
      throw error;
    }
  }

  /**
   * Get retry queue status
   */
  getRetryQueueStatus() {
    const queueItems = Array.from(this.retryQueue.values());
    
    return {
      queueLength: queueItems.length,
      nextRetry: queueItems.length > 0 ? 
        Math.min(...queueItems.map(item => item.retryAt.getTime())) : null,
      isProcessing: this.isProcessingRetries,
      analyticsBufferSize: this.analyticsBuffer.length
    };
  }

  /**
   * Clear expired items from retry queue
   */
  clearExpiredRetries() {
    const now = new Date();
    const expiredKeys = [];

    for (const [key, item] of this.retryQueue.entries()) {
      // Remove items older than 24 hours
      if (now - item.retryAt > 24 * 60 * 60 * 1000) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.retryQueue.delete(key));
    
    console.log('🧹 Cleared expired retries:', expiredKeys.length);
  }
}

export default new DeliveryTrackingService();
