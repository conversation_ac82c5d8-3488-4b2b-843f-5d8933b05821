/**
 * Enhanced Analytics Service
 * Provides comprehensive financial analytics with real-time data aggregation
 * Integrates with all financial systems for unified insights
 */

import supabase from './supabaseClient';
import walletService from './walletService';
import savingsAccountService from './savingsAccountService';
import investmentPortfolioService from './investmentPortfolioService';
import realTimeEventService from './realTimeEventService';
import analyticsPerformanceService from './analyticsPerformanceService';
import transactionCategorizationService from './transactionCategorizationService';
import categoryManagementService from './categoryManagementService';
import predictiveAnalyticsService from './predictiveAnalyticsService';
import budgetManagementService from './budgetManagementService';

class EnhancedAnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.subscribers = new Set();
    this.realTimeChannel = null;
    this.isInitialized = false;
    this.currentUserId = null;
  }

  /**
   * Initialize real-time analytics subscriptions
   */
  async initializeRealTime(userId) {
    try {
      // Check if already initialized for this user
      if (this.isInitialized && this.currentUserId === userId && this.realTimeChannel) {
        console.log('📊 Real-time analytics already initialized for user:', userId);
        return { success: true, message: 'Already initialized' };
      }

      // Prevent multiple simultaneous initializations
      if (this.initializationPromise) {
        console.log('📊 Real-time analytics initialization already in progress, waiting...');
        return await this.initializationPromise;
      }

      // Create initialization promise to prevent race conditions
      this.initializationPromise = this._doInitializeRealTime(userId);
      const result = await this.initializationPromise;
      this.initializationPromise = null;
      return result;
    } catch (error) {
      this.initializationPromise = null;
      console.error('❌ Error initializing real-time analytics:', error);
      return { success: false, error: error.message };
    }
  }

  async _doInitializeRealTime(userId) {
    try {
      // Cleanup any existing subscriptions
      await this.cleanup();

      console.log('📊 Initializing real-time analytics for user:', userId);

      // Subscribe to real-time updates
      this.realTimeChannel = supabase
        .channel(`analytics_${userId}_${Date.now()}`) // Add timestamp to ensure unique channel
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${userId}`
          },
          (payload) => this.handleTransactionUpdate(payload)
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'savings_transactions',
            filter: `user_id=eq.${userId}`
          },
          (payload) => this.handleSavingsUpdate(payload)
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'investment_transactions',
            filter: `user_id=eq.${userId}`
          },
          (payload) => this.handleInvestmentUpdate(payload)
        );

      // Subscribe with improved error handling
      const subscriptionResult = await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.warn('⚠️ Real-time subscription timeout, continuing without real-time features');
          resolve({ success: true, warning: 'Real-time features disabled due to timeout' });
        }, 3000); // Reduced timeout to 3 seconds

        this.realTimeChannel
          .subscribe((status) => {
            console.log('📊 Subscription status:', status);

            if (status === 'SUBSCRIBED') {
              clearTimeout(timeout);
              resolve({ success: true });
            } else if (status === 'CHANNEL_ERROR') {
              clearTimeout(timeout);
              console.warn('⚠️ Channel error, continuing without real-time features');
              resolve({ success: true, warning: 'Real-time features disabled due to channel error' });
            } else if (status === 'TIMED_OUT') {
              clearTimeout(timeout);
              console.warn('⚠️ Subscription timed out, continuing without real-time features');
              resolve({ success: true, warning: 'Real-time features disabled due to timeout' });
            } else if (status === 'CLOSED') {
              clearTimeout(timeout);
              console.warn('⚠️ Channel closed, continuing without real-time features');
              resolve({ success: true, warning: 'Real-time features disabled due to closed channel' });
            }
            // For other statuses (JOINING, etc.), continue waiting
          });
      });

      this.isInitialized = true;
      this.currentUserId = userId;

      console.log('✅ Real-time analytics initialized successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing real-time analytics:', error);

      // Cleanup on error
      await this.cleanup();

      return { success: false, error: error.message };
    }
  }

  /**
   * Handle real-time transaction updates
   */
  handleTransactionUpdate(payload) {
    console.log('📊 Transaction update received:', payload);
    this.invalidateCache();
    this.notifySubscribers('transaction_update', payload);
  }

  /**
   * Handle real-time savings updates
   */
  handleSavingsUpdate(payload) {
    console.log('📊 Savings update received:', payload);
    this.invalidateCache();
    this.notifySubscribers('savings_update', payload);
  }

  /**
   * Handle real-time investment updates
   */
  handleInvestmentUpdate(payload) {
    console.log('📊 Investment update received:', payload);
    this.invalidateCache();
    this.notifySubscribers('investment_update', payload);
  }

  /**
   * Subscribe to real-time analytics updates
   */
  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  /**
   * Notify all subscribers of updates
   */
  notifySubscribers(type, data) {
    this.subscribers.forEach(callback => {
      try {
        callback({ type, data });
      } catch (error) {
        console.error('❌ Error notifying subscriber:', error);
      }
    });
  }

  /**
   * Get comprehensive dashboard analytics
   */
  async getDashboardAnalytics(userId, period = 'month') {
    try {
      const cacheKey = `dashboard_${userId}_${period}`;

      // Use performance service for optimized data retrieval
      const result = await analyticsPerformanceService.getOptimizedData(
        cacheKey,
        async () => {
          console.log('📊 Getting dashboard analytics for user:', userId, 'period:', period);

          // Get date range for period
          const dateRange = this.getDateRange(period);

          // Create batch queries for optimal performance
          const queries = [
            {
              key: 'wallet',
              queryFunction: () => this.getWalletAnalytics(userId),
              options: { useCache: true, cacheTTL: 2 * 60 * 1000 } // 2 minutes for wallet data
            },
            {
              key: 'transactions',
              queryFunction: () => this.getTransactionAnalytics(userId, dateRange),
              options: { useCache: true }
            },
            {
              key: 'savings',
              queryFunction: () => this.getSavingsAnalytics(userId, dateRange),
              options: { useCache: true }
            },
            {
              key: 'investments',
              queryFunction: () => this.getInvestmentAnalytics(userId, dateRange),
              options: { useCache: true }
            },
            {
              key: 'spendingCategories',
              queryFunction: () => this.getSpendingByCategory(userId, dateRange),
              options: { useCache: true }
            },
            {
              key: 'categorizationInsights',
              queryFunction: () => this.getCategorizationInsights(userId, dateRange),
              options: { useCache: true }
            },
            {
              key: 'monthlyTrends',
              queryFunction: () => this.getMonthlyTrends(userId),
              options: { useCache: true }
            },
            {
              key: 'predictiveForecasts',
              queryFunction: async () => {
                try {
                  await predictiveAnalyticsService.initialize(userId);
                  const result = await predictiveAnalyticsService.generateSpendingForecast(userId, 'month', 3);
                  return result.success ? result.data : null;
                } catch (error) {
                  console.warn('⚠️ Error loading predictive forecasts:', error);
                  return null;
                }
              },
              options: { useCache: true, cacheTTL: 10 * 60 * 1000 } // 10 minutes for forecasts
            },
            {
              key: 'budgetAnalytics',
              queryFunction: async () => {
                try {
                  await budgetManagementService.initialize(userId);
                  const result = await budgetManagementService.getBudgetAnalytics(userId);
                  return result.success ? result.data : null;
                } catch (error) {
                  console.warn('⚠️ Error loading budget analytics:', error);
                  return null;
                }
              },
              options: { useCache: true, cacheTTL: 5 * 60 * 1000 } // 5 minutes for budget data
            },
            {
              key: 'budgetRecommendations',
              queryFunction: async () => {
                try {
                  await budgetManagementService.initialize(userId);
                  const result = await budgetManagementService.getBudgetRecommendations(userId);
                  return result.success ? result.data : [];
                } catch (error) {
                  console.warn('⚠️ Error loading budget recommendations:', error);
                  return [];
                }
              },
              options: { useCache: true, cacheTTL: 5 * 60 * 1000 } // 5 minutes for recommendations
            }
          ];

          // Execute batch queries
          const batchResult = await analyticsPerformanceService.batchQueries(queries);

          if (!batchResult.success) {
            throw new Error('Failed to fetch analytics data');
          }

          // Extract data from batch results
          const walletData = batchResult.results.wallet?.data || {};
          const transactionData = batchResult.results.transactions?.data || {};
          const savingsData = batchResult.results.savings?.data || {};
          const investmentData = batchResult.results.investments?.data || {};
          const spendingCategories = batchResult.results.spendingCategories?.data || [];
          const monthlyTrends = batchResult.results.monthlyTrends?.data || [];
          const categorizationInsights = batchResult.results.categorizationInsights?.data || {};
          const predictiveForecasts = batchResult.results.predictiveForecasts?.data || null;
          const budgetAnalytics = batchResult.results.budgetAnalytics?.data || null;
          const budgetRecommendations = batchResult.results.budgetRecommendations?.data || [];

          // Calculate comprehensive insights
          const analytics = {
            period,
            dateRange,
            wallet: walletData,
            transactions: transactionData,
            savings: savingsData,
            investments: investmentData,
            spendingCategories,
            monthlyTrends,
            categorizationInsights,
            predictiveForecasts,
            budgetAnalytics,
            budgetRecommendations,
            summary: this.calculateSummaryMetrics({
              wallet: walletData,
              transactions: transactionData,
              savings: savingsData,
              investments: investmentData,
              budgetAnalytics
            }),
            insights: this.generateFinancialInsights({
              transactions: transactionData,
              savings: savingsData,
              investments: investmentData,
              spendingCategories,
              predictiveForecasts,
              budgetAnalytics,
              budgetRecommendations
            }),
            lastUpdated: new Date().toISOString()
          };

          console.log('✅ Dashboard analytics calculated successfully');
          return analytics;
        },
        { useCache: true, cacheTTL: this.cacheTimeout }
      );

      if (result.success) {
        return {
          success: true,
          data: result.data,
          fromCache: result.fromCache
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('❌ Error getting dashboard analytics:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Get wallet analytics
   */
  async getWalletAnalytics(userId) {
    try {
      const walletResult = await walletService.getWalletBalance(userId);
      const wallet = walletResult.success ? walletResult.data : null;

      return {
        currentBalance: wallet?.balance || 0,
        availableBalance: wallet?.available_balance || 0,
        pendingBalance: wallet?.pending_balance || 0,
        currency: wallet?.currency || 'UGX',
        dailyLimit: wallet?.daily_limit || 0,
        monthlyLimit: wallet?.monthly_limit || 0,
        spentToday: wallet?.spent_today || 0,
        spentThisMonth: wallet?.spent_this_month || 0
      };
    } catch (error) {
      console.error('❌ Error getting wallet analytics:', error);
      return {};
    }
  }

  /**
   * Get transaction analytics for period
   */
  async getTransactionAnalytics(userId, dateRange) {
    try {
      // Use optimized query from performance service
      const query = analyticsPerformanceService.createOptimizedTransactionQuery(
        userId,
        dateRange,
        {
          selectFields: 'id, amount, transaction_type, category, status, created_at',
          limit: 1000, // Reasonable limit for analytics
          orderBy: 'created_at',
          orderDirection: 'desc'
        }
      );

      const { data: transactions, error } = await query;
      if (error) throw error;

      const analytics = {
        totalTransactions: transactions.length,
        totalSpent: 0,
        totalReceived: 0,
        averageTransaction: 0,
        transactionsByType: {},
        transactionsByStatus: {},
        dailyTransactions: this.groupTransactionsByDay(transactions),
        recentTransactions: transactions.slice(0, 10)
      };

      // Calculate transaction metrics
      transactions.forEach(tx => {
        const amount = Math.abs(tx.amount);
        
        if (tx.amount < 0) {
          analytics.totalSpent += amount;
        } else {
          analytics.totalReceived += amount;
        }

        // Group by type
        analytics.transactionsByType[tx.transaction_type] = 
          (analytics.transactionsByType[tx.transaction_type] || 0) + 1;

        // Group by status
        analytics.transactionsByStatus[tx.status] = 
          (analytics.transactionsByStatus[tx.status] || 0) + 1;
      });

      analytics.averageTransaction = transactions.length > 0 
        ? (analytics.totalSpent + analytics.totalReceived) / transactions.length 
        : 0;

      analytics.netFlow = analytics.totalReceived - analytics.totalSpent;

      return analytics;
    } catch (error) {
      console.error('❌ Error getting transaction analytics:', error);
      return {};
    }
  }

  /**
   * Get savings analytics for period
   */
  async getSavingsAnalytics(userId, dateRange) {
    try {
      // Get savings accounts
      const accountsResult = await savingsAccountService.getUserSavingsAccounts(userId);
      const accounts = accountsResult.success ? (accountsResult.accounts || accountsResult.data || []) : [];

      // Get savings transactions
      const { data: transactions, error } = await supabase
        .from('savings_transactions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', dateRange.start)
        .lte('created_at', dateRange.end)
        .order('created_at', { ascending: false });

      if (error) {
        console.warn('⚠️ Error fetching savings transactions:', error);
        // Continue with empty transactions array
      }

      const safeTransactions = transactions || [];

      const safeAccounts = Array.isArray(accounts) ? accounts : [];

      const analytics = {
        totalAccounts: safeAccounts.length,
        totalBalance: safeAccounts.reduce((sum, acc) => sum + (acc.current_balance || acc.balance || 0), 0),
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalInterest: 0,
        goalProgress: this.calculateGoalProgress(safeAccounts),
        accountsByType: this.groupAccountsByType(safeAccounts),
        monthlyGrowth: this.calculateMonthlySavingsGrowth(safeTransactions)
      };

      // Calculate transaction metrics
      safeTransactions.forEach(tx => {
        const amount = Math.abs(tx.amount);
        
        switch (tx.transaction_type) {
          case 'deposit':
            analytics.totalDeposits += amount;
            break;
          case 'withdrawal':
            analytics.totalWithdrawals += amount;
            break;
          case 'interest':
            analytics.totalInterest += amount;
            break;
        }
      });

      analytics.netSavings = analytics.totalDeposits - analytics.totalWithdrawals;
      analytics.savingsRate = analytics.totalDeposits > 0 
        ? (analytics.netSavings / analytics.totalDeposits) * 100 
        : 0;

      return analytics;
    } catch (error) {
      console.error('❌ Error getting savings analytics:', error);
      return {
        totalAccounts: 0,
        totalBalance: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalInterest: 0,
        netSavings: 0,
        savingsRate: 0,
        goalProgress: { totalGoals: 0, averageProgress: 0, goals: [] },
        accountsByType: {},
        monthlyGrowth: []
      };
    }
  }

  /**
   * Get investment analytics for period
   */
  async getInvestmentAnalytics(userId, dateRange) {
    try {
      // Get investment portfolios
      const portfoliosResult = await investmentPortfolioService.getUserPortfolios(userId);
      const portfolios = portfoliosResult.success ? (portfoliosResult.portfolios || portfoliosResult.data || []) : [];

      // Get investment transactions
      const { data: transactions, error } = await supabase
        .from('investment_transactions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', dateRange.start)
        .lte('created_at', dateRange.end)
        .order('created_at', { ascending: false });

      if (error) {
        console.warn('⚠️ Error fetching investment transactions:', error);
        // Continue with empty transactions array
      }

      const safeTransactions = transactions || [];

      const safePortfolios = Array.isArray(portfolios) ? portfolios : [];

      const analytics = {
        totalPortfolios: safePortfolios.length,
        totalValue: safePortfolios.reduce((sum, p) => sum + (p.current_value || 0), 0),
        totalInvested: safePortfolios.reduce((sum, p) => sum + (p.total_invested || p.invested || 0), 0),
        totalGainLoss: 0,
        totalReturn: 0,
        performanceMetrics: this.calculateInvestmentPerformance(safePortfolios),
        assetAllocation: this.calculateAssetAllocation(safePortfolios),
        riskMetrics: this.calculateRiskMetrics(safePortfolios)
      };

      analytics.totalGainLoss = analytics.totalValue - analytics.totalInvested;
      analytics.totalReturn = analytics.totalInvested > 0 
        ? (analytics.totalGainLoss / analytics.totalInvested) * 100 
        : 0;

      return analytics;
    } catch (error) {
      console.error('❌ Error getting investment analytics:', error);
      return {
        totalPortfolios: 0,
        totalValue: 0,
        totalInvested: 0,
        totalGainLoss: 0,
        totalReturn: 0,
        performanceMetrics: { totalReturn: 0, bestPerformer: null, worstPerformer: null },
        assetAllocation: [],
        riskMetrics: { averageRiskScore: 0, riskLevel: 'unknown', portfolioCount: 0 }
      };
    }
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  invalidateCache() {
    this.cache.clear();
  }

  /**
   * Utility methods
   */
  getDateRange(period) {
    const now = new Date();
    const start = new Date();

    switch (period) {
      case 'week':
        start.setDate(now.getDate() - 7);
        break;
      case 'month':
        start.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        start.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        start.setFullYear(now.getFullYear() - 1);
        break;
      default:
        start.setMonth(now.getMonth() - 1);
    }

    return {
      start: start.toISOString(),
      end: now.toISOString()
    };
  }

  /**
   * Get spending by category with enhanced categorization
   */
  async getSpendingByCategory(userId, dateRange) {
    try {
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('category, amount, description, merchant_name, category_confidence')
        .eq('user_id', userId)
        .lt('amount', 0) // Only expenses
        .gte('created_at', dateRange.start)
        .lte('created_at', dateRange.end);

      if (error) {
        console.warn('⚠️ Error fetching transactions for spending analysis:', error);
        return {
          categories: [],
          totalAmount: 0,
          uncategorizedCount: 0,
          needsReview: 0
        };
      }

      // Use basic category mapping with fallbacks (independent of category service)
      const basicCategoryMap = {
        'food_dining': { name: 'Food & Dining', icon: 'restaurant', color: '#FF6B6B' },
        'transportation': { name: 'Transportation', icon: 'car', color: '#4ECDC4' },
        'shopping': { name: 'Shopping', icon: 'bag', color: '#45B7D1' },
        'bills_utilities': { name: 'Bills & Utilities', icon: 'receipt', color: '#96CEB4' },
        'entertainment': { name: 'Entertainment', icon: 'play-circle', color: '#FFEAA7' },
        'healthcare': { name: 'Healthcare', icon: 'medical', color: '#FD79A8' },
        'education': { name: 'Education', icon: 'school', color: '#A29BFE' },
        'personal_care': { name: 'Personal Care', icon: 'person', color: '#74B9FF' },
        'transfer': { name: 'Transfers', icon: 'swap-horizontal', color: '#636E72' },
        'other': { name: 'Other', icon: 'ellipsis-horizontal', color: '#B2BEC3' }
      };

      // Try to get enhanced categories, but don't fail if service is unavailable
      let categoryMap = { ...basicCategoryMap };
      try {
        const categoriesResult = await categoryManagementService.getUserCategories(userId);
        if (categoriesResult && categoriesResult.success && Array.isArray(categoriesResult.data)) {
          categoriesResult.data.forEach(cat => {
            if (cat && cat.id) {
              categoryMap[cat.id] = {
                name: cat.name || cat.id,
                icon: cat.icon || 'folder',
                color: cat.color || '#B2BEC3'
              };
            }
          });
        }
      } catch (error) {
        console.warn('⚠️ Category service unavailable, using basic categories:', error);
      }

      const categories = {};
      const uncategorizedTransactions = [];
      const safeTransactions = Array.isArray(transactions) ? transactions : [];

      safeTransactions.forEach(tx => {
        try {
          if (!tx || typeof tx.amount !== 'number') return;

          const categoryId = tx.category || 'other';
          const amount = Math.abs(tx.amount);

          // Track uncategorized or low-confidence transactions
          if (!tx.category || tx.category === 'other' || (tx.category_confidence || 0) < 0.5) {
            uncategorizedTransactions.push(tx);
          }

          if (!categories[categoryId]) {
            const categoryInfo = categoryMap[categoryId] || categoryMap['other'];
            categories[categoryId] = {
              id: categoryId,
              name: categoryInfo.name || categoryId,
              icon: categoryInfo.icon || 'folder',
              color: categoryInfo.color || '#B2BEC3',
              amount: 0,
              transactionCount: 0,
              averageAmount: 0,
              confidence: 0
            };
          }

          categories[categoryId].amount += amount;
          categories[categoryId].transactionCount++;
          categories[categoryId].confidence += (tx.category_confidence || 0);
        } catch (txError) {
          console.warn('⚠️ Error processing transaction for spending analysis:', txError);
        }
      });

      // Calculate averages and finalize data
      let categoryArray = [];
      try {
        categoryArray = Object.values(categories).map(cat => {
          if (!cat) return null;

          return {
            ...cat,
            averageAmount: cat.transactionCount > 0 ? cat.amount / cat.transactionCount : 0,
            averageConfidence: cat.transactionCount > 0 ? cat.confidence / cat.transactionCount : 0,
            percentage: 0 // Will be calculated below
          };
        }).filter(Boolean); // Remove any null entries
      } catch (error) {
        console.warn('⚠️ Error processing category data:', error);
        categoryArray = [];
      }

      // Calculate percentages
      const safeCategoryArray = Array.isArray(categoryArray) ? categoryArray : [];
      const totalAmount = safeCategoryArray.reduce((sum, cat) => sum + (cat?.amount || 0), 0);

      safeCategoryArray.forEach(cat => {
        try {
          if (cat && typeof cat.amount === 'number') {
            cat.percentage = totalAmount > 0 ? (cat.amount / totalAmount) * 100 : 0;
          }
        } catch (error) {
          console.warn('⚠️ Error calculating percentage for category:', error);
        }
      });

      // Sort by amount
      try {
        safeCategoryArray.sort((a, b) => (b?.amount || 0) - (a?.amount || 0));
      } catch (error) {
        console.warn('⚠️ Error sorting categories:', error);
      }

      return {
        categories: safeCategoryArray,
        totalAmount: totalAmount || 0,
        uncategorizedCount: Array.isArray(uncategorizedTransactions) ? uncategorizedTransactions.length : 0,
        needsReview: safeCategoryArray.filter(cat => cat && typeof cat.averageConfidence === 'number' && cat.averageConfidence < 0.7).length
      };
    } catch (error) {
      console.error('❌ Error getting spending by category:', error);
      return {
        categories: [],
        totalAmount: 0,
        uncategorizedCount: 0,
        needsReview: 0
      };
    }
  }

  /**
   * Get categorization insights and recommendations
   */
  async getCategorizationInsights(userId, dateRange) {
    try {
      console.log('🤖 Getting categorization insights for user:', userId);

      // Try to initialize categorization service, but don't fail if unavailable
      let categorizationServiceAvailable = false;
      try {
        await transactionCategorizationService.initialize(userId);
        categorizationServiceAvailable = true;
      } catch (initError) {
        console.warn('⚠️ Categorization service unavailable:', initError);
      }

      // Get uncategorized transactions with error handling
      let uncategorizedTx = [];
      try {
        const { data, error } = await supabase
          .from('transactions')
          .select('id, amount, description, merchant_name, created_at')
          .eq('user_id', userId)
          .or('category.is.null,category.eq.other')
          .gte('created_at', dateRange.start)
          .lte('created_at', dateRange.end)
          .limit(100);

        if (error) {
          console.warn('⚠️ Error fetching uncategorized transactions:', error);
        } else {
          uncategorizedTx = data || [];
        }
      } catch (error) {
        console.warn('⚠️ Failed to fetch uncategorized transactions:', error);
      }

      // Get low-confidence transactions with error handling
      let lowConfidenceTx = [];
      try {
        const { data, error } = await supabase
          .from('transactions')
          .select('id, amount, description, merchant_name, category, category_confidence, created_at')
          .eq('user_id', userId)
          .not('category', 'is', null)
          .lt('category_confidence', 0.7)
          .gte('created_at', dateRange.start)
          .lte('created_at', dateRange.end)
          .limit(50);

        if (error) {
          console.warn('⚠️ Error fetching low-confidence transactions:', error);
        } else {
          lowConfidenceTx = data || [];
        }
      } catch (error) {
        console.warn('⚠️ Failed to fetch low-confidence transactions:', error);
      }

      // Get category distribution with error handling
      let spendingData = { categories: [] };
      try {
        spendingData = await this.getSpendingByCategory(userId, dateRange);
        if (!spendingData || typeof spendingData !== 'object') {
          spendingData = { categories: [] };
        }
      } catch (error) {
        console.warn('⚠️ Failed to get spending data:', error);
      }

      // Calculate insights with safe array operations
      const safeUncategorized = Array.isArray(uncategorizedTx) ? uncategorizedTx : [];
      const safeLowConfidence = Array.isArray(lowConfidenceTx) ? lowConfidenceTx : [];
      const safeCategories = Array.isArray(spendingData.categories) ? spendingData.categories : [];

      const insights = {
        uncategorizedTransactions: {
          count: safeUncategorized.length,
          totalAmount: safeUncategorized.reduce((sum, tx) => {
            const amount = tx && typeof tx.amount === 'number' ? Math.abs(tx.amount) : 0;
            return sum + amount;
          }, 0),
          transactions: safeUncategorized
        },
        lowConfidenceTransactions: {
          count: safeLowConfidence.length,
          totalAmount: safeLowConfidence.reduce((sum, tx) => {
            const amount = tx && typeof tx.amount === 'number' ? Math.abs(tx.amount) : 0;
            return sum + amount;
          }, 0),
          transactions: safeLowConfidence
        },
        categoryDistribution: safeCategories,
        recommendations: [],
        automationOpportunities: []
      };

      // Generate recommendations
      if (insights.uncategorizedTransactions.count > 0) {
        insights.recommendations.push({
          type: 'uncategorized',
          priority: 'high',
          title: 'Categorize Transactions',
          description: `You have ${insights.uncategorizedTransactions.count} uncategorized transactions worth ${insights.uncategorizedTransactions.totalAmount.toLocaleString()} UGX`,
          action: 'categorize_bulk',
          data: { transactionIds: safeUncategorized.map(tx => tx.id).filter(Boolean) }
        });
      }

      if (insights.lowConfidenceTransactions.count > 0) {
        insights.recommendations.push({
          type: 'low_confidence',
          priority: 'medium',
          title: 'Review Categories',
          description: `${insights.lowConfidenceTransactions.count} transactions may be incorrectly categorized`,
          action: 'review_categories',
          data: { transactionIds: safeLowConfidence.map(tx => tx.id).filter(Boolean) }
        });
      }

      // Find automation opportunities with safe operations
      const merchantPatterns = {};
      safeUncategorized.forEach(tx => {
        if (tx && tx.merchant_name && typeof tx.merchant_name === 'string') {
          const merchant = tx.merchant_name.toLowerCase();
          if (!merchantPatterns[merchant]) {
            merchantPatterns[merchant] = [];
          }
          merchantPatterns[merchant].push(tx);
        }
      });

      Object.entries(merchantPatterns).forEach(([merchant, transactions]) => {
        if (Array.isArray(transactions) && transactions.length >= 3) {
          const totalAmount = transactions.reduce((sum, tx) => {
            const amount = tx && typeof tx.amount === 'number' ? Math.abs(tx.amount) : 0;
            return sum + amount;
          }, 0);

          insights.automationOpportunities.push({
            type: 'merchant_pattern',
            merchant,
            transactionCount: transactions.length,
            totalAmount,
            suggestion: 'Create automatic categorization rule',
            confidence: 0.8
          });
        }
      });

      console.log('✅ Categorization insights generated');
      return insights;
    } catch (error) {
      console.error('❌ Error getting categorization insights:', error);
      return {
        uncategorizedTransactions: { count: 0, totalAmount: 0, transactions: [] },
        lowConfidenceTransactions: { count: 0, totalAmount: 0, transactions: [] },
        categoryDistribution: [],
        recommendations: [],
        automationOpportunities: []
      };
    }
  }

  /**
   * Get monthly trends
   */
  async getMonthlyTrends(userId) {
    try {
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, created_at')
        .eq('user_id', userId)
        .gte('created_at', sixMonthsAgo.toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;

      const monthlyData = {};
      transactions.forEach(tx => {
        const month = new Date(tx.created_at).toISOString().slice(0, 7); // YYYY-MM
        if (!monthlyData[month]) {
          monthlyData[month] = { income: 0, expenses: 0 };
        }

        if (tx.amount > 0) {
          monthlyData[month].income += tx.amount;
        } else {
          monthlyData[month].expenses += Math.abs(tx.amount);
        }
      });

      return Object.entries(monthlyData)
        .map(([month, data]) => ({
          month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          ...data,
          net: data.income - data.expenses
        }))
        .slice(-6); // Last 6 months
    } catch (error) {
      console.error('❌ Error getting monthly trends:', error);
      return [];
    }
  }

  /**
   * Group transactions by day
   */
  groupTransactionsByDay(transactions) {
    const dailyData = {};
    transactions.forEach(tx => {
      const day = new Date(tx.created_at).toISOString().slice(0, 10); // YYYY-MM-DD
      if (!dailyData[day]) {
        dailyData[day] = { count: 0, amount: 0 };
      }
      dailyData[day].count += 1;
      dailyData[day].amount += Math.abs(tx.amount);
    });
    return dailyData;
  }

  /**
   * Calculate goal progress
   */
  calculateGoalProgress(accounts) {
    if (!accounts || !Array.isArray(accounts)) {
      console.warn('⚠️ Invalid accounts data for goal progress calculation');
      return {
        totalGoals: 0,
        averageProgress: 0,
        goals: []
      };
    }

    const goalsWithProgress = accounts
      .filter(acc => acc && acc.target_amount && acc.target_amount > 0)
      .map(acc => ({
        accountId: acc.id,
        accountName: acc.account_name || acc.name || 'Unnamed Account',
        currentAmount: acc.current_balance || acc.balance || 0,
        targetAmount: acc.target_amount,
        progress: ((acc.current_balance || acc.balance || 0) / acc.target_amount) * 100,
        targetDate: acc.target_date
      }));

    return {
      totalGoals: goalsWithProgress.length,
      averageProgress: goalsWithProgress.length > 0
        ? goalsWithProgress.reduce((sum, goal) => sum + (goal?.progress || 0), 0) / goalsWithProgress.length
        : 0,
      goals: goalsWithProgress
    };
  }

  /**
   * Group accounts by type
   */
  groupAccountsByType(accounts) {
    if (!accounts || !Array.isArray(accounts)) {
      console.warn('⚠️ Invalid accounts data for grouping by type');
      return {};
    }

    const types = {};
    accounts.forEach(acc => {
      if (!acc) return;

      const type = acc.account_type || acc.type || 'general';
      if (!types[type]) {
        types[type] = { count: 0, totalBalance: 0 };
      }
      types[type].count += 1;
      types[type].totalBalance += acc.current_balance || acc.balance || 0;
    });
    return types;
  }

  /**
   * Calculate monthly savings growth
   */
  calculateMonthlySavingsGrowth(transactions) {
    if (!transactions || !Array.isArray(transactions)) {
      console.warn('⚠️ Invalid transactions data for monthly growth calculation');
      return [];
    }

    const monthlyGrowth = {};
    transactions.forEach(tx => {
      if (!tx || !tx.created_at) return;

      try {
        const month = new Date(tx.created_at).toISOString().slice(0, 7);
        if (!monthlyGrowth[month]) {
          monthlyGrowth[month] = 0;
        }

        if (tx.transaction_type === 'deposit') {
          monthlyGrowth[month] += tx.amount || 0;
        } else if (tx.transaction_type === 'withdrawal') {
          monthlyGrowth[month] -= Math.abs(tx.amount || 0);
        }
      } catch (error) {
        console.warn('⚠️ Error processing transaction for monthly growth:', error);
      }
    });

    return Object.entries(monthlyGrowth)
      .map(([month, growth]) => ({
        month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        growth
      }))
      .slice(-6);
  }

  /**
   * Calculate investment performance
   */
  calculateInvestmentPerformance(portfolios) {
    if (!portfolios || !Array.isArray(portfolios) || portfolios.length === 0) {
      console.warn('⚠️ Invalid portfolios data for performance calculation');
      return {
        totalReturn: 0,
        bestPerformer: null,
        worstPerformer: null
      };
    }

    const safePortfolios = Array.isArray(portfolios) ? portfolios : [];
    const totalValue = safePortfolios.reduce((sum, p) => sum + (p.current_value || p.value || 0), 0);
    const totalInvested = safePortfolios.reduce((sum, p) => sum + (p.total_invested || p.invested || 0), 0);
    const totalReturn = totalInvested > 0 ? ((totalValue - totalInvested) / totalInvested) * 100 : 0;

    return {
      totalReturn,
      bestPerformer: safePortfolios.length > 0 ? safePortfolios.reduce((best, current) =>
        (current.total_return || current.return || 0) > (best.total_return || best.return || 0) ? current : best, safePortfolios[0]) : null,
      worstPerformer: safePortfolios.length > 0 ? safePortfolios.reduce((worst, current) =>
        (current.total_return || current.return || 0) < (worst.total_return || worst.return || 0) ? current : worst, safePortfolios[0]) : null
    };
  }

  /**
   * Calculate asset allocation
   */
  calculateAssetAllocation(portfolios) {
    if (!portfolios || !Array.isArray(portfolios)) {
      console.warn('⚠️ Invalid portfolios data for asset allocation calculation');
      return [];
    }

    const allocation = {};
    const safePortfolios = Array.isArray(portfolios) ? portfolios : [];
    const totalValue = safePortfolios.reduce((sum, p) => sum + (p.current_value || p.value || 0), 0);

    safePortfolios.forEach(portfolio => {
      if (!portfolio) return;

      const type = portfolio.portfolio_type || portfolio.type || 'mixed';
      const value = portfolio.current_value || portfolio.value || 0;
      allocation[type] = (allocation[type] || 0) + value;
    });

    return Object.entries(allocation).map(([type, value]) => ({
      type,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0
    }));
  }

  /**
   * Calculate risk metrics
   */
  calculateRiskMetrics(portfolios) {
    if (!portfolios || !Array.isArray(portfolios) || portfolios.length === 0) {
      console.warn('⚠️ Invalid portfolios data for risk metrics calculation');
      return {
        averageRiskScore: 0,
        riskLevel: 'unknown',
        portfolioCount: 0
      };
    }

    const safePortfolios = Array.isArray(portfolios) ? portfolios : [];
    const riskScores = safePortfolios
      .filter(p => p && typeof p.risk_score === 'number')
      .map(p => p.risk_score);

    if (riskScores.length === 0) {
      return {
        averageRiskScore: 0,
        riskLevel: 'unknown',
        portfolioCount: safePortfolios.length
      };
    }

    const averageRisk = riskScores.reduce((sum, score) => sum + score, 0) / riskScores.length;

    return {
      averageRiskScore: averageRisk,
      riskLevel: averageRisk <= 3 ? 'conservative' : averageRisk <= 7 ? 'moderate' : 'aggressive',
      portfolioCount: safePortfolios.length
    };
  }

  /**
   * Calculate summary metrics
   */
  calculateSummaryMetrics({ wallet, transactions, savings, investments }) {
    return {
      totalNetWorth: (wallet.currentBalance || 0) + (savings.totalBalance || 0) + (investments.totalValue || 0),
      monthlyIncome: transactions.totalReceived || 0,
      monthlyExpenses: transactions.totalSpent || 0,
      monthlySavings: savings.netSavings || 0,
      savingsRate: transactions.totalReceived > 0
        ? ((savings.netSavings || 0) / transactions.totalReceived) * 100
        : 0,
      investmentReturn: investments.totalReturn || 0
    };
  }

  /**
   * Generate financial insights
   */
  generateFinancialInsights({ transactions, savings, spendingCategories }) {
    const insights = [];

    // Spending insights
    if (spendingCategories.length > 0) {
      const topCategory = spendingCategories[0];
      insights.push({
        type: 'spending',
        title: 'Top Spending Category',
        description: `You spent the most on ${topCategory.category}`,
        amount: topCategory.amount,
        icon: 'trending-down',
        color: '#E74C3C'
      });
    }

    // Savings insights
    if (savings.savingsRate > 20) {
      insights.push({
        type: 'savings',
        title: 'Great Savings Rate',
        description: `You're saving ${savings.savingsRate.toFixed(1)}% of your income`,
        amount: savings.netSavings,
        icon: 'trending-up',
        color: '#27AE60'
      });
    }

    // Transaction insights
    if (transactions.netFlow > 0) {
      insights.push({
        type: 'income',
        title: 'Positive Cash Flow',
        description: 'Your income exceeded expenses this period',
        amount: transactions.netFlow,
        icon: 'arrow-up',
        color: '#27AE60'
      });
    }

    return insights;
  }

  /**
   * Cleanup
   */
  async cleanup() {
    try {
      // Prevent multiple cleanup calls
      if (!this.isInitialized && !this.realTimeChannel) {
        return;
      }

      console.log('🧹 Cleaning up real-time analytics...');

      if (this.realTimeChannel) {
        try {
          // Unsubscribe from the channel
          await this.realTimeChannel.unsubscribe();
        } catch (unsubError) {
          console.warn('⚠️ Error unsubscribing from channel:', unsubError);
        }

        try {
          // Remove the channel
          supabase.removeChannel(this.realTimeChannel);
        } catch (removeError) {
          console.warn('⚠️ Error removing channel:', removeError);
        }

        this.realTimeChannel = null;
      }

      this.cache.clear();
      this.subscribers.clear();
      this.isInitialized = false;
      this.currentUserId = null;

      console.log('✅ Real-time analytics cleanup completed');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}

export default new EnhancedAnalyticsService();
