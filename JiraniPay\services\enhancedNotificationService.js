/**
 * Enhanced Notification Service
 * Comprehensive notification system with real-time push notifications, SMS alerts,
 * email notifications, rich formatting, and delivery tracking
 */

import { supabase } from './supabaseClient';
import notificationService from './notificationService';
import communicationService from './communicationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatRelativeTime } from '../utils/dateUtils';

// Notification types and their configurations
const NOTIFICATION_TYPES = {
  transaction_completed: {
    priority: 'high',
    channels: ['push', 'sms'],
    template: 'transaction_success',
    retryAttempts: 3,
    expiry: 24 * 60 * 60 * 1000 // 24 hours
  },
  transaction_failed: {
    priority: 'high',
    channels: ['push', 'sms'],
    template: 'transaction_failed',
    retryAttempts: 3,
    expiry: 24 * 60 * 60 * 1000
  },
  money_received: {
    priority: 'high',
    channels: ['push', 'sms'],
    template: 'money_received',
    retryAttempts: 2,
    expiry: 12 * 60 * 60 * 1000 // 12 hours
  },
  bill_payment_success: {
    priority: 'medium',
    channels: ['push'],
    template: 'bill_payment',
    retryAttempts: 2,
    expiry: 12 * 60 * 60 * 1000
  },
  security_alert: {
    priority: 'critical',
    channels: ['push', 'sms', 'email'],
    template: 'security_alert',
    retryAttempts: 5,
    expiry: 7 * 24 * 60 * 60 * 1000 // 7 days
  },
  fraud_alert: {
    priority: 'critical',
    channels: ['push', 'sms', 'email'],
    template: 'fraud_alert',
    retryAttempts: 5,
    expiry: 7 * 24 * 60 * 60 * 1000
  },
  limit_warning: {
    priority: 'medium',
    channels: ['push'],
    template: 'limit_warning',
    retryAttempts: 1,
    expiry: 6 * 60 * 60 * 1000 // 6 hours
  },
  account_verification: {
    priority: 'high',
    channels: ['push', 'email'],
    template: 'verification_update',
    retryAttempts: 3,
    expiry: 24 * 60 * 60 * 1000
  },
  promotional: {
    priority: 'low',
    channels: ['push'],
    template: 'promotional',
    retryAttempts: 1,
    expiry: 3 * 24 * 60 * 60 * 1000 // 3 days
  }
};

// Delivery status types
const DELIVERY_STATUS = {
  PENDING: 'pending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  EXPIRED: 'expired',
  RETRYING: 'retrying'
};

class EnhancedNotificationService {
  constructor() {
    this.notificationTypes = NOTIFICATION_TYPES;
    this.deliveryStatus = DELIVERY_STATUS;
    this.retryQueue = new Map();
    this.deliveryTracking = new Map();
  }

  /**
   * Send enhanced notification with delivery tracking
   */
  async sendNotification(userId, notificationData) {
    try {
      console.log('📨 Sending enhanced notification:', { 
        userId, 
        type: notificationData.type 
      });

      // Get notification configuration
      const config = this.notificationTypes[notificationData.type] || 
                    this.notificationTypes.transaction_completed;

      // Get user preferences
      const userPreferences = await this.getUserNotificationPreferences(userId);
      
      // Filter channels based on user preferences
      const enabledChannels = this.filterChannelsByPreferences(
        config.channels, 
        userPreferences, 
        notificationData.type
      );

      if (enabledChannels.length === 0) {
        console.log('⚠️ No enabled channels for notification type:', notificationData.type);
        return { success: true, skipped: true, reason: 'No enabled channels' };
      }

      // Create notification record
      const notificationRecord = await this.createNotificationRecord(
        userId, 
        notificationData, 
        config, 
        enabledChannels
      );

      // Send through each enabled channel
      const deliveryResults = await Promise.allSettled(
        enabledChannels.map(channel => 
          this.sendThroughChannel(
            channel, 
            userId, 
            notificationData, 
            notificationRecord.id
          )
        )
      );

      // Update delivery status
      await this.updateDeliveryStatus(notificationRecord.id, deliveryResults);

      // Schedule retries for failed deliveries
      await this.scheduleRetries(notificationRecord.id, deliveryResults, config);

      console.log('✅ Enhanced notification sent:', {
        notificationId: notificationRecord.id,
        channels: enabledChannels,
        results: deliveryResults.map(r => r.status)
      });

      return {
        success: true,
        notificationId: notificationRecord.id,
        channels: enabledChannels,
        deliveryResults
      };
    } catch (error) {
      console.error('❌ Error sending enhanced notification:', error);
      throw error;
    }
  }

  /**
   * Send notification through specific channel
   */
  async sendThroughChannel(channel, userId, notificationData, notificationId) {
    try {
      console.log(`📤 Sending via ${channel}:`, { userId, type: notificationData.type });

      switch (channel) {
        case 'push':
          return await this.sendPushNotification(userId, notificationData, notificationId);
        
        case 'sms':
          return await this.sendSMSNotification(userId, notificationData, notificationId);
        
        case 'email':
          return await this.sendEmailNotification(userId, notificationData, notificationId);
        
        default:
          throw new Error(`Unsupported channel: ${channel}`);
      }
    } catch (error) {
      console.error(`❌ Error sending via ${channel}:`, error);
      return {
        channel,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Send push notification
   */
  async sendPushNotification(userId, notificationData, notificationId) {
    try {
      // Get formatted content
      const content = await this.formatNotificationContent(
        notificationData, 
        'push'
      );

      // Send via existing notification service
      await notificationService.scheduleTransactionNotification({
        id: notificationId,
        type: notificationData.type,
        title: content.title,
        description: content.body,
        amount: notificationData.amount,
        data: {
          notificationId,
          type: notificationData.type,
          ...notificationData.data
        }
      });

      // Log delivery attempt
      await this.logDeliveryAttempt(notificationId, 'push', 'sent');

      return {
        channel: 'push',
        success: true,
        messageId: `push_${notificationId}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      await this.logDeliveryAttempt(notificationId, 'push', 'failed', error.message);
      throw error;
    }
  }

  /**
   * Send SMS notification
   */
  async sendSMSNotification(userId, notificationData, notificationId) {
    try {
      // Get user phone number
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('phone_number')
        .eq('user_id', userId)
        .single();

      if (error || !profile.phone_number) {
        throw new Error('User phone number not found');
      }

      // Get formatted content
      const content = await this.formatNotificationContent(
        notificationData, 
        'sms'
      );

      // Send via communication service
      const result = await communicationService.sendSMSMessage(
        profile.phone_number,
        content.message,
        notificationData.type
      );

      // Log delivery attempt
      const status = result.success ? 'sent' : 'failed';
      await this.logDeliveryAttempt(
        notificationId, 
        'sms', 
        status, 
        result.error
      );

      return {
        channel: 'sms',
        success: result.success,
        messageId: result.messageId,
        timestamp: new Date().toISOString(),
        error: result.error
      };
    } catch (error) {
      await this.logDeliveryAttempt(notificationId, 'sms', 'failed', error.message);
      throw error;
    }
  }

  /**
   * Send email notification
   */
  async sendEmailNotification(userId, notificationData, notificationId) {
    try {
      // Get user email
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('email, full_name')
        .eq('user_id', userId)
        .single();

      if (error || !profile.email) {
        throw new Error('User email not found');
      }

      // Get formatted content
      const content = await this.formatNotificationContent(
        notificationData, 
        'email',
        { userName: profile.full_name }
      );

      // Send via communication service
      const result = await communicationService.sendEmail({
        to: profile.email,
        subject: content.subject,
        html: content.html,
        text: content.text
      });

      // Log delivery attempt
      const status = result.success ? 'sent' : 'failed';
      await this.logDeliveryAttempt(
        notificationId, 
        'email', 
        status, 
        result.error
      );

      return {
        channel: 'email',
        success: result.success,
        messageId: result.messageId,
        timestamp: new Date().toISOString(),
        error: result.error
      };
    } catch (error) {
      await this.logDeliveryAttempt(notificationId, 'email', 'failed', error.message);
      throw error;
    }
  }

  /**
   * Create notification record in database
   */
  async createNotificationRecord(userId, notificationData, config, channels) {
    try {
      const record = {
        user_id: userId,
        type: notificationData.type,
        title: notificationData.title,
        content: notificationData.content || notificationData.message,
        data: notificationData.data || {},
        channels: channels,
        priority: config.priority,
        status: this.deliveryStatus.PENDING,
        retry_count: 0,
        max_retries: config.retryAttempts,
        expires_at: new Date(Date.now() + config.expiry).toISOString(),
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('notifications')
        .insert(record)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('❌ Error creating notification record:', error);
      throw error;
    }
  }

  /**
   * Get user notification preferences
   */
  async getUserNotificationPreferences(userId) {
    try {
      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // Return default preferences if none found
      return preferences || {
        push_enabled: true,
        sms_enabled: true,
        email_enabled: true,
        transaction_notifications: true,
        security_alerts: true,
        promotional_notifications: false,
        fraud_alerts: true,
        limit_warnings: true
      };
    } catch (error) {
      console.error('❌ Error getting user preferences:', error);
      // Return safe defaults
      return {
        push_enabled: true,
        sms_enabled: false,
        email_enabled: false,
        transaction_notifications: true,
        security_alerts: true,
        promotional_notifications: false,
        fraud_alerts: true,
        limit_warnings: true
      };
    }
  }

  /**
   * Filter channels based on user preferences
   */
  filterChannelsByPreferences(channels, preferences, notificationType) {
    return channels.filter(channel => {
      // Check if channel is globally enabled
      const channelEnabled = preferences[`${channel}_enabled`];
      if (!channelEnabled) return false;

      // Check if notification type is enabled
      const typeKey = this.getPreferenceKeyForType(notificationType);
      const typeEnabled = preferences[typeKey];
      
      // Always allow critical security notifications
      if (notificationType === 'security_alert' || notificationType === 'fraud_alert') {
        return true;
      }

      return typeEnabled !== false;
    });
  }

  /**
   * Get preference key for notification type
   */
  getPreferenceKeyForType(notificationType) {
    const typeMap = {
      transaction_completed: 'transaction_notifications',
      transaction_failed: 'transaction_notifications',
      money_received: 'transaction_notifications',
      bill_payment_success: 'transaction_notifications',
      security_alert: 'security_alerts',
      fraud_alert: 'fraud_alerts',
      limit_warning: 'limit_warnings',
      account_verification: 'security_alerts',
      promotional: 'promotional_notifications'
    };

    return typeMap[notificationType] || 'transaction_notifications';
  }

  /**
   * Format notification content for specific channel
   */
  async formatNotificationContent(notificationData, channel, context = {}) {
    // This will be implemented with the template engine
    // For now, return basic formatting
    const { type, title, content, amount, data } = notificationData;

    switch (channel) {
      case 'push':
        return {
          title: title || this.getDefaultTitle(type),
          body: content || this.getDefaultMessage(type, { amount, ...data })
        };

      case 'sms':
        return {
          message: `JiraniPay: ${content || this.getDefaultMessage(type, { amount, ...data })}`
        };

      case 'email':
        return {
          subject: `JiraniPay - ${title || this.getDefaultTitle(type)}`,
          html: this.getDefaultEmailHTML(type, { amount, ...data, ...context }),
          text: content || this.getDefaultMessage(type, { amount, ...data })
        };

      default:
        return { message: content };
    }
  }

  /**
   * Get default title for notification type
   */
  getDefaultTitle(type) {
    const titles = {
      transaction_completed: 'Transaction Successful',
      transaction_failed: 'Transaction Failed',
      money_received: 'Money Received',
      bill_payment_success: 'Bill Payment Successful',
      security_alert: 'Security Alert',
      fraud_alert: 'Fraud Alert',
      limit_warning: 'Transaction Limit Warning',
      account_verification: 'Account Verification Update',
      promotional: 'Special Offer'
    };

    return titles[type] || 'JiraniPay Notification';
  }

  /**
   * Get default message for notification type
   */
  getDefaultMessage(type, data) {
    const { amount, recipient, reference } = data;

    switch (type) {
      case 'transaction_completed':
        return `Your transaction of ${formatCurrency(amount)} was successful. Ref: ${reference}`;
      
      case 'money_received':
        return `You received ${formatCurrency(amount)} from ${recipient}. Ref: ${reference}`;
      
      case 'security_alert':
        return 'Suspicious activity detected on your account. Please review your recent transactions.';
      
      default:
        return 'You have a new notification from JiraniPay.';
    }
  }

  /**
   * Get default email HTML template
   */
  getDefaultEmailHTML(type, data) {
    const { amount, recipient, reference, userName } = data;

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #1a73e8; color: white; padding: 20px; text-align: center;">
          <h1>JiraniPay</h1>
        </div>
        <div style="padding: 20px;">
          <p>Hello ${userName || 'Valued Customer'},</p>
          <p>${this.getDefaultMessage(type, data)}</p>
          <p>Thank you for using JiraniPay!</p>
        </div>
        <div style="background: #f5f5f5; padding: 10px; text-align: center; font-size: 12px;">
          <p>This is an automated message from JiraniPay. Please do not reply to this email.</p>
        </div>
      </div>
    `;
  }

  /**
   * Log delivery attempt
   */
  async logDeliveryAttempt(notificationId, channel, status, error = null) {
    try {
      await supabase
        .from('notification_delivery_logs')
        .insert({
          notification_id: notificationId,
          channel,
          status,
          error_message: error,
          attempted_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging delivery attempt:', error);
      // Don't throw - logging failure shouldn't stop notification
    }
  }

  /**
   * Update delivery status
   */
  async updateDeliveryStatus(notificationId, deliveryResults) {
    try {
      const hasSuccess = deliveryResults.some(r => 
        r.status === 'fulfilled' && r.value.success
      );
      const allFailed = deliveryResults.every(r => 
        r.status === 'rejected' || !r.value.success
      );

      const status = hasSuccess ? 
        this.deliveryStatus.SENT : 
        (allFailed ? this.deliveryStatus.FAILED : this.deliveryStatus.PENDING);

      await supabase
        .from('notifications')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId);
    } catch (error) {
      console.error('❌ Error updating delivery status:', error);
    }
  }

  /**
   * Schedule retries for failed deliveries
   */
  async scheduleRetries(notificationId, deliveryResults, config) {
    try {
      const failedChannels = deliveryResults
        .filter(r => r.status === 'rejected' || !r.value.success)
        .map(r => r.value?.channel || 'unknown');

      if (failedChannels.length > 0) {
        // Add to retry queue (implement retry logic as needed)
        this.retryQueue.set(notificationId, {
          channels: failedChannels,
          retryCount: 0,
          maxRetries: config.retryAttempts,
          nextRetry: Date.now() + (5 * 60 * 1000) // 5 minutes
        });

        console.log('📋 Scheduled retries for notification:', {
          notificationId,
          failedChannels,
          maxRetries: config.retryAttempts
        });
      }
    } catch (error) {
      console.error('❌ Error scheduling retries:', error);
    }
  }
}

export default new EnhancedNotificationService();
