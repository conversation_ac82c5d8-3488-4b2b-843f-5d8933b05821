/**
 * Financial Planning Service
 * Comprehensive service for budget management, goal tracking,
 * financial recommendations, and planning algorithms
 */

import { supabase } from './supabaseClient';
import savingsAccountService from './savingsAccountService';
import investmentPortfolioService from './investmentPortfolioService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { isValidUUID, requireAuthentication } from '../utils/userUtils';

class FinancialPlanningService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 10 * 60 * 1000; // 10 minutes
    this.recommendationEngine = new FinancialRecommendationEngine();
  }

  /**
   * Create a new budget
   */
  async createBudget(userId, budgetData) {
    try {
      console.log('💰 Creating budget:', { userId, budgetData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Validate budget data
      const validation = this.validateBudgetData(budgetData);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      // Create budget
      const { data: budget, error } = await supabase
        .from('budgets')
        .insert({
          user_id: userId,
          budget_name: budgetData.budgetName,
          budget_period: budgetData.budgetPeriod || 'monthly',
          currency: budgetData.currency || 'UGX',
          total_income: budgetData.totalIncome || 0,
          total_expenses: budgetData.totalExpenses || 0,
          total_savings: budgetData.totalSavings || 0,
          total_investments: budgetData.totalInvestments || 0,
          start_date: budgetData.startDate,
          end_date: budgetData.endDate,
          description: budgetData.description,
          metadata: {
            created_via: 'mobile_app',
            budget_strategy: budgetData.budgetStrategy || '50-30-20'
          }
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating budget:', error);
        return { success: false, error: 'Failed to create budget' };
      }

      // Create budget categories if provided
      if (budgetData.categories && budgetData.categories.length > 0) {
        await this.createBudgetCategories(budget.id, userId, budgetData.categories);
      }

      // Clear cache
      this.clearUserCache(userId);

      // Send confirmation notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'budget_created',
          title: 'Budget Created',
          content: `Your ${budgetData.budgetName} budget has been created successfully!`,
          data: {
            budgetId: budget.id,
            budgetName: budgetData.budgetName,
            budgetPeriod: budgetData.budgetPeriod
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send budget creation notification:', notificationError);
      }

      console.log('✅ Budget created:', budget.id);

      return {
        success: true,
        budget: this.formatBudgetResponse(budget)
      };
    } catch (error) {
      console.error('❌ Error creating budget:', error);
      return { success: false, error: 'Failed to create budget' };
    }
  }

  /**
   * Get user's budgets
   */
  async getUserBudgets(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Check cache first
      const cacheKey = this.getCacheKey(userId, 'budgets', options);
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        return { success: true, budgets: cached };
      }

      console.log('💰 Fetching budgets for user:', userId);

      let query = supabase
        .from('budgets')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options.budgetPeriod) {
        query = query.eq('budget_period', options.budgetPeriod);
      }
      if (options.isActive !== undefined) {
        query = query.eq('is_active', options.isActive);
      }

      const { data: budgets, error } = await query;

      if (error) {
        console.error('❌ Error fetching budgets:', error);
        return { success: false, error: 'Failed to fetch budgets' };
      }

      // Get categories for each budget
      const budgetsWithCategories = await Promise.all(
        budgets.map(async (budget) => {
          const categoriesResult = await this.getBudgetCategories(budget.id);
          return {
            ...this.formatBudgetResponse(budget),
            categories: categoriesResult.success ? categoriesResult.categories : [],
            categoriesCount: categoriesResult.success ? categoriesResult.categories.length : 0
          };
        })
      );

      // Cache the result
      this.setCachedData(cacheKey, budgetsWithCategories);

      console.log(`✅ Found ${budgetsWithCategories.length} budgets`);

      return {
        success: true,
        budgets: budgetsWithCategories
      };
    } catch (error) {
      console.error('❌ Error getting user budgets:', error);
      return { success: false, error: 'Failed to fetch budgets' };
    }
  }

  /**
   * Create financial goal
   */
  async createFinancialGoal(userId, goalData) {
    try {
      console.log('🎯 Creating financial goal:', { userId, goalData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Validate goal data
      const validation = this.validateGoalData(goalData);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      // Calculate recommended monthly contribution
      const recommendedMonthly = this.calculateRecommendedMonthlyContribution(
        goalData.targetAmount,
        goalData.targetDate,
        goalData.currentAmount || 0
      );

      // Create goal
      const { data: goal, error } = await supabase
        .from('financial_goals')
        .insert({
          user_id: userId,
          goal_name: goalData.goalName,
          goal_type: goalData.goalType,
          description: goalData.description,
          target_amount: goalData.targetAmount,
          current_amount: goalData.currentAmount || 0,
          target_date: goalData.targetDate,
          monthly_contribution: goalData.monthlyContribution,
          recommended_monthly: recommendedMonthly,
          linked_savings_account: goalData.linkedSavingsAccount,
          linked_investment_portfolio: goalData.linkedInvestmentPortfolio,
          priority_level: goalData.priorityLevel || 3,
          metadata: {
            created_via: 'mobile_app',
            goal_strategy: goalData.goalStrategy
          }
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating goal:', error);
        return { success: false, error: 'Failed to create goal' };
      }

      // Clear cache
      this.clearUserCache(userId);

      // Send confirmation notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'goal_created',
          title: 'Financial Goal Created',
          content: `Your ${goalData.goalName} goal has been created! Target: ${formatCurrency(goalData.targetAmount, 'UGX')}`,
          data: {
            goalId: goal.id,
            goalName: goalData.goalName,
            targetAmount: goalData.targetAmount,
            recommendedMonthly
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send goal creation notification:', notificationError);
      }

      console.log('✅ Financial goal created:', goal.id);

      return {
        success: true,
        goal: this.formatGoalResponse(goal)
      };
    } catch (error) {
      console.error('❌ Error creating financial goal:', error);
      return { success: false, error: 'Failed to create goal' };
    }
  }

  /**
   * Get user's financial goals
   */
  async getUserFinancialGoals(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      console.log('🎯 Fetching financial goals for user:', userId);

      let query = supabase
        .from('financial_goals')
        .select('*')
        .eq('user_id', userId)
        .order('priority_level', { ascending: false });

      // Apply filters
      if (options.goalType) {
        query = query.eq('goal_type', options.goalType);
      }
      if (options.isActive !== undefined) {
        query = query.eq('is_active', options.isActive);
      }
      if (options.isAchieved !== undefined) {
        query = query.eq('is_achieved', options.isAchieved);
      }

      const { data: goals, error } = await query;

      if (error) {
        console.error('❌ Error fetching goals:', error);
        return { success: false, error: 'Failed to fetch goals' };
      }

      const formattedGoals = goals.map(goal => this.formatGoalResponse(goal));

      console.log(`✅ Found ${formattedGoals.length} financial goals`);

      return {
        success: true,
        goals: formattedGoals
      };
    } catch (error) {
      console.error('❌ Error getting user financial goals:', error);
      return { success: false, error: 'Failed to fetch goals' };
    }
  }

  /**
   * Generate financial recommendations
   */
  async generateFinancialRecommendations(userId) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      console.log('🤖 Generating financial recommendations for user:', userId);

      // Get user's financial data
      const [savingsResult, investmentResult, budgetsResult, goalsResult] = await Promise.all([
        savingsAccountService.getSavingsSummary(userId),
        investmentPortfolioService.getPortfolioSummary(userId),
        this.getUserBudgets(userId, { isActive: true }),
        this.getUserFinancialGoals(userId, { isActive: true })
      ]);

      const financialProfile = {
        savings: savingsResult.success ? savingsResult.summary : {},
        investments: investmentResult.success ? investmentResult.summary : {},
        budgets: budgetsResult.success ? budgetsResult.budgets : [],
        goals: goalsResult.success ? goalsResult.goals : []
      };

      // Generate recommendations using the recommendation engine
      const recommendations = await this.recommendationEngine.generateRecommendations(financialProfile);

      return {
        success: true,
        recommendations,
        financialProfile
      };
    } catch (error) {
      console.error('❌ Error generating recommendations:', error);
      return { success: false, error: 'Failed to generate recommendations' };
    }
  }

  /**
   * Calculate retirement planning
   */
  async calculateRetirementPlan(userId, retirementData) {
    try {
      const {
        currentAge,
        retirementAge,
        currentSavings,
        monthlyContribution,
        expectedReturn,
        inflationRate,
        desiredRetirementIncome
      } = retirementData;

      const yearsToRetirement = retirementAge - currentAge;
      const monthsToRetirement = yearsToRetirement * 12;
      const monthlyReturn = expectedReturn / 12;
      const monthlyInflation = inflationRate / 12;

      // Calculate future value of current savings
      const futureValueCurrentSavings = currentSavings * Math.pow(1 + expectedReturn, yearsToRetirement);

      // Calculate future value of monthly contributions
      const futureValueContributions = monthlyContribution * 
        ((Math.pow(1 + monthlyReturn, monthsToRetirement) - 1) / monthlyReturn);

      // Total retirement savings
      const totalRetirementSavings = futureValueCurrentSavings + futureValueContributions;

      // Calculate required savings for desired income
      const adjustedRetirementIncome = desiredRetirementIncome * Math.pow(1 + inflationRate, yearsToRetirement);
      const requiredSavings = adjustedRetirementIncome / 0.04; // 4% withdrawal rule

      // Calculate shortfall or surplus
      const shortfall = Math.max(0, requiredSavings - totalRetirementSavings);
      const surplus = Math.max(0, totalRetirementSavings - requiredSavings);

      // Calculate required monthly contribution to meet goal
      const requiredMonthlyContribution = shortfall > 0 ? 
        (shortfall - futureValueCurrentSavings) / 
        ((Math.pow(1 + monthlyReturn, monthsToRetirement) - 1) / monthlyReturn) : 0;

      return {
        success: true,
        plan: {
          yearsToRetirement,
          totalRetirementSavings,
          requiredSavings,
          shortfall,
          surplus,
          currentMonthlyContribution: monthlyContribution,
          requiredMonthlyContribution: Math.max(0, requiredMonthlyContribution),
          projectedMonthlyIncome: totalRetirementSavings * 0.04 / 12,
          inflationAdjustedIncome: adjustedRetirementIncome / 12
        }
      };
    } catch (error) {
      console.error('❌ Error calculating retirement plan:', error);
      return { success: false, error: 'Failed to calculate retirement plan' };
    }
  }

  /**
   * Calculate emergency fund recommendation
   */
  async calculateEmergencyFundRecommendation(userId, expenseData) {
    try {
      const { monthlyExpenses, currentEmergencyFund, riskTolerance } = expenseData;

      // Recommended emergency fund based on risk tolerance
      const multipliers = {
        conservative: 6, // 6 months of expenses
        moderate: 4,     // 4 months of expenses
        aggressive: 3    // 3 months of expenses
      };

      const multiplier = multipliers[riskTolerance] || 4;
      const recommendedAmount = monthlyExpenses * multiplier;
      const shortfall = Math.max(0, recommendedAmount - currentEmergencyFund);
      const surplus = Math.max(0, currentEmergencyFund - recommendedAmount);

      // Calculate monthly savings needed to reach goal in 12 months
      const monthlyTarget = shortfall / 12;

      return {
        success: true,
        recommendation: {
          recommendedAmount,
          currentAmount: currentEmergencyFund,
          shortfall,
          surplus,
          monthlyTarget,
          monthsOfExpensesCovered: currentEmergencyFund / monthlyExpenses,
          recommendedMonths: multiplier,
          riskTolerance
        }
      };
    } catch (error) {
      console.error('❌ Error calculating emergency fund recommendation:', error);
      return { success: false, error: 'Failed to calculate emergency fund recommendation' };
    }
  }

  /**
   * Utility Methods
   */

  validateBudgetData(data) {
    const errors = [];

    if (!data.budgetName || data.budgetName.trim().length < 2) {
      errors.push('Budget name must be at least 2 characters');
    }

    if (!data.startDate) {
      errors.push('Start date is required');
    }

    if (!data.endDate) {
      errors.push('End date is required');
    }

    if (data.startDate && data.endDate && new Date(data.startDate) >= new Date(data.endDate)) {
      errors.push('End date must be after start date');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateGoalData(data) {
    const errors = [];

    if (!data.goalName || data.goalName.trim().length < 2) {
      errors.push('Goal name must be at least 2 characters');
    }

    if (!data.targetAmount || data.targetAmount <= 0) {
      errors.push('Target amount must be greater than 0');
    }

    if (data.currentAmount && data.currentAmount < 0) {
      errors.push('Current amount cannot be negative');
    }

    if (data.targetDate && new Date(data.targetDate) <= new Date()) {
      errors.push('Target date must be in the future');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  calculateRecommendedMonthlyContribution(targetAmount, targetDate, currentAmount) {
    if (!targetDate) return 0;

    const now = new Date();
    const target = new Date(targetDate);
    const monthsRemaining = Math.max(1, Math.ceil((target - now) / (1000 * 60 * 60 * 24 * 30)));
    const remainingAmount = targetAmount - currentAmount;

    return Math.max(0, remainingAmount / monthsRemaining);
  }

  async createBudgetCategories(budgetId, userId, categories) {
    try {
      const categoryData = categories.map(category => ({
        budget_id: budgetId,
        user_id: userId,
        category_name: category.name,
        category_type: category.type,
        subcategory: category.subcategory,
        budgeted_amount: category.budgetedAmount || 0,
        is_fixed: category.isFixed || false,
        is_essential: category.isEssential || true,
        description: category.description,
        color: category.color,
        icon: category.icon
      }));

      await supabase.from('budget_categories').insert(categoryData);
    } catch (error) {
      console.error('❌ Error creating budget categories:', error);
    }
  }

  async getBudgetCategories(budgetId) {
    try {
      const { data: categories, error } = await supabase
        .from('budget_categories')
        .select('*')
        .eq('budget_id', budgetId)
        .order('category_type', { ascending: true });

      if (error) {
        console.error('❌ Error fetching budget categories:', error);
        return { success: false, error: 'Failed to fetch categories' };
      }

      return {
        success: true,
        categories: categories.map(c => this.formatCategoryResponse(c))
      };
    } catch (error) {
      console.error('❌ Error getting budget categories:', error);
      return { success: false, error: 'Failed to fetch categories' };
    }
  }

  formatBudgetResponse(budget) {
    return {
      id: budget.id,
      budgetName: budget.budget_name,
      budgetPeriod: budget.budget_period,
      currency: budget.currency,
      totalIncome: parseFloat(budget.total_income || 0),
      totalExpenses: parseFloat(budget.total_expenses || 0),
      totalSavings: parseFloat(budget.total_savings || 0),
      totalInvestments: parseFloat(budget.total_investments || 0),
      startDate: budget.start_date,
      endDate: budget.end_date,
      isActive: budget.is_active,
      isTemplate: budget.is_template,
      description: budget.description,
      metadata: budget.metadata,
      createdAt: budget.created_at,
      updatedAt: budget.updated_at
    };
  }

  formatGoalResponse(goal) {
    const progressPercentage = goal.target_amount > 0 ? 
      Math.min((goal.current_amount / goal.target_amount) * 100, 100) : 0;

    return {
      id: goal.id,
      goalName: goal.goal_name,
      goalType: goal.goal_type,
      description: goal.description,
      targetAmount: parseFloat(goal.target_amount || 0),
      currentAmount: parseFloat(goal.current_amount || 0),
      targetDate: goal.target_date,
      monthlyContribution: parseFloat(goal.monthly_contribution || 0),
      recommendedMonthly: parseFloat(goal.recommended_monthly || 0),
      progressPercentage,
      linkedSavingsAccount: goal.linked_savings_account,
      linkedInvestmentPortfolio: goal.linked_investment_portfolio,
      priorityLevel: goal.priority_level,
      isActive: goal.is_active,
      isAchieved: goal.is_achieved,
      achievedDate: goal.achieved_date,
      metadata: goal.metadata,
      createdAt: goal.created_at,
      updatedAt: goal.updated_at
    };
  }

  formatCategoryResponse(category) {
    const variance = category.budgeted_amount - category.actual_amount;
    const variancePercentage = category.budgeted_amount > 0 ? 
      (variance / category.budgeted_amount) * 100 : 0;

    return {
      id: category.id,
      budgetId: category.budget_id,
      categoryName: category.category_name,
      categoryType: category.category_type,
      subcategory: category.subcategory,
      budgetedAmount: parseFloat(category.budgeted_amount || 0),
      actualAmount: parseFloat(category.actual_amount || 0),
      variance,
      variancePercentage,
      isFixed: category.is_fixed,
      isEssential: category.is_essential,
      description: category.description,
      color: category.color,
      icon: category.icon,
      createdAt: category.created_at,
      updatedAt: category.updated_at
    };
  }

  /**
   * Cache Management
   */
  getCacheKey(userId, operation, params = {}) {
    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : '';
    return `financial_planning_${operation}_${userId}_${paramString}`;
  }

  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }
}

/**
 * Financial Recommendation Engine
 */
class FinancialRecommendationEngine {
  generateRecommendations(financialProfile) {
    const recommendations = [];

    // Emergency fund recommendations
    recommendations.push(...this.generateEmergencyFundRecommendations(financialProfile));

    // Savings recommendations
    recommendations.push(...this.generateSavingsRecommendations(financialProfile));

    // Investment recommendations
    recommendations.push(...this.generateInvestmentRecommendations(financialProfile));

    // Budget recommendations
    recommendations.push(...this.generateBudgetRecommendations(financialProfile));

    // Goal recommendations
    recommendations.push(...this.generateGoalRecommendations(financialProfile));

    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  generateEmergencyFundRecommendations(profile) {
    const recommendations = [];
    const totalSavings = profile.savings.totalSavings || 0;
    
    if (totalSavings < 100000) { // Less than 100k UGX emergency fund
      recommendations.push({
        type: 'emergency_fund',
        title: 'Build Your Emergency Fund',
        description: 'Start building an emergency fund to cover unexpected expenses',
        action: 'Create an emergency savings account',
        priority: 9,
        category: 'savings',
        impact: 'high'
      });
    }

    return recommendations;
  }

  generateSavingsRecommendations(profile) {
    const recommendations = [];
    const savingsAccounts = profile.savings.totalAccounts || 0;

    if (savingsAccounts === 0) {
      recommendations.push({
        type: 'first_savings',
        title: 'Start Your Savings Journey',
        description: 'Create your first savings account to begin building wealth',
        action: 'Create a savings account',
        priority: 8,
        category: 'savings',
        impact: 'high'
      });
    }

    return recommendations;
  }

  generateInvestmentRecommendations(profile) {
    const recommendations = [];
    const portfolios = profile.investments.totalPortfolios || 0;
    const totalSavings = profile.savings.totalSavings || 0;

    if (portfolios === 0 && totalSavings > 50000) {
      recommendations.push({
        type: 'start_investing',
        title: 'Start Investing for Growth',
        description: 'Begin investing to grow your wealth over time',
        action: 'Create an investment portfolio',
        priority: 7,
        category: 'investment',
        impact: 'medium'
      });
    }

    return recommendations;
  }

  generateBudgetRecommendations(profile) {
    const recommendations = [];
    const budgets = profile.budgets.length;

    if (budgets === 0) {
      recommendations.push({
        type: 'create_budget',
        title: 'Create Your First Budget',
        description: 'Track your income and expenses with a budget',
        action: 'Create a budget',
        priority: 6,
        category: 'budgeting',
        impact: 'medium'
      });
    }

    return recommendations;
  }

  generateGoalRecommendations(profile) {
    const recommendations = [];
    const goals = profile.goals.length;

    if (goals === 0) {
      recommendations.push({
        type: 'set_goals',
        title: 'Set Financial Goals',
        description: 'Define your financial objectives and track progress',
        action: 'Create financial goals',
        priority: 5,
        category: 'planning',
        impact: 'medium'
      });
    }

    return recommendations;
  }
}

export default new FinancialPlanningService();
