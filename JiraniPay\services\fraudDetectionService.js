/**
 * Fraud Detection Engine
 * Real-time fraud detection monitoring suspicious patterns, login attempts,
 * transaction anomalies, and velocity checks
 */

import { supabase } from './supabaseClient';
import { formatCurrency } from '../utils/currencyUtils';
import notificationService from './notificationService';
import securityAlertService from './securityAlertService';

// Fraud detection thresholds and rules
const FRAUD_RULES = {
  login: {
    maxFailedAttempts: 5,
    lockoutDuration: 30, // minutes
    suspiciousAttempts: 3
  },
  transaction: {
    velocityWindow: 5, // minutes
    maxVelocityCount: 10,
    unusualAmountMultiplier: 5, // 5x average
    maxDailyTransactions: 50
  },
  device: {
    newDeviceRiskScore: 30,
    unknownLocationRiskScore: 25
  },
  behavioral: {
    offHoursRiskScore: 15, // 10PM - 6AM
    weekendRiskScore: 10,
    rapidSuccessionThreshold: 3 // transactions within 1 minute
  }
};

class FraudDetectionService {
  constructor() {
    this.rules = FRAUD_RULES;
    this.riskThresholds = {
      low: 25,
      medium: 50,
      high: 75,
      critical: 90
    };
  }

  /**
   * Analyze transaction for fraud indicators
   */
  async analyzeTransaction(userId, transactionData) {
    try {
      console.log('🔍 Analyzing transaction for fraud:', { userId, amount: transactionData.amount });

      const riskFactors = [];
      let totalRiskScore = 0;

      // 1. Velocity Check
      const velocityRisk = await this.checkTransactionVelocity(userId);
      if (velocityRisk.isRisky) {
        riskFactors.push(velocityRisk);
        totalRiskScore += velocityRisk.score;
      }

      // 2. Amount Analysis
      const amountRisk = await this.analyzeTransactionAmount(userId, transactionData.amount);
      if (amountRisk.isRisky) {
        riskFactors.push(amountRisk);
        totalRiskScore += amountRisk.score;
      }

      // 3. Time-based Analysis
      const timeRisk = this.analyzeTransactionTiming();
      if (timeRisk.isRisky) {
        riskFactors.push(timeRisk);
        totalRiskScore += timeRisk.score;
      }

      // 4. Device Analysis
      const deviceRisk = await this.analyzeDevice(userId, transactionData.deviceInfo);
      if (deviceRisk.isRisky) {
        riskFactors.push(deviceRisk);
        totalRiskScore += deviceRisk.score;
      }

      // 5. Behavioral Pattern Analysis
      const behaviorRisk = await this.analyzeBehavioralPatterns(userId, transactionData);
      if (behaviorRisk.isRisky) {
        riskFactors.push(behaviorRisk);
        totalRiskScore += behaviorRisk.score;
      }

      const riskLevel = this.calculateRiskLevel(totalRiskScore);
      
      const analysis = {
        riskScore: totalRiskScore,
        riskLevel,
        riskFactors,
        recommendation: this.getRecommendation(riskLevel, riskFactors),
        timestamp: new Date().toISOString()
      };

      // Log the analysis
      await this.logFraudAnalysis(userId, transactionData, analysis);

      // Trigger alerts if high risk
      if (riskLevel === 'high' || riskLevel === 'critical') {
        await this.triggerFraudAlert(userId, analysis);
      }

      console.log('✅ Fraud analysis completed:', { riskScore: totalRiskScore, riskLevel });
      
      return analysis;
    } catch (error) {
      console.error('❌ Error in fraud analysis:', error);
      throw error;
    }
  }

  /**
   * Check transaction velocity (frequency)
   */
  async checkTransactionVelocity(userId) {
    try {
      const windowStart = new Date();
      windowStart.setMinutes(windowStart.getMinutes() - this.rules.transaction.velocityWindow);

      const { data: recentTransactions, error } = await supabase
        .from('transactions')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', windowStart.toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      const transactionCount = recentTransactions.length;
      const isRisky = transactionCount >= this.rules.transaction.maxVelocityCount;

      return {
        type: 'velocity',
        isRisky,
        score: isRisky ? 40 : 0,
        details: {
          transactionCount,
          timeWindow: this.rules.transaction.velocityWindow,
          threshold: this.rules.transaction.maxVelocityCount
        },
        message: isRisky ? 
          `${transactionCount} transactions in ${this.rules.transaction.velocityWindow} minutes` : 
          'Normal transaction velocity'
      };
    } catch (error) {
      console.error('❌ Error checking velocity:', error);
      return { type: 'velocity', isRisky: false, score: 0, error: error.message };
    }
  }

  /**
   * Analyze transaction amount for anomalies
   */
  async analyzeTransactionAmount(userId, amount) {
    try {
      // Get user's transaction history for baseline
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      if (transactions.length < 5) {
        // Not enough history, use conservative approach
        return {
          type: 'amount',
          isRisky: amount > 1000000, // UGX 1M
          score: amount > 1000000 ? 20 : 0,
          details: { reason: 'insufficient_history', amount },
          message: 'Insufficient transaction history for analysis'
        };
      }

      const amounts = transactions.map(tx => parseFloat(tx.amount));
      const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
      const maxAmount = Math.max(...amounts);

      const isUnusuallyHigh = amount > (avgAmount * this.rules.transaction.unusualAmountMultiplier);
      const isNewMaximum = amount > maxAmount;

      let riskScore = 0;
      if (isUnusuallyHigh) riskScore += 30;
      if (isNewMaximum) riskScore += 20;

      return {
        type: 'amount',
        isRisky: riskScore > 0,
        score: riskScore,
        details: {
          amount,
          avgAmount,
          maxAmount,
          multiplier: amount / avgAmount,
          isUnusuallyHigh,
          isNewMaximum
        },
        message: riskScore > 0 ? 
          `Amount ${formatCurrency(amount)} is ${(amount/avgAmount).toFixed(1)}x average` : 
          'Amount within normal range'
      };
    } catch (error) {
      console.error('❌ Error analyzing amount:', error);
      return { type: 'amount', isRisky: false, score: 0, error: error.message };
    }
  }

  /**
   * Analyze transaction timing
   */
  analyzeTransactionTiming() {
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();

    let riskScore = 0;
    const factors = [];

    // Off-hours check (10PM - 6AM)
    if (hour >= 22 || hour <= 6) {
      riskScore += this.rules.behavioral.offHoursRiskScore;
      factors.push('off_hours');
    }

    // Weekend check
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      riskScore += this.rules.behavioral.weekendRiskScore;
      factors.push('weekend');
    }

    return {
      type: 'timing',
      isRisky: riskScore > 0,
      score: riskScore,
      details: {
        hour,
        dayOfWeek,
        factors
      },
      message: factors.length > 0 ? 
        `Transaction during ${factors.join(', ')}` : 
        'Normal transaction timing'
    };
  }

  /**
   * Analyze device and location
   */
  async analyzeDevice(userId, deviceInfo = {}) {
    try {
      let riskScore = 0;
      const factors = [];

      // Check if device is known
      if (deviceInfo.deviceId) {
        const { data: knownDevice, error } = await supabase
          .from('trusted_devices')
          .select('id')
          .eq('user_id', userId)
          .eq('device_id', deviceInfo.deviceId)
          .single();

        if (error && error.code !== 'PGRST116') throw error;

        if (!knownDevice) {
          riskScore += this.rules.device.newDeviceRiskScore;
          factors.push('new_device');
        }
      }

      // Check location if available
      if (deviceInfo.location && deviceInfo.location !== 'unknown') {
        // Simple location check - in production, use more sophisticated geolocation
        const { data: knownLocations, error } = await supabase
          .from('user_sessions')
          .select('location')
          .eq('user_id', userId)
          .not('location', 'is', null)
          .limit(10);

        if (error) throw error;

        const isKnownLocation = knownLocations.some(
          session => session.location === deviceInfo.location
        );

        if (!isKnownLocation) {
          riskScore += this.rules.device.unknownLocationRiskScore;
          factors.push('unknown_location');
        }
      }

      return {
        type: 'device',
        isRisky: riskScore > 0,
        score: riskScore,
        details: {
          deviceId: deviceInfo.deviceId,
          location: deviceInfo.location,
          factors
        },
        message: factors.length > 0 ? 
          `Transaction from ${factors.join(', ')}` : 
          'Known device and location'
      };
    } catch (error) {
      console.error('❌ Error analyzing device:', error);
      return { type: 'device', isRisky: false, score: 0, error: error.message };
    }
  }

  /**
   * Analyze behavioral patterns
   */
  async analyzeBehavioralPatterns(userId, transactionData) {
    try {
      let riskScore = 0;
      const factors = [];

      // Check for rapid succession transactions
      const oneMinuteAgo = new Date();
      oneMinuteAgo.setMinutes(oneMinuteAgo.getMinutes() - 1);

      const { data: recentTransactions, error } = await supabase
        .from('transactions')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', oneMinuteAgo.toISOString());

      if (error) throw error;

      if (recentTransactions.length >= this.rules.behavioral.rapidSuccessionThreshold) {
        riskScore += 25;
        factors.push('rapid_succession');
      }

      // Check daily transaction count
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      const { data: todayTransactions, error: todayError } = await supabase
        .from('transactions')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', todayStart.toISOString());

      if (todayError) throw todayError;

      if (todayTransactions.length >= this.rules.transaction.maxDailyTransactions) {
        riskScore += 20;
        factors.push('high_daily_volume');
      }

      return {
        type: 'behavioral',
        isRisky: riskScore > 0,
        score: riskScore,
        details: {
          recentCount: recentTransactions.length,
          dailyCount: todayTransactions.length,
          factors
        },
        message: factors.length > 0 ? 
          `Unusual behavior: ${factors.join(', ')}` : 
          'Normal behavioral patterns'
      };
    } catch (error) {
      console.error('❌ Error analyzing behavior:', error);
      return { type: 'behavioral', isRisky: false, score: 0, error: error.message };
    }
  }

  /**
   * Calculate risk level from score
   */
  calculateRiskLevel(score) {
    if (score >= this.riskThresholds.critical) return 'critical';
    if (score >= this.riskThresholds.high) return 'high';
    if (score >= this.riskThresholds.medium) return 'medium';
    if (score >= this.riskThresholds.low) return 'low';
    return 'minimal';
  }

  /**
   * Get recommendation based on risk level
   */
  getRecommendation(riskLevel, riskFactors) {
    switch (riskLevel) {
      case 'critical':
        return {
          action: 'block',
          message: 'Transaction blocked due to high fraud risk',
          requiresReview: true
        };
      case 'high':
        return {
          action: 'review',
          message: 'Transaction requires manual review',
          requiresReview: true
        };
      case 'medium':
        return {
          action: 'challenge',
          message: 'Additional authentication required',
          requiresReview: false
        };
      case 'low':
        return {
          action: 'monitor',
          message: 'Transaction flagged for monitoring',
          requiresReview: false
        };
      default:
        return {
          action: 'allow',
          message: 'Transaction approved',
          requiresReview: false
        };
    }
  }

  /**
   * Log fraud analysis results
   */
  async logFraudAnalysis(userId, transactionData, analysis) {
    try {
      const { error } = await supabase
        .from('fraud_analysis_logs')
        .insert({
          user_id: userId,
          transaction_data: transactionData,
          risk_score: analysis.riskScore,
          risk_level: analysis.riskLevel,
          risk_factors: analysis.riskFactors,
          recommendation: analysis.recommendation,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('❌ Error logging fraud analysis:', error);
      // Don't throw - logging failure shouldn't stop transaction
    }
  }

  /**
   * Trigger fraud alert
   */
  async triggerFraudAlert(userId, analysis) {
    try {
      console.log('🚨 Triggering fraud alert for user:', userId);

      // Send notification to user
      await notificationService.sendSecurityAlert(userId, {
        type: 'fraud_detection',
        severity: analysis.riskLevel,
        message: `Suspicious transaction detected with ${analysis.riskLevel} risk level`,
        details: analysis.riskFactors.map(factor => factor.message).join(', ')
      });

      // Log security event
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: 'fraud_alert',
          severity: analysis.riskLevel,
          details: {
            riskScore: analysis.riskScore,
            riskFactors: analysis.riskFactors,
            recommendation: analysis.recommendation
          },
          created_at: new Date().toISOString()
        });

      console.log('✅ Fraud alert triggered successfully');
    } catch (error) {
      console.error('❌ Error triggering fraud alert:', error);
    }
  }
}

export default new FraudDetectionService();
