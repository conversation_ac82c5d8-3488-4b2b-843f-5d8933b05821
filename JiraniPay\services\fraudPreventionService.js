/**
 * Fraud Prevention Service
 * Main integration service that coordinates transaction limits, fraud detection,
 * risk scoring, and security alerts for comprehensive fraud prevention
 */

import transactionLimitsService from './transactionLimitsService';
import fraudDetectionService from './fraudDetectionService';
import riskScoringService from './riskScoringService';
import securityAlertService from './securityAlertService';
import { supabase } from './supabaseClient';

class FraudPreventionService {
  constructor() {
    this.riskThresholds = {
      allowTransaction: 40,
      requireChallenge: 60,
      requireReview: 80,
      blockTransaction: 90
    };
  }

  /**
   * Comprehensive transaction validation
   * Checks limits, analyzes fraud risk, and determines if transaction should proceed
   */
  async validateTransaction(userId, transactionData) {
    try {
      console.log('🔍 Starting comprehensive transaction validation:', { 
        userId, 
        amount: transactionData.amount 
      });

      const validationResult = {
        allowed: false,
        requiresChallenge: false,
        requiresReview: false,
        riskScore: 0,
        riskLevel: 'minimal',
        reasons: [],
        recommendations: [],
        timestamp: new Date().toISOString()
      };

      // Step 1: Check transaction limits
      console.log('📊 Checking transaction limits...');
      const limitCheck = await transactionLimitsService.checkTransactionLimits(
        userId, 
        transactionData.amount, 
        transactionData.type
      );

      if (!limitCheck.allowed) {
        validationResult.reasons.push({
          type: 'limit_violation',
          message: limitCheck.message,
          details: limitCheck
        });

        // Send limit violation alert
        await securityAlertService.sendLimitViolationAlert(userId, limitCheck);
        
        // Record violation
        await transactionLimitsService.recordLimitViolation(
          userId, 
          transactionData.amount, 
          limitCheck.reason,
          { transactionType: transactionData.type }
        );

        return validationResult;
      }

      // Step 2: Calculate risk score
      console.log('🎯 Calculating risk score...');
      const riskAssessment = await riskScoringService.calculateRiskScore(
        userId, 
        transactionData
      );

      validationResult.riskScore = riskAssessment.finalScore;
      validationResult.riskLevel = riskAssessment.riskLevel;

      // Step 3: Fraud detection analysis
      console.log('🔍 Running fraud detection...');
      const fraudAnalysis = await fraudDetectionService.analyzeTransaction(
        userId, 
        transactionData
      );

      // Combine risk factors
      const combinedRiskScore = Math.max(riskAssessment.finalScore, fraudAnalysis.riskScore);
      validationResult.riskScore = combinedRiskScore;
      validationResult.riskLevel = this.calculateCombinedRiskLevel(combinedRiskScore);

      // Step 4: Determine action based on risk
      const action = this.determineTransactionAction(combinedRiskScore, fraudAnalysis);
      
      validationResult.allowed = action.allow;
      validationResult.requiresChallenge = action.challenge;
      validationResult.requiresReview = action.review;
      validationResult.reasons = action.reasons;
      validationResult.recommendations = action.recommendations;

      // Step 5: Handle high-risk transactions
      if (combinedRiskScore >= this.riskThresholds.requireReview) {
        await this.handleHighRiskTransaction(userId, transactionData, {
          riskScore: combinedRiskScore,
          riskLevel: validationResult.riskLevel,
          fraudAnalysis,
          riskAssessment
        });
      }

      // Step 6: Log validation result
      await this.logValidationResult(userId, transactionData, validationResult);

      console.log('✅ Transaction validation completed:', {
        allowed: validationResult.allowed,
        riskScore: combinedRiskScore,
        riskLevel: validationResult.riskLevel
      });

      return validationResult;
    } catch (error) {
      console.error('❌ Error in transaction validation:', error);
      
      // In case of error, be conservative and require review
      return {
        allowed: false,
        requiresChallenge: false,
        requiresReview: true,
        riskScore: 100,
        riskLevel: 'critical',
        reasons: [{
          type: 'system_error',
          message: 'Unable to validate transaction due to system error',
          details: { error: error.message }
        }],
        recommendations: [{
          type: 'manual_review',
          message: 'Transaction requires manual review due to validation error'
        }],
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Determine transaction action based on risk analysis
   */
  determineTransactionAction(riskScore, fraudAnalysis) {
    const action = {
      allow: false,
      challenge: false,
      review: false,
      reasons: [],
      recommendations: []
    };

    if (riskScore >= this.riskThresholds.blockTransaction) {
      action.reasons.push({
        type: 'high_risk',
        message: 'Transaction blocked due to critical risk level',
        details: { riskScore, threshold: this.riskThresholds.blockTransaction }
      });
      action.recommendations.push({
        type: 'contact_support',
        message: 'Contact customer support to verify this transaction'
      });
    } else if (riskScore >= this.riskThresholds.requireReview) {
      action.review = true;
      action.reasons.push({
        type: 'requires_review',
        message: 'Transaction requires manual review due to high risk',
        details: { riskScore, threshold: this.riskThresholds.requireReview }
      });
      action.recommendations.push({
        type: 'manual_review',
        message: 'Transaction will be reviewed by security team'
      });
    } else if (riskScore >= this.riskThresholds.requireChallenge) {
      action.allow = true;
      action.challenge = true;
      action.reasons.push({
        type: 'requires_challenge',
        message: 'Additional authentication required',
        details: { riskScore, threshold: this.riskThresholds.requireChallenge }
      });
      action.recommendations.push({
        type: 'additional_auth',
        message: 'Complete additional verification to proceed'
      });
    } else {
      action.allow = true;
      if (riskScore >= this.riskThresholds.allowTransaction) {
        action.reasons.push({
          type: 'monitored',
          message: 'Transaction approved with monitoring',
          details: { riskScore }
        });
      }
    }

    return action;
  }

  /**
   * Handle high-risk transactions
   */
  async handleHighRiskTransaction(userId, transactionData, riskData) {
    try {
      console.log('🚨 Handling high-risk transaction:', { 
        userId, 
        riskScore: riskData.riskScore 
      });

      // Send fraud alert
      await securityAlertService.sendFraudAlert(userId, riskData.fraudAnalysis);

      // Create security event
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: 'high_risk_transaction',
          severity: riskData.riskLevel,
          details: {
            transaction_amount: transactionData.amount,
            transaction_type: transactionData.type,
            risk_score: riskData.riskScore,
            risk_factors: riskData.fraudAnalysis.riskFactors,
            risk_assessment: riskData.riskAssessment.components
          },
          created_at: new Date().toISOString()
        });

      // If critical risk, temporarily increase monitoring
      if (riskData.riskScore >= this.riskThresholds.blockTransaction) {
        await this.enableEnhancedMonitoring(userId, 24); // 24 hours
      }

      console.log('✅ High-risk transaction handled');
    } catch (error) {
      console.error('❌ Error handling high-risk transaction:', error);
    }
  }

  /**
   * Enable enhanced monitoring for user
   */
  async enableEnhancedMonitoring(userId, durationHours = 24) {
    try {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + durationHours);

      await supabase
        .from('user_monitoring')
        .upsert({
          user_id: userId,
          monitoring_level: 'enhanced',
          enabled_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          reason: 'high_risk_transaction'
        });

      console.log('✅ Enhanced monitoring enabled for user:', userId);
    } catch (error) {
      console.error('❌ Error enabling enhanced monitoring:', error);
    }
  }

  /**
   * Calculate combined risk level
   */
  calculateCombinedRiskLevel(score) {
    if (score >= 90) return 'critical';
    if (score >= 75) return 'high';
    if (score >= 50) return 'medium';
    if (score >= 25) return 'low';
    return 'minimal';
  }

  /**
   * Log validation result for audit trail
   */
  async logValidationResult(userId, transactionData, validationResult) {
    try {
      await supabase
        .from('transaction_validations')
        .insert({
          user_id: userId,
          transaction_data: {
            amount: transactionData.amount,
            type: transactionData.type,
            recipient: transactionData.recipient
          },
          validation_result: validationResult,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging validation result:', error);
      // Don't throw - logging failure shouldn't stop transaction
    }
  }

  /**
   * Get user's fraud prevention status
   */
  async getUserFraudStatus(userId) {
    try {
      const [limitStatus, recentAlerts, riskHistory] = await Promise.all([
        transactionLimitsService.getLimitStatus(userId),
        securityAlertService.getUserSecurityAlerts(userId, 10),
        this.getRecentRiskHistory(userId)
      ]);

      return {
        limits: limitStatus,
        recentAlerts: recentAlerts.filter(alert => !alert.resolved),
        riskHistory,
        overallRiskLevel: this.calculateOverallRiskLevel(recentAlerts, riskHistory),
        recommendations: this.generateUserRecommendations(limitStatus, recentAlerts, riskHistory)
      };
    } catch (error) {
      console.error('❌ Error getting fraud status:', error);
      throw error;
    }
  }

  /**
   * Get recent risk history for user
   */
  async getRecentRiskHistory(userId, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: riskHistory, error } = await supabase
        .from('risk_scores')
        .select('risk_score, risk_level, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      return riskHistory || [];
    } catch (error) {
      console.error('❌ Error getting risk history:', error);
      return [];
    }
  }

  /**
   * Calculate overall risk level for user
   */
  calculateOverallRiskLevel(alerts, riskHistory) {
    const highSeverityAlerts = alerts.filter(alert => 
      alert.severity === 'high' || alert.severity === 'critical'
    ).length;

    const avgRiskScore = riskHistory.length > 0 
      ? riskHistory.reduce((sum, risk) => sum + risk.risk_score, 0) / riskHistory.length
      : 0;

    if (highSeverityAlerts > 2 || avgRiskScore > 75) return 'high';
    if (highSeverityAlerts > 0 || avgRiskScore > 50) return 'medium';
    return 'low';
  }

  /**
   * Generate user-specific recommendations
   */
  generateUserRecommendations(limitStatus, alerts, riskHistory) {
    const recommendations = [];

    // Verification recommendations
    if (limitStatus.verificationLevel === 'basic') {
      recommendations.push({
        type: 'verification',
        priority: 'medium',
        title: 'Complete Enhanced Verification',
        description: 'Increase your transaction limits and reduce fraud risk',
        action: 'verify_identity'
      });
    }

    // Security recommendations based on alerts
    const recentHighRiskAlerts = alerts.filter(alert => 
      alert.severity === 'high' || alert.severity === 'critical'
    );

    if (recentHighRiskAlerts.length > 0) {
      recommendations.push({
        type: 'security',
        priority: 'high',
        title: 'Review Security Settings',
        description: 'Recent high-risk alerts detected on your account',
        action: 'review_security'
      });
    }

    // Risk pattern recommendations
    const highRiskScores = riskHistory.filter(risk => risk.risk_score > 70);
    if (highRiskScores.length > 3) {
      recommendations.push({
        type: 'behavior',
        priority: 'medium',
        title: 'Review Transaction Patterns',
        description: 'Consider adjusting transaction timing and amounts',
        action: 'review_patterns'
      });
    }

    return recommendations;
  }
}

export default new FraudPreventionService();
