/**
 * Investment Transaction Service
 * Service for handling buy/sell transactions, order management,
 * and portfolio updates for investment assets
 */

import { supabase } from './supabaseClient';
import investmentPortfolioService from './investmentPortfolioService';
import marketDataService from './marketDataService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';
import { isValidUUID, requireAuthentication } from '../utils/userUtils';

class InvestmentTransactionService {
  constructor() {
    this.tradingFeePercentage = 0.001; // 0.1% trading fee
    this.minimumOrderValue = 10; // $10 minimum order
    this.maxOrderValue = 100000; // $100k maximum order
  }

  /**
   * Execute buy order
   */
  async executeBuyOrder(userId, orderData) {
    try {
      console.log('📈 Executing buy order:', { userId, orderData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Validate order data
      const validation = this.validateOrderData(orderData, 'buy');
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      // Get portfolio details
      const portfolioResult = await investmentPortfolioService.getPortfolioDetails(
        orderData.portfolioId, 
        userId
      );
      if (!portfolioResult.success) {
        return { success: false, error: 'Portfolio not found' };
      }

      const portfolio = portfolioResult.portfolio;

      // Get current asset price
      const assetResult = await marketDataService.getAssetDetails(orderData.assetId);
      if (!assetResult.success) {
        return { success: false, error: 'Asset not found' };
      }

      const asset = assetResult.asset;
      const currentPrice = asset.currentPrice;

      // Calculate order details
      let quantity, totalCost;
      if (orderData.orderType === 'market') {
        if (orderData.investmentAmount) {
          // Dollar amount specified
          const fees = orderData.investmentAmount * this.tradingFeePercentage;
          const netAmount = orderData.investmentAmount - fees;
          quantity = netAmount / currentPrice;
          totalCost = orderData.investmentAmount;
        } else {
          // Quantity specified
          quantity = orderData.quantity;
          const grossAmount = quantity * currentPrice;
          const fees = grossAmount * this.tradingFeePercentage;
          totalCost = grossAmount + fees;
        }
      } else {
        // Limit order
        quantity = orderData.quantity;
        const grossAmount = quantity * orderData.limitPrice;
        const fees = grossAmount * this.tradingFeePercentage;
        totalCost = grossAmount + fees;
      }

      // Validate order value
      if (totalCost < this.minimumOrderValue) {
        return { 
          success: false, 
          error: `Minimum order value is ${formatCurrency(this.minimumOrderValue, 'USD')}` 
        };
      }

      if (totalCost > this.maxOrderValue) {
        return { 
          success: false, 
          error: `Maximum order value is ${formatCurrency(this.maxOrderValue, 'USD')}` 
        };
      }

      // Check sufficient cash balance
      if (portfolio.cashBalance < totalCost) {
        return { 
          success: false, 
          error: 'Insufficient cash balance in portfolio' 
        };
      }

      // Execute the transaction
      const result = await this.processTransaction({
        userId,
        portfolioId: orderData.portfolioId,
        assetId: orderData.assetId,
        transactionType: 'buy',
        quantity,
        price: currentPrice,
        totalAmount: totalCost,
        fees: totalCost * this.tradingFeePercentage,
        orderType: orderData.orderType,
        limitPrice: orderData.limitPrice,
        notes: orderData.notes
      });

      if (result.success) {
        // Update portfolio holdings
        await this.updatePortfolioHolding(
          orderData.portfolioId,
          orderData.assetId,
          quantity,
          currentPrice,
          'buy'
        );

        // Update portfolio cash balance
        await this.updatePortfolioCash(orderData.portfolioId, -totalCost);

        // Send notification
        await this.sendTransactionNotification(userId, {
          type: 'buy_executed',
          asset: asset,
          quantity,
          price: currentPrice,
          totalCost,
          portfolioName: portfolio.portfolioName
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error executing buy order:', error);
      return { success: false, error: 'Failed to execute buy order' };
    }
  }

  /**
   * Execute sell order
   */
  async executeSellOrder(userId, orderData) {
    try {
      console.log('📉 Executing sell order:', { userId, orderData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      // Validate order data
      const validation = this.validateOrderData(orderData, 'sell');
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      // Get portfolio details
      const portfolioResult = await investmentPortfolioService.getPortfolioDetails(
        orderData.portfolioId, 
        userId
      );
      if (!portfolioResult.success) {
        return { success: false, error: 'Portfolio not found' };
      }

      const portfolio = portfolioResult.portfolio;

      // Get current holding
      const holding = portfolio.holdings.find(h => h.assetId === orderData.assetId);
      if (!holding) {
        return { success: false, error: 'Asset not found in portfolio' };
      }

      // Check sufficient quantity
      if (holding.quantity < orderData.quantity) {
        return { 
          success: false, 
          error: `Insufficient quantity. Available: ${holding.quantity}` 
        };
      }

      // Get current asset price
      const assetResult = await marketDataService.getAssetDetails(orderData.assetId);
      if (!assetResult.success) {
        return { success: false, error: 'Asset not found' };
      }

      const asset = assetResult.asset;
      const currentPrice = asset.currentPrice;

      // Calculate order details
      const quantity = orderData.quantity;
      const grossAmount = quantity * currentPrice;
      const fees = grossAmount * this.tradingFeePercentage;
      const netAmount = grossAmount - fees;

      // Execute the transaction
      const result = await this.processTransaction({
        userId,
        portfolioId: orderData.portfolioId,
        assetId: orderData.assetId,
        transactionType: 'sell',
        quantity,
        price: currentPrice,
        totalAmount: grossAmount,
        fees,
        netAmount,
        orderType: orderData.orderType,
        limitPrice: orderData.limitPrice,
        notes: orderData.notes
      });

      if (result.success) {
        // Update portfolio holdings
        await this.updatePortfolioHolding(
          orderData.portfolioId,
          orderData.assetId,
          quantity,
          currentPrice,
          'sell'
        );

        // Update portfolio cash balance
        await this.updatePortfolioCash(orderData.portfolioId, netAmount);

        // Calculate realized gain/loss
        const costBasis = holding.averageCost * quantity;
        const realizedGainLoss = grossAmount - costBasis;

        // Send notification
        await this.sendTransactionNotification(userId, {
          type: 'sell_executed',
          asset: asset,
          quantity,
          price: currentPrice,
          netAmount,
          realizedGainLoss,
          portfolioName: portfolio.portfolioName
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error executing sell order:', error);
      return { success: false, error: 'Failed to execute sell order' };
    }
  }

  /**
   * Process transaction and create database records
   */
  async processTransaction(transactionData) {
    try {
      const reference = await this.generateTransactionReference();

      const { data: transaction, error } = await supabase
        .from('investment_transactions')
        .insert({
          portfolio_id: transactionData.portfolioId,
          asset_id: transactionData.assetId,
          user_id: transactionData.userId,
          transaction_type: transactionData.transactionType,
          quantity: transactionData.quantity,
          price: transactionData.price,
          total_amount: transactionData.totalAmount,
          fees: transactionData.fees,
          net_amount: transactionData.netAmount || transactionData.totalAmount - transactionData.fees,
          order_type: transactionData.orderType || 'market',
          execution_price: transactionData.price,
          status: 'executed',
          executed_at: new Date().toISOString(),
          reference_number: reference,
          notes: transactionData.notes,
          metadata: {
            source: 'mobile_app',
            execution_method: 'instant'
          }
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating transaction record:', error);
        return { success: false, error: 'Failed to create transaction record' };
      }

      console.log('✅ Transaction executed:', transaction.id);

      return {
        success: true,
        transaction: this.formatTransactionResponse(transaction)
      };
    } catch (error) {
      console.error('❌ Error processing transaction:', error);
      return { success: false, error: 'Failed to process transaction' };
    }
  }

  /**
   * Update portfolio holding
   */
  async updatePortfolioHolding(portfolioId, assetId, quantity, price, transactionType) {
    try {
      // Get existing holding
      const { data: existingHolding, error: holdingError } = await supabase
        .from('portfolio_holdings')
        .select('*')
        .eq('portfolio_id', portfolioId)
        .eq('asset_id', assetId)
        .single();

      if (transactionType === 'buy') {
        if (existingHolding) {
          // Update existing holding
          const newQuantity = existingHolding.quantity + quantity;
          const newTotalCost = existingHolding.total_cost + (quantity * price);
          const newAverageCost = newTotalCost / newQuantity;

          await supabase
            .from('portfolio_holdings')
            .update({
              quantity: newQuantity,
              average_cost: newAverageCost,
              total_cost: newTotalCost,
              current_value: newQuantity * price,
              last_transaction_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', existingHolding.id);
        } else {
          // Create new holding
          await supabase
            .from('portfolio_holdings')
            .insert({
              portfolio_id: portfolioId,
              asset_id: assetId,
              user_id: (await supabase.from('investment_portfolios').select('user_id').eq('id', portfolioId).single()).data.user_id,
              quantity: quantity,
              average_cost: price,
              total_cost: quantity * price,
              current_value: quantity * price,
              first_purchase_date: new Date().toISOString(),
              last_transaction_date: new Date().toISOString()
            });
        }
      } else if (transactionType === 'sell') {
        if (existingHolding) {
          const newQuantity = existingHolding.quantity - quantity;
          
          if (newQuantity <= 0) {
            // Remove holding if quantity is zero or negative
            await supabase
              .from('portfolio_holdings')
              .update({
                quantity: 0,
                is_active: false,
                last_transaction_date: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', existingHolding.id);
          } else {
            // Update holding with new quantity
            const newTotalCost = existingHolding.total_cost - (quantity * existingHolding.average_cost);
            
            await supabase
              .from('portfolio_holdings')
              .update({
                quantity: newQuantity,
                total_cost: newTotalCost,
                current_value: newQuantity * price,
                last_transaction_date: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', existingHolding.id);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error updating portfolio holding:', error);
      throw error;
    }
  }

  /**
   * Update portfolio cash balance
   */
  async updatePortfolioCash(portfolioId, amount) {
    try {
      const { data: portfolio, error } = await supabase
        .from('investment_portfolios')
        .select('cash_balance')
        .eq('id', portfolioId)
        .single();

      if (error) {
        throw new Error('Portfolio not found');
      }

      await supabase
        .from('investment_portfolios')
        .update({
          cash_balance: portfolio.cash_balance + amount,
          updated_at: new Date().toISOString()
        })
        .eq('id', portfolioId);
    } catch (error) {
      console.error('❌ Error updating portfolio cash:', error);
      throw error;
    }
  }

  /**
   * Get transaction history
   */
  async getTransactionHistory(portfolioId, userId, options = {}) {
    try {
      if (!portfolioId || !isValidUUID(portfolioId)) {
        return { success: false, error: 'Valid portfolio ID is required' };
      }

      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      let query = supabase
        .from('investment_transactions')
        .select(`
          *,
          asset:investment_assets(symbol, name, asset_type)
        `)
        .eq('portfolio_id', portfolioId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }

      if (options.startDate) {
        query = query.gte('created_at', options.startDate);
      }

      if (options.endDate) {
        query = query.lte('created_at', options.endDate);
      }

      const { data: transactions, error } = await query;

      if (error) {
        console.error('❌ Error fetching transaction history:', error);
        return { success: false, error: 'Failed to fetch transaction history' };
      }

      return {
        success: true,
        transactions: transactions.map(t => this.formatTransactionResponse(t))
      };
    } catch (error) {
      console.error('❌ Error getting transaction history:', error);
      return { success: false, error: 'Failed to fetch transaction history' };
    }
  }

  /**
   * Validate order data
   */
  validateOrderData(data, transactionType) {
    const errors = [];

    if (!data.portfolioId || !isValidUUID(data.portfolioId)) {
      errors.push('Valid portfolio ID is required');
    }

    if (!data.assetId || !isValidUUID(data.assetId)) {
      errors.push('Valid asset ID is required');
    }

    if (transactionType === 'buy') {
      if (!data.investmentAmount && !data.quantity) {
        errors.push('Either investment amount or quantity is required');
      }
      if (data.investmentAmount && data.investmentAmount <= 0) {
        errors.push('Investment amount must be positive');
      }
    }

    if (transactionType === 'sell') {
      if (!data.quantity || data.quantity <= 0) {
        errors.push('Quantity is required and must be positive');
      }
    }

    if (data.orderType === 'limit' && (!data.limitPrice || data.limitPrice <= 0)) {
      errors.push('Limit price is required for limit orders');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate transaction reference
   */
  async generateTransactionReference() {
    const prefix = 'TXN';
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Send transaction notification
   */
  async sendTransactionNotification(userId, data) {
    try {
      const notifications = {
        buy_executed: {
          title: 'Buy Order Executed',
          content: `Successfully purchased ${data.quantity.toFixed(4)} shares of ${data.asset.symbol} for ${formatCurrency(data.totalCost, 'USD')}`,
          type: 'investment_buy'
        },
        sell_executed: {
          title: 'Sell Order Executed',
          content: `Successfully sold ${data.quantity.toFixed(4)} shares of ${data.asset.symbol} for ${formatCurrency(data.netAmount, 'USD')}`,
          type: 'investment_sell'
        }
      };

      const notification = notifications[data.type];
      if (notification) {
        await enhancedNotificationService.sendNotification(userId, {
          ...notification,
          data: {
            portfolioName: data.portfolioName,
            assetSymbol: data.asset.symbol,
            quantity: data.quantity,
            price: data.price,
            ...data
          }
        });
      }
    } catch (error) {
      console.error('❌ Error sending transaction notification:', error);
    }
  }

  /**
   * Format transaction response
   */
  formatTransactionResponse(transaction) {
    return {
      id: transaction.id,
      portfolioId: transaction.portfolio_id,
      assetId: transaction.asset_id,
      asset: transaction.asset,
      transactionType: transaction.transaction_type,
      quantity: parseFloat(transaction.quantity || 0),
      price: parseFloat(transaction.price || 0),
      totalAmount: parseFloat(transaction.total_amount || 0),
      fees: parseFloat(transaction.fees || 0),
      netAmount: parseFloat(transaction.net_amount || 0),
      orderType: transaction.order_type,
      orderId: transaction.order_id,
      executionPrice: parseFloat(transaction.execution_price || 0),
      status: transaction.status,
      executedAt: transaction.executed_at,
      referenceNumber: transaction.reference_number,
      externalReference: transaction.external_reference,
      notes: transaction.notes,
      metadata: transaction.metadata,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at
    };
  }
}

export default new InvestmentTransactionService();
