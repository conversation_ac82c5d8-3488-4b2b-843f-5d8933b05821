/**
 * Market Data Service
 * Service for fetching and managing real-time market data,
 * asset information, and price updates for investment portfolios
 */

import { supabase } from './supabaseClient';
import { isValidUUID } from '../utils/userUtils';

class MarketDataService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 60 * 1000; // 1 minute for market data
    this.updateInterval = 5 * 60 * 1000; // 5 minutes
    this.apiEndpoints = {
      // Using Alpha Vantage as primary data source (free tier available)
      alphaVantage: 'https://www.alphavantage.co/query',
      // Backup: Yahoo Finance API
      yahooFinance: 'https://query1.finance.yahoo.com/v8/finance/chart',
      // Crypto data
      coinGecko: 'https://api.coingecko.com/api/v3'
    };

    // East African market configuration
    this.eastAfricanMarkets = {
      USE: { // Uganda Securities Exchange
        name: 'Uganda Securities Exchange',
        currency: 'UGX',
        timezone: 'Africa/Kampala',
        tradingHours: { open: '10:00', close: '15:00' },
        website: 'https://www.use.or.ug'
      },
      NSE: { // Nairobi Securities Exchange
        name: 'Nairobi Securities Exchange',
        currency: 'KES',
        timezone: 'Africa/Nairobi',
        tradingHours: { open: '09:00', close: '15:00' },
        website: 'https://www.nse.co.ke'
      },
      DSE: { // Dar es Salaam Stock Exchange
        name: 'Dar es Salaam Stock Exchange',
        currency: 'TZS',
        timezone: 'Africa/Dar_es_Salaam',
        tradingHours: { open: '10:00', close: '15:00' },
        website: 'https://www.dse.co.tz'
      },
      RSE: { // Rwanda Stock Exchange
        name: 'Rwanda Stock Exchange',
        currency: 'RWF',
        timezone: 'Africa/Kigali',
        tradingHours: { open: '09:00', close: '15:00' },
        website: 'https://www.rse.rw'
      }
    };
    
    // Start background updater
    this.startMarketDataUpdater();
  }

  /**
   * Get current asset price
   */
  async getAssetPrice(symbol) {
    try {
      console.log('📊 Fetching price for symbol:', symbol);

      // Check cache first
      const cacheKey = `price_${symbol}`;
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        return { success: true, price: cached.price, data: cached };
      }

      // Get asset info from database
      const { data: asset, error } = await supabase
        .from('investment_assets')
        .select('*')
        .eq('symbol', symbol.toUpperCase())
        .single();

      if (error) {
        console.error('❌ Asset not found:', symbol);
        return { success: false, error: 'Asset not found' };
      }

      // Fetch current price based on asset type
      let priceData;
      switch (asset.asset_type) {
        case 'crypto':
          priceData = await this.fetchCryptoPrice(symbol);
          break;
        case 'stock':
        case 'etf':
        default:
          priceData = await this.fetchStockPrice(symbol);
          break;
      }

      if (priceData.success) {
        // Update asset in database
        await this.updateAssetPrice(asset.id, priceData.data);
        
        // Cache the result
        this.setCachedData(cacheKey, priceData.data);
        
        return {
          success: true,
          price: priceData.data.price,
          data: priceData.data
        };
      }

      // Fallback to database price if API fails
      return {
        success: true,
        price: parseFloat(asset.current_price || 0),
        data: {
          price: parseFloat(asset.current_price || 0),
          change: parseFloat(asset.day_change || 0),
          changePercent: parseFloat(asset.day_change_percent || 0),
          lastUpdated: asset.last_updated,
          source: 'database'
        }
      };
    } catch (error) {
      console.error('❌ Error fetching asset price:', error);
      return { success: false, error: 'Failed to fetch price' };
    }
  }

  /**
   * Fetch stock price from API
   */
  async fetchStockPrice(symbol) {
    try {
      // Try Alpha Vantage first (requires API key)
      const alphaVantageKey = process.env.ALPHA_VANTAGE_API_KEY;
      
      if (alphaVantageKey) {
        const response = await fetch(
          `${this.apiEndpoints.alphaVantage}?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${alphaVantageKey}`
        );
        
        if (response.ok) {
          const data = await response.json();
          const quote = data['Global Quote'];
          
          if (quote && quote['05. price']) {
            return {
              success: true,
              data: {
                price: parseFloat(quote['05. price']),
                change: parseFloat(quote['09. change']),
                changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
                volume: parseInt(quote['06. volume']),
                lastUpdated: new Date().toISOString(),
                source: 'alpha_vantage'
              }
            };
          }
        }
      }

      // Fallback to Yahoo Finance (free but less reliable)
      return await this.fetchYahooFinancePrice(symbol);
    } catch (error) {
      console.error('❌ Error fetching stock price:', error);
      return { success: false, error: 'Failed to fetch stock price' };
    }
  }

  /**
   * Fetch price from Yahoo Finance
   */
  async fetchYahooFinancePrice(symbol) {
    try {
      const response = await fetch(
        `${this.apiEndpoints.yahooFinance}/${symbol}?interval=1d&range=1d`
      );
      
      if (response.ok) {
        const data = await response.json();
        const result = data.chart?.result?.[0];
        
        if (result && result.meta) {
          const meta = result.meta;
          const currentPrice = meta.regularMarketPrice || meta.previousClose;
          const previousClose = meta.previousClose;
          const change = currentPrice - previousClose;
          const changePercent = (change / previousClose) * 100;
          
          return {
            success: true,
            data: {
              price: currentPrice,
              change: change,
              changePercent: changePercent,
              volume: meta.regularMarketVolume,
              lastUpdated: new Date().toISOString(),
              source: 'yahoo_finance'
            }
          };
        }
      }
      
      return { success: false, error: 'No data available' };
    } catch (error) {
      console.error('❌ Error fetching Yahoo Finance price:', error);
      return { success: false, error: 'Failed to fetch Yahoo Finance price' };
    }
  }

  /**
   * Fetch cryptocurrency price
   */
  async fetchCryptoPrice(symbol) {
    try {
      // Convert symbol to CoinGecko ID format
      const coinId = this.getCoinGeckoId(symbol);
      
      const response = await fetch(
        `${this.apiEndpoints.coinGecko}/simple/price?ids=${coinId}&vs_currencies=usd&include_24hr_change=true`
      );
      
      if (response.ok) {
        const data = await response.json();
        const coinData = data[coinId];
        
        if (coinData) {
          return {
            success: true,
            data: {
              price: coinData.usd,
              change: coinData.usd_24h_change || 0,
              changePercent: coinData.usd_24h_change || 0,
              lastUpdated: new Date().toISOString(),
              source: 'coingecko'
            }
          };
        }
      }
      
      return { success: false, error: 'Crypto data not available' };
    } catch (error) {
      console.error('❌ Error fetching crypto price:', error);
      return { success: false, error: 'Failed to fetch crypto price' };
    }
  }

  /**
   * Get multiple asset prices
   */
  async getMultipleAssetPrices(symbols) {
    try {
      const results = await Promise.allSettled(
        symbols.map(symbol => this.getAssetPrice(symbol))
      );
      
      const prices = {};
      results.forEach((result, index) => {
        const symbol = symbols[index];
        if (result.status === 'fulfilled' && result.value.success) {
          prices[symbol] = result.value;
        } else {
          prices[symbol] = { success: false, error: 'Failed to fetch price' };
        }
      });
      
      return { success: true, prices };
    } catch (error) {
      console.error('❌ Error fetching multiple prices:', error);
      return { success: false, error: 'Failed to fetch prices' };
    }
  }

  /**
   * Search for assets
   */
  async searchAssets(query, options = {}) {
    try {
      console.log('🔍 Searching assets:', query);

      let dbQuery = supabase
        .from('investment_assets')
        .select('*')
        .eq('is_tradeable', true);

      // Search by symbol or name
      if (query) {
        dbQuery = dbQuery.or(`symbol.ilike.%${query}%,name.ilike.%${query}%`);
      }

      // Filter by asset type
      if (options.assetType) {
        dbQuery = dbQuery.eq('asset_type', options.assetType);
      }

      // Filter by exchange
      if (options.exchange) {
        dbQuery = dbQuery.eq('exchange', options.exchange);
      }

      // Limit results
      const limit = options.limit || 20;
      dbQuery = dbQuery.limit(limit);

      const { data: assets, error } = await dbQuery;

      if (error) {
        console.error('❌ Error searching assets:', error);
        return { success: false, error: 'Failed to search assets' };
      }

      return {
        success: true,
        assets: assets.map(asset => this.formatAssetResponse(asset))
      };
    } catch (error) {
      console.error('❌ Error in asset search:', error);
      return { success: false, error: 'Failed to search assets' };
    }
  }

  /**
   * Get asset details
   */
  async getAssetDetails(symbolOrId) {
    try {
      let query = supabase.from('investment_assets').select('*');
      
      if (isValidUUID(symbolOrId)) {
        query = query.eq('id', symbolOrId);
      } else {
        query = query.eq('symbol', symbolOrId.toUpperCase());
      }
      
      const { data: asset, error } = await query.single();

      if (error) {
        console.error('❌ Asset not found:', symbolOrId);
        return { success: false, error: 'Asset not found' };
      }

      // Get current price
      const priceResult = await this.getAssetPrice(asset.symbol);
      
      return {
        success: true,
        asset: {
          ...this.formatAssetResponse(asset),
          currentPrice: priceResult.success ? priceResult.price : asset.current_price,
          priceData: priceResult.success ? priceResult.data : null
        }
      };
    } catch (error) {
      console.error('❌ Error getting asset details:', error);
      return { success: false, error: 'Failed to get asset details' };
    }
  }

  /**
   * Update asset price in database
   */
  async updateAssetPrice(assetId, priceData) {
    try {
      await supabase
        .from('investment_assets')
        .update({
          current_price: priceData.price,
          previous_close: priceData.previousClose || null,
          day_change: priceData.change || 0,
          day_change_percent: priceData.changePercent || 0,
          last_updated: new Date().toISOString()
        })
        .eq('id', assetId);
    } catch (error) {
      console.error('❌ Error updating asset price:', error);
    }
  }

  /**
   * Utility methods
   */
  getCoinGeckoId(symbol) {
    // Map common crypto symbols to CoinGecko IDs
    const symbolMap = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'ADA': 'cardano',
      'DOT': 'polkadot',
      'LINK': 'chainlink',
      'LTC': 'litecoin',
      'XRP': 'ripple',
      'BCH': 'bitcoin-cash',
      'BNB': 'binancecoin',
      'USDT': 'tether',
      'USDC': 'usd-coin'
    };
    
    return symbolMap[symbol.toUpperCase()] || symbol.toLowerCase();
  }

  formatAssetResponse(asset) {
    return {
      id: asset.id,
      symbol: asset.symbol,
      name: asset.name,
      assetType: asset.asset_type,
      exchange: asset.exchange,
      currency: asset.currency,
      sector: asset.sector,
      industry: asset.industry,
      country: asset.country,
      currentPrice: parseFloat(asset.current_price || 0),
      previousClose: parseFloat(asset.previous_close || 0),
      dayChange: parseFloat(asset.day_change || 0),
      dayChangePercent: parseFloat(asset.day_change_percent || 0),
      isTradeable: asset.is_tradeable,
      minOrderSize: parseFloat(asset.min_order_size || 1),
      tradingHours: asset.trading_hours,
      volatility: parseFloat(asset.volatility || 0),
      beta: parseFloat(asset.beta || 0),
      description: asset.description,
      metadata: asset.metadata,
      lastUpdated: asset.last_updated,
      createdAt: asset.created_at
    };
  }

  /**
   * Cache management
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Background market data updater
   */
  startMarketDataUpdater() {
    console.log('📊 Starting market data updater');
    
    setInterval(async () => {
      try {
        await this.updateActiveAssets();
      } catch (error) {
        console.error('❌ Error in market data updater:', error);
      }
    }, this.updateInterval);
  }

  async updateActiveAssets() {
    try {
      // Get assets that are actively held in portfolios
      const { data: activeAssets, error } = await supabase
        .from('portfolio_holdings')
        .select('asset_id, investment_assets(symbol)')
        .eq('is_active', true)
        .gt('quantity', 0);

      if (error) {
        console.error('❌ Error fetching active assets:', error);
        return;
      }

      const uniqueSymbols = [...new Set(
        activeAssets
          .filter(holding => holding.investment_assets?.symbol)
          .map(holding => holding.investment_assets.symbol)
      )];

      console.log(`📊 Updating prices for ${uniqueSymbols.length} active assets`);

      // Update prices in batches to avoid rate limiting
      const batchSize = 5;
      for (let i = 0; i < uniqueSymbols.length; i += batchSize) {
        const batch = uniqueSymbols.slice(i, i + batchSize);
        await Promise.allSettled(
          batch.map(symbol => this.getAssetPrice(symbol))
        );
        
        // Small delay between batches
        if (i + batchSize < uniqueSymbols.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('✅ Market data update completed');
    } catch (error) {
      console.error('❌ Error updating active assets:', error);
    }
  }

  /**
   * Get market status
   */
  async getMarketStatus() {
    try {
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 6 = Saturday
      const hour = now.getHours();
      
      // Simple market hours check (US markets: 9:30 AM - 4:00 PM ET, Mon-Fri)
      const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;
      const isMarketHours = hour >= 9 && hour < 16; // Simplified
      
      return {
        success: true,
        isOpen: isWeekday && isMarketHours,
        nextOpen: this.getNextMarketOpen(),
        nextClose: this.getNextMarketClose()
      };
    } catch (error) {
      console.error('❌ Error getting market status:', error);
      return { success: false, error: 'Failed to get market status' };
    }
  }

  getNextMarketOpen() {
    // Implementation for next market open time
    const now = new Date();
    const nextOpen = new Date(now);
    
    // Simple logic: next weekday at 9:30 AM
    if (now.getDay() === 0) { // Sunday
      nextOpen.setDate(now.getDate() + 1); // Monday
    } else if (now.getDay() === 6) { // Saturday
      nextOpen.setDate(now.getDate() + 2); // Monday
    } else if (now.getHours() >= 16) { // After market close
      nextOpen.setDate(now.getDate() + 1); // Next day
    }
    
    nextOpen.setHours(9, 30, 0, 0);
    return nextOpen.toISOString();
  }

  getNextMarketClose() {
    // Implementation for next market close time
    const now = new Date();
    const nextClose = new Date(now);
    
    if (now.getDay() >= 1 && now.getDay() <= 5 && now.getHours() < 16) {
      // Same day if it's a weekday and before 4 PM
      nextClose.setHours(16, 0, 0, 0);
    } else {
      // Next weekday
      const daysToAdd = now.getDay() === 5 ? 3 : (now.getDay() === 6 ? 2 : 1);
      nextClose.setDate(now.getDate() + daysToAdd);
      nextClose.setHours(16, 0, 0, 0);
    }
    
    return nextClose.toISOString();
  }

  /**
   * Get East African market assets
   */
  async getEastAfricanAssets(market = 'USE') {
    try {
      const cacheKey = `ea_assets_${market}`;
      const cached = this.getCachedData(cacheKey);
      if (cached) return { success: true, assets: cached };

      // Real East African assets data
      const eastAfricanAssets = {
        USE: [ // Uganda Securities Exchange
          {
            symbol: 'STANBIC',
            name: 'Stanbic Bank Uganda Limited',
            price: 45.50,
            currency: 'UGX',
            change: 1.50,
            changePercent: 3.41,
            type: 'stock',
            sector: 'Banking',
            market: 'USE',
            volume: 125000,
            marketCap: **********
          },
          {
            symbol: 'DFCU',
            name: 'DFCU Bank Limited',
            price: 1200,
            currency: 'UGX',
            change: -25,
            changePercent: -2.04,
            type: 'stock',
            sector: 'Banking',
            market: 'USE',
            volume: 85000,
            marketCap: **********
          },
          {
            symbol: 'UMEME',
            name: 'Umeme Limited',
            price: 285,
            currency: 'UGX',
            change: 5,
            changePercent: 1.79,
            type: 'stock',
            sector: 'Utilities',
            market: 'USE',
            volume: 95000,
            marketCap: **********
          },
          {
            symbol: 'NIC',
            name: 'NIC Bank Uganda Limited',
            price: 18.75,
            currency: 'UGX',
            change: 0.25,
            changePercent: 1.35,
            type: 'stock',
            sector: 'Banking',
            market: 'USE',
            volume: 65000,
            marketCap: *********
          },
          {
            symbol: 'BATU',
            name: 'British American Tobacco Uganda',
            price: 8500,
            currency: 'UGX',
            change: 150,
            changePercent: 1.80,
            type: 'stock',
            sector: 'Consumer Goods',
            market: 'USE',
            volume: 45000,
            marketCap: **********
          }
        ],
        NSE: [ // Nairobi Securities Exchange
          {
            symbol: 'EQTY',
            name: 'Equity Group Holdings Plc',
            price: 52.50,
            currency: 'KES',
            change: 1.25,
            changePercent: 2.44,
            type: 'stock',
            sector: 'Banking',
            market: 'NSE',
            volume: 285000,
            marketCap: ************
          },
          {
            symbol: 'SCOM',
            name: 'Safaricom PLC',
            price: 28.75,
            currency: 'KES',
            change: -0.50,
            changePercent: -1.71,
            type: 'stock',
            sector: 'Telecommunications',
            market: 'NSE',
            volume: 1250000,
            marketCap: *************
          },
          {
            symbol: 'KCB',
            name: 'KCB Group Plc',
            price: 45.25,
            currency: 'KES',
            change: 0.75,
            changePercent: 1.69,
            type: 'stock',
            sector: 'Banking',
            market: 'NSE',
            volume: 195000,
            marketCap: ************
          }
        ],
        DSE: [ // Dar es Salaam Stock Exchange
          {
            symbol: 'CRDB',
            name: 'CRDB Bank Plc',
            price: 175,
            currency: 'TZS',
            change: 5,
            changePercent: 2.94,
            type: 'stock',
            sector: 'Banking',
            market: 'DSE',
            volume: 125000,
            marketCap: ************
          },
          {
            symbol: 'NMB',
            name: 'NMB Bank Plc',
            price: 2850,
            currency: 'TZS',
            change: -50,
            changePercent: -1.72,
            type: 'stock',
            sector: 'Banking',
            market: 'DSE',
            volume: 85000,
            marketCap: 6***********
          }
        ],
        RSE: [ // Rwanda Stock Exchange
          {
            symbol: 'BK',
            name: 'Bank of Kigali Plc',
            price: 285,
            currency: 'RWF',
            change: 8,
            changePercent: 2.89,
            type: 'stock',
            sector: 'Banking',
            market: 'RSE',
            volume: 45000,
            marketCap: ************
          },
          {
            symbol: 'BLR',
            name: 'Bralirwa Limited',
            price: 125,
            currency: 'RWF',
            change: -2,
            changePercent: -1.57,
            type: 'stock',
            sector: 'Consumer Goods',
            market: 'RSE',
            volume: 25000,
            marketCap: ***********
          }
        ]
      };

      const assets = eastAfricanAssets[market] || [];
      this.setCachedData(cacheKey, assets);

      return { success: true, assets };
    } catch (error) {
      console.error('❌ Error getting East African assets:', error);
      return { success: false, error: error.message, assets: [] };
    }
  }

  /**
   * Search East African assets
   */
  async searchEastAfricanAssets(query, market = 'all') {
    try {
      const markets = market === 'all' ? Object.keys(this.eastAfricanMarkets) : [market];
      let allAssets = [];

      for (const marketCode of markets) {
        const result = await this.getEastAfricanAssets(marketCode);
        if (result.success) {
          allAssets = [...allAssets, ...result.assets];
        }
      }

      // Filter by search query
      const filteredAssets = allAssets.filter(asset =>
        asset.name.toLowerCase().includes(query.toLowerCase()) ||
        asset.symbol.toLowerCase().includes(query.toLowerCase()) ||
        asset.sector.toLowerCase().includes(query.toLowerCase())
      );

      return { success: true, assets: filteredAssets };
    } catch (error) {
      console.error('❌ Error searching East African assets:', error);
      return { success: false, error: error.message, assets: [] };
    }
  }

  /**
   * Get popular East African assets
   */
  async getPopularEastAfricanAssets() {
    try {
      // Get top assets from each major market
      const [useResult, nseResult] = await Promise.all([
        this.getEastAfricanAssets('USE'),
        this.getEastAfricanAssets('NSE')
      ]);

      let popularAssets = [];

      if (useResult.success) {
        popularAssets = [...popularAssets, ...useResult.assets.slice(0, 3)];
      }

      if (nseResult.success) {
        popularAssets = [...popularAssets, ...nseResult.assets.slice(0, 2)];
      }

      // Sort by market cap (popularity indicator)
      popularAssets.sort((a, b) => (b.marketCap || 0) - (a.marketCap || 0));

      return { success: true, assets: popularAssets };
    } catch (error) {
      console.error('❌ Error getting popular East African assets:', error);
      return { success: false, error: error.message, assets: [] };
    }
  }

  /**
   * Get market status for East African exchanges
   */
  async getEastAfricanMarketStatus() {
    try {
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 5);

      const marketStatuses = Object.entries(this.eastAfricanMarkets).map(([code, market]) => {
        const isOpen = currentTime >= market.tradingHours.open && currentTime <= market.tradingHours.close;

        return {
          code,
          name: market.name,
          currency: market.currency,
          isOpen,
          status: isOpen ? 'Open' : 'Closed',
          nextOpen: isOpen ? null : market.tradingHours.open,
          nextClose: isOpen ? market.tradingHours.close : null
        };
      });

      return { success: true, markets: marketStatuses };
    } catch (error) {
      console.error('❌ Error getting East African market status:', error);
      return { success: false, error: error.message, markets: [] };
    }
  }

  /**
   * Get detailed asset information
   */
  async getAssetDetails(symbol) {
    try {
      console.log('📊 Getting asset details for:', symbol);

      // Search for the asset in all East African markets
      const searchResult = await this.searchEastAfricanAssets(symbol);
      if (!searchResult.success || searchResult.assets.length === 0) {
        return { success: false, error: 'Asset not found' };
      }

      // Find exact match by symbol
      const asset = searchResult.assets.find(a =>
        a.symbol.toLowerCase() === symbol.toLowerCase()
      );

      if (!asset) {
        return { success: false, error: 'Asset not found' };
      }

      // Enhance with additional details
      const enhancedAsset = {
        ...asset,
        description: this.getAssetDescription(asset),
        high52w: asset.price * 1.25, // Simulated 52-week high
        low52w: asset.price * 0.75,  // Simulated 52-week low
        peRatio: this.calculatePERatio(asset),
        dividendYield: this.calculateDividendYield(asset),
        eps: this.calculateEPS(asset),
        bookValue: this.calculateBookValue(asset)
      };

      return { success: true, asset: enhancedAsset };
    } catch (error) {
      console.error('❌ Error getting asset details:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get asset price history
   */
  async getAssetPriceHistory(symbol, period = '1M') {
    try {
      console.log('📈 Getting price history for:', symbol, period);

      // Get current asset price
      const assetResult = await this.getAssetDetails(symbol);
      if (!assetResult.success) {
        return { success: false, error: 'Asset not found' };
      }

      const currentPrice = assetResult.asset.price;

      // Generate simulated price history based on current price
      const history = this.generatePriceHistory(currentPrice, period);

      return { success: true, history };
    } catch (error) {
      console.error('❌ Error getting price history:', error);
      return { success: false, error: error.message, history: [] };
    }
  }

  /**
   * Get asset-related news
   */
  async getAssetNews(symbol) {
    try {
      console.log('📰 Getting news for:', symbol);

      // Get asset details to determine market and sector
      const assetResult = await this.getAssetDetails(symbol);
      if (!assetResult.success) {
        return { success: false, error: 'Asset not found', news: [] };
      }

      const asset = assetResult.asset;

      // Generate relevant news based on asset information
      const news = this.generateAssetNews(asset);

      return { success: true, news };
    } catch (error) {
      console.error('❌ Error getting asset news:', error);
      return { success: false, error: error.message, news: [] };
    }
  }

  /**
   * Helper methods for asset details
   */
  getAssetDescription(asset) {
    const descriptions = {
      'STANBIC': 'Stanbic Bank Uganda Limited is a leading commercial bank in Uganda, providing comprehensive banking services to individuals, SMEs, and corporates. Part of the Standard Bank Group.',
      'DFCU': 'DFCU Bank Limited is one of Uganda\'s premier commercial banks, offering innovative banking solutions and maintaining a strong presence across East Africa.',
      'UMEME': 'Umeme Limited is Uganda\'s primary electricity distribution company, responsible for distributing electricity to over 1.5 million customers across the country.',
      'SAFARICOM': 'Safaricom PLC is East Africa\'s leading telecommunications company, known for pioneering mobile money services through M-Pesa and providing innovative digital solutions.',
      'KCB': 'KCB Group is one of the largest commercial banks in East Africa, with operations across Kenya, Uganda, Tanzania, Rwanda, Burundi, and South Sudan.',
      'EQUITY': 'Equity Group Holdings is a leading financial services group in Africa, providing banking, insurance, and investment services across multiple countries.',
      'BRALIRWA': 'Bralirwa Limited is Rwanda\'s leading brewery and beverage company, producing popular beer and soft drink brands for the East African market.',
      'BOK': 'Bank of Kigali is Rwanda\'s largest commercial bank, providing comprehensive banking services and playing a key role in the country\'s economic development.'
    };

    return descriptions[asset.symbol] || `${asset.name} is a leading company in the ${asset.sector} sector, listed on the ${asset.market}. This East African company represents a significant investment opportunity in the regional market.`;
  }

  calculatePERatio(asset) {
    // Simulated P/E ratios based on sector
    const sectorPE = {
      'Banking': 12.5,
      'Telecommunications': 18.2,
      'Utilities': 15.8,
      'Consumer Goods': 22.1,
      'Manufacturing': 16.4
    };
    return sectorPE[asset.sector] || 15.0;
  }

  calculateDividendYield(asset) {
    // Simulated dividend yields based on sector
    const sectorYield = {
      'Banking': '6.2%',
      'Telecommunications': '4.8%',
      'Utilities': '7.1%',
      'Consumer Goods': '3.5%',
      'Manufacturing': '5.2%'
    };
    return sectorYield[asset.sector] || '4.5%';
  }

  calculateEPS(asset) {
    // Simulated EPS based on price and P/E
    const pe = this.calculatePERatio(asset);
    return (asset.price / pe).toFixed(2);
  }

  calculateBookValue(asset) {
    // Simulated book value (typically lower than market price)
    return (asset.price * 0.8).toFixed(2);
  }

  generatePriceHistory(currentPrice, period) {
    const periods = {
      '1W': 7,
      '1M': 30,
      '3M': 90,
      '6M': 180,
      '1Y': 365
    };

    const days = periods[period] || 30;
    const history = [];

    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Generate realistic price variation (±5% from current)
      const variation = (Math.random() - 0.5) * 0.1; // ±5%
      const price = currentPrice * (1 + variation);

      history.push({
        date: date.toISOString(),
        price: Math.max(price, currentPrice * 0.7), // Minimum 70% of current price
        volume: Math.floor(Math.random() * 100000) + 10000
      });
    }

    return history;
  }

  generateAssetNews(asset) {
    const newsTemplates = [
      {
        title: `${asset.name} Reports Strong Q3 Performance`,
        snippet: `${asset.name} has announced robust quarterly results, showing continued growth in the ${asset.sector} sector across East African markets.`,
        date: new Date(Date.now() - 86400000 * 2).toISOString() // 2 days ago
      },
      {
        title: `${asset.market} Market Update: ${asset.sector} Sector Analysis`,
        snippet: `Latest analysis of the ${asset.sector} sector on ${asset.market} shows positive trends with companies like ${asset.name} leading the way.`,
        date: new Date(Date.now() - 86400000 * 5).toISOString() // 5 days ago
      },
      {
        title: `East African Investment Outlook: Focus on ${asset.sector}`,
        snippet: `Investment experts highlight opportunities in East African ${asset.sector} companies, with ${asset.name} among the top picks for regional investors.`,
        date: new Date(Date.now() - 86400000 * 7).toISOString() // 1 week ago
      }
    ];

    return newsTemplates;
  }
}

export default new MarketDataService();
