/**
 * Predictive Analytics Service
 * Provides intelligent forecasting, budget suggestions, and spending predictions
 * Built upon the Smart Transaction Categorization infrastructure
 */

import { supabase } from './supabaseClient';

class PredictiveAnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes for predictions
    this.isInitialized = false;
  }

  /**
   * Initialize predictive analytics service
   */
  async initialize(userId) {
    try {
      console.log('🔮 Initializing predictive analytics service for user:', userId);
      this.userId = userId;
      this.isInitialized = true;
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing predictive analytics:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate spending forecasts based on historical data
   */
  async generateSpendingForecast(userId, period = 'month', forecastMonths = 3) {
    try {
      console.log('🔮 Generating spending forecast for user:', userId);

      const cacheKey = `forecast_${userId}_${period}_${forecastMonths}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // Get historical transaction data (last 12 months for better prediction)
      const historicalData = await this.getHistoricalSpendingData(userId, 12);
      
      if (!historicalData.success || !historicalData.data.length) {
        return {
          success: true,
          data: {
            forecasts: [],
            confidence: 0,
            trends: [],
            seasonality: {},
            totalPredicted: 0
          }
        };
      }

      // Analyze spending patterns
      const patterns = this.analyzeSpendingPatterns(historicalData.data);
      
      // Generate forecasts
      const forecasts = this.calculateSpendingForecasts(patterns, forecastMonths);
      
      // Calculate confidence intervals
      const confidence = this.calculateForecastConfidence(patterns, historicalData.data);

      const result = {
        success: true,
        data: {
          forecasts,
          confidence,
          trends: patterns.trends,
          seasonality: patterns.seasonality,
          totalPredicted: forecasts.reduce((sum, f) => sum + f.predicted, 0),
          generatedAt: new Date().toISOString()
        }
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ Error generating spending forecast:', error);
      return {
        success: false,
        error: error.message,
        data: {
          forecasts: [],
          confidence: 0,
          trends: [],
          seasonality: {},
          totalPredicted: 0
        }
      };
    }
  }

  /**
   * Create intelligent budget suggestions
   */
  async generateBudgetSuggestions(userId, targetSavingsRate = 0.2) {
    try {
      console.log('💡 Generating budget suggestions for user:', userId);

      const cacheKey = `budget_suggestions_${userId}_${targetSavingsRate}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // Get user's financial data
      const [incomeData, spendingData, goalsData] = await Promise.all([
        this.getIncomeAnalysis(userId),
        this.getSpendingAnalysis(userId),
        this.getFinancialGoals(userId)
      ]);

      if (!incomeData.success || !spendingData.success) {
        return {
          success: true,
          data: {
            suggestedBudgets: [],
            totalBudget: 0,
            savingsAllocation: 0,
            recommendations: []
          }
        };
      }

      // Calculate budget suggestions
      const suggestions = this.calculateBudgetSuggestions(
        incomeData.data,
        spendingData.data,
        goalsData.data || [],
        targetSavingsRate
      );

      const result = {
        success: true,
        data: suggestions
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ Error generating budget suggestions:', error);
      return {
        success: false,
        error: error.message,
        data: {
          suggestedBudgets: [],
          totalBudget: 0,
          savingsAllocation: 0,
          recommendations: []
        }
      };
    }
  }

  /**
   * Analyze spending patterns for predictions
   */
  analyzeSpendingPatterns(transactions) {
    try {
      const safeTransactions = Array.isArray(transactions) ? transactions : [];
      
      // Group by month and category
      const monthlyData = this.groupTransactionsByMonth(safeTransactions);
      const categoryData = this.groupTransactionsByCategory(safeTransactions);
      
      // Calculate trends
      const trends = this.calculateTrends(monthlyData);
      
      // Detect seasonality
      const seasonality = this.detectSeasonality(monthlyData);
      
      // Calculate volatility
      const volatility = this.calculateVolatility(monthlyData);

      return {
        trends,
        seasonality,
        volatility,
        monthlyData,
        categoryData,
        averageMonthlySpending: this.calculateAverageMonthlySpending(monthlyData)
      };
    } catch (error) {
      console.error('❌ Error analyzing spending patterns:', error);
      return {
        trends: [],
        seasonality: {},
        volatility: 0,
        monthlyData: {},
        categoryData: {},
        averageMonthlySpending: 0
      };
    }
  }

  /**
   * Calculate spending forecasts based on patterns
   */
  calculateSpendingForecasts(patterns, forecastMonths) {
    try {
      const forecasts = [];
      const currentDate = new Date();
      
      for (let i = 1; i <= forecastMonths; i++) {
        const forecastDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
        const monthKey = `${forecastDate.getFullYear()}-${String(forecastDate.getMonth() + 1).padStart(2, '0')}`;
        
        // Base prediction on average spending
        let basePrediction = patterns.averageMonthlySpending || 0;
        
        // Apply trend adjustment
        if (patterns.trends.length > 0) {
          const latestTrend = patterns.trends[patterns.trends.length - 1];
          basePrediction *= (1 + (latestTrend.growthRate || 0) * i);
        }
        
        // Apply seasonal adjustment
        const month = forecastDate.getMonth() + 1;
        const seasonalMultiplier = patterns.seasonality[month] || 1;
        basePrediction *= seasonalMultiplier;
        
        // Calculate confidence bounds
        const volatility = patterns.volatility || 0.1;
        const lowerBound = basePrediction * (1 - volatility);
        const upperBound = basePrediction * (1 + volatility);
        
        forecasts.push({
          month: monthKey,
          date: forecastDate.toISOString(),
          predicted: Math.round(basePrediction),
          lowerBound: Math.round(lowerBound),
          upperBound: Math.round(upperBound),
          confidence: Math.max(0.5, 1 - volatility) // Higher volatility = lower confidence
        });
      }
      
      return forecasts;
    } catch (error) {
      console.error('❌ Error calculating forecasts:', error);
      return [];
    }
  }

  /**
   * Get historical spending data
   */
  async getHistoricalSpendingData(userId, months = 12) {
    try {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);
      
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, category, description, merchant_name, created_at')
        .eq('user_id', userId)
        .lt('amount', 0) // Only expenses
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true });

      if (error) {
        console.warn('⚠️ Error fetching historical data:', error);
        return { success: false, error: error.message, data: [] };
      }

      return { success: true, data: transactions || [] };
    } catch (error) {
      console.error('❌ Error getting historical data:', error);
      return { success: false, error: error.message, data: [] };
    }
  }

  /**
   * Group transactions by month
   */
  groupTransactionsByMonth(transactions) {
    const monthlyData = {};
    
    transactions.forEach(tx => {
      try {
        if (!tx || !tx.created_at || typeof tx.amount !== 'number') return;
        
        const date = new Date(tx.created_at);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = {
            total: 0,
            count: 0,
            categories: {}
          };
        }
        
        const amount = Math.abs(tx.amount);
        monthlyData[monthKey].total += amount;
        monthlyData[monthKey].count += 1;
        
        const category = tx.category || 'other';
        monthlyData[monthKey].categories[category] = (monthlyData[monthKey].categories[category] || 0) + amount;
      } catch (error) {
        console.warn('⚠️ Error processing transaction for monthly grouping:', error);
      }
    });
    
    return monthlyData;
  }

  /**
   * Group transactions by category
   */
  groupTransactionsByCategory(transactions) {
    const categoryData = {};
    
    transactions.forEach(tx => {
      try {
        if (!tx || typeof tx.amount !== 'number') return;
        
        const category = tx.category || 'other';
        const amount = Math.abs(tx.amount);
        
        if (!categoryData[category]) {
          categoryData[category] = {
            total: 0,
            count: 0,
            average: 0,
            transactions: []
          };
        }
        
        categoryData[category].total += amount;
        categoryData[category].count += 1;
        categoryData[category].transactions.push(tx);
      } catch (error) {
        console.warn('⚠️ Error processing transaction for category grouping:', error);
      }
    });
    
    // Calculate averages
    Object.keys(categoryData).forEach(category => {
      const data = categoryData[category];
      data.average = data.count > 0 ? data.total / data.count : 0;
    });
    
    return categoryData;
  }

  /**
   * Calculate spending trends
   */
  calculateTrends(monthlyData) {
    try {
      const months = Object.keys(monthlyData).sort();
      if (months.length < 2) return [];
      
      const trends = [];
      
      for (let i = 1; i < months.length; i++) {
        const currentMonth = monthlyData[months[i]];
        const previousMonth = monthlyData[months[i - 1]];
        
        if (currentMonth && previousMonth && previousMonth.total > 0) {
          const growthRate = (currentMonth.total - previousMonth.total) / previousMonth.total;
          
          trends.push({
            month: months[i],
            growthRate,
            amount: currentMonth.total,
            previousAmount: previousMonth.total,
            change: currentMonth.total - previousMonth.total
          });
        }
      }
      
      return trends;
    } catch (error) {
      console.error('❌ Error calculating trends:', error);
      return [];
    }
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Detect seasonality patterns
   */
  detectSeasonality(monthlyData) {
    try {
      const months = Object.keys(monthlyData).sort();
      const seasonalData = {};

      // Group by month number (1-12)
      months.forEach(monthKey => {
        const monthNum = parseInt(monthKey.split('-')[1]);
        const amount = monthlyData[monthKey].total;

        if (!seasonalData[monthNum]) {
          seasonalData[monthNum] = [];
        }
        seasonalData[monthNum].push(amount);
      });

      // Calculate seasonal multipliers
      const overallAverage = months.reduce((sum, month) => sum + monthlyData[month].total, 0) / months.length;
      const seasonality = {};

      for (let month = 1; month <= 12; month++) {
        if (seasonalData[month] && seasonalData[month].length > 0) {
          const monthAverage = seasonalData[month].reduce((sum, val) => sum + val, 0) / seasonalData[month].length;
          seasonality[month] = overallAverage > 0 ? monthAverage / overallAverage : 1;
        } else {
          seasonality[month] = 1;
        }
      }

      return seasonality;
    } catch (error) {
      console.error('❌ Error detecting seasonality:', error);
      return {};
    }
  }

  /**
   * Calculate spending volatility
   */
  calculateVolatility(monthlyData) {
    try {
      const amounts = Object.values(monthlyData).map(data => data.total);
      if (amounts.length < 2) return 0.1; // Default volatility

      const mean = amounts.reduce((sum, val) => sum + val, 0) / amounts.length;
      const variance = amounts.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / amounts.length;
      const standardDeviation = Math.sqrt(variance);

      return mean > 0 ? standardDeviation / mean : 0.1;
    } catch (error) {
      console.error('❌ Error calculating volatility:', error);
      return 0.1;
    }
  }

  /**
   * Calculate average monthly spending
   */
  calculateAverageMonthlySpending(monthlyData) {
    try {
      const amounts = Object.values(monthlyData).map(data => data.total);
      return amounts.length > 0 ? amounts.reduce((sum, val) => sum + val, 0) / amounts.length : 0;
    } catch (error) {
      console.error('❌ Error calculating average monthly spending:', error);
      return 0;
    }
  }

  /**
   * Calculate forecast confidence
   */
  calculateForecastConfidence(patterns, historicalData) {
    try {
      const dataPoints = Array.isArray(historicalData) ? historicalData.length : 0;
      const volatility = patterns.volatility || 0.1;

      // Base confidence on data availability and volatility
      let confidence = 0.5; // Base confidence

      // More data points = higher confidence
      if (dataPoints > 100) confidence += 0.3;
      else if (dataPoints > 50) confidence += 0.2;
      else if (dataPoints > 20) confidence += 0.1;

      // Lower volatility = higher confidence
      confidence += Math.max(0, (0.2 - volatility) * 2);

      return Math.min(0.95, Math.max(0.1, confidence));
    } catch (error) {
      console.error('❌ Error calculating forecast confidence:', error);
      return 0.5;
    }
  }

  /**
   * Get income analysis
   */
  async getIncomeAnalysis(userId) {
    try {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6); // Last 6 months

      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, description, created_at')
        .eq('user_id', userId)
        .gt('amount', 0) // Only income
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true });

      if (error) {
        console.warn('⚠️ Error fetching income data:', error);
        return { success: false, error: error.message, data: null };
      }

      const safeTransactions = Array.isArray(transactions) ? transactions : [];
      const monthlyIncome = this.groupTransactionsByMonth(safeTransactions);
      const averageIncome = this.calculateAverageMonthlySpending(monthlyIncome);

      return {
        success: true,
        data: {
          monthlyIncome,
          averageIncome,
          totalIncome: safeTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0),
          transactionCount: safeTransactions.length
        }
      };
    } catch (error) {
      console.error('❌ Error getting income analysis:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  /**
   * Get spending analysis
   */
  async getSpendingAnalysis(userId) {
    try {
      const sixMonthsAgo = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString();
      const now = new Date().toISOString();

      // Direct database query to avoid circular dependency
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, category, created_at')
        .eq('user_id', userId)
        .gte('created_at', sixMonthsAgo)
        .lte('created_at', now)
        .lt('amount', 0); // Only spending transactions

      if (error) {
        throw error;
      }

      // Group by category
      const categorySpending = {};
      transactions.forEach(transaction => {
        const category = transaction.category || 'uncategorized';
        if (!categorySpending[category]) {
          categorySpending[category] = 0;
        }
        categorySpending[category] += Math.abs(transaction.amount);
      });

      return {
        success: true,
        data: categorySpending
      };
    } catch (error) {
      console.error('❌ Error getting spending analysis:', error);
      return { success: false, error: error.message, data: null };
    }
  }

  /**
   * Get financial goals
   */
  async getFinancialGoals(userId) {
    try {
      const { data: goals, error } = await supabase
        .from('financial_goals')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        console.warn('⚠️ Error fetching financial goals:', error);
        return { success: false, error: error.message, data: [] };
      }

      return { success: true, data: goals || [] };
    } catch (error) {
      console.error('❌ Error getting financial goals:', error);
      return { success: false, error: error.message, data: [] };
    }
  }

  /**
   * Calculate budget suggestions
   */
  calculateBudgetSuggestions(incomeData, spendingData, goals, targetSavingsRate) {
    try {
      const monthlyIncome = incomeData.averageIncome || 0;
      const totalSpending = spendingData.totalAmount || 0;
      const categories = Array.isArray(spendingData.categories) ? spendingData.categories : [];

      // Calculate target savings
      const targetSavings = monthlyIncome * targetSavingsRate;
      const availableForSpending = monthlyIncome - targetSavings;

      // Generate category budgets based on historical spending
      const suggestedBudgets = categories.map(category => {
        const historicalPercentage = totalSpending > 0 ? category.amount / totalSpending : 0;
        const suggestedAmount = availableForSpending * historicalPercentage;

        return {
          categoryId: category.id,
          categoryName: category.name,
          icon: category.icon,
          color: category.color,
          suggestedAmount: Math.round(suggestedAmount),
          historicalAmount: category.amount,
          percentage: historicalPercentage * 100,
          priority: this.calculateCategoryPriority(category)
        };
      });

      // Sort by priority and amount
      suggestedBudgets.sort((a, b) => b.priority - a.priority || b.suggestedAmount - a.suggestedAmount);

      const recommendations = this.generateBudgetRecommendations(
        monthlyIncome,
        totalSpending,
        targetSavings,
        suggestedBudgets,
        goals
      );

      return {
        suggestedBudgets,
        totalBudget: availableForSpending,
        savingsAllocation: targetSavings,
        monthlyIncome,
        recommendations,
        targetSavingsRate: targetSavingsRate * 100
      };
    } catch (error) {
      console.error('❌ Error calculating budget suggestions:', error);
      return {
        suggestedBudgets: [],
        totalBudget: 0,
        savingsAllocation: 0,
        recommendations: []
      };
    }
  }

  /**
   * Calculate category priority for budgeting
   */
  calculateCategoryPriority(category) {
    const essentialCategories = ['food_dining', 'bills_utilities', 'transportation', 'healthcare'];
    const categoryId = category.id || category.categoryId || '';

    if (essentialCategories.includes(categoryId)) return 3; // High priority
    if (categoryId.includes('education') || categoryId.includes('personal_care')) return 2; // Medium priority
    return 1; // Low priority
  }

  /**
   * Generate budget recommendations
   */
  generateBudgetRecommendations(income, spending, savings, budgets, goals) {
    const recommendations = [];

    try {
      // Income vs spending analysis
      if (spending > income * 0.8) {
        recommendations.push({
          type: 'warning',
          title: 'High Spending Alert',
          message: 'Your spending is over 80% of your income. Consider reducing expenses.',
          priority: 'high',
          action: 'reduce_spending'
        });
      }

      // Savings rate analysis
      const currentSavingsRate = income > 0 ? (income - spending) / income : 0;
      if (currentSavingsRate < 0.1) {
        recommendations.push({
          type: 'suggestion',
          title: 'Increase Savings',
          message: 'Try to save at least 10% of your income for financial security.',
          priority: 'medium',
          action: 'increase_savings'
        });
      }

      // Category-specific recommendations
      const highSpendingCategories = budgets.filter(b => b.percentage > 30);
      highSpendingCategories.forEach(category => {
        recommendations.push({
          type: 'insight',
          title: `High ${category.categoryName} Spending`,
          message: `${category.categoryName} represents ${category.percentage.toFixed(1)}% of your spending.`,
          priority: 'low',
          action: 'review_category',
          categoryId: category.categoryId
        });
      });

      return recommendations;
    } catch (error) {
      console.error('❌ Error generating recommendations:', error);
      return [];
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }
}

export default new PredictiveAnalyticsService();
