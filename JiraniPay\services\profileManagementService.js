/**
 * Profile Management Service for JiraniPay
 * 
 * Comprehensive service for managing user profiles, KYC verification,
 * security settings, and privacy controls.
 */

import supabase from './supabaseClient';
import enhancedNetworkService from './enhancedNetworkService';
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as SecureStore from 'expo-secure-store';
import authService from './authService';
import schemaDetector from '../utils/databaseSchemaDetector';

// Base64 decode function for file uploads
function decodeBase64ToUint8Array(base64String) {
  return Uint8Array.from(atob(base64String), c => c.charCodeAt(0));
}

class ProfileManagementService {
  constructor() {
    this.tableName = {
      userProfiles: 'profiles', // FIXED: Use correct table name from schema-essential.sql
      kycDocuments: 'kyc_documents',
      kycVerificationLevels: 'kyc_verification_levels',
      securitySettings: 'security_settings',
      privacySettings: 'privacy_settings',
      profileCompletionSteps: 'profile_completion_steps',
      notificationPreferences: 'notification_preferences',
      supportTickets: 'support_tickets',
      userDevices: 'user_devices',
    };

    // Profile completion steps configuration
    this.completionSteps = {
      basic_info: { weight: 20, required_for: 'basic', label: 'Basic Information' },
      profile_picture: { weight: 10, required_for: 'basic', label: 'Profile Picture' },
      phone_verification: { weight: 15, required_for: 'basic', label: 'Phone Verification' },
      email_verification: { weight: 10, required_for: 'standard', label: 'Email Verification' },
      kyc_basic: { weight: 25, required_for: 'standard', label: 'Basic KYC' },
      kyc_standard: { weight: 15, required_for: 'premium', label: 'Standard KYC' },
      security_setup: { weight: 5, required_for: 'basic', label: 'Security Setup' },
    };

    // KYC verification levels and limits (UGX)
    this.verificationLevels = {
      basic: {
        daily_limit: 100000,
        monthly_limit: 3000000,
        annual_limit: 36000000,
        required_documents: [],
        features: ['basic_transfers', 'bill_payments', 'airtime_purchase']
      },
      standard: {
        daily_limit: 500000,
        monthly_limit: 15000000,
        annual_limit: 180000000,
        required_documents: ['national_id'],
        features: ['basic_transfers', 'bill_payments', 'airtime_purchase', 'international_transfers', 'loans']
      },
      premium: {
        daily_limit: 2000000,
        monthly_limit: 60000000,
        annual_limit: 720000000,
        required_documents: ['national_id', 'utility_bill'],
        features: ['all_features', 'priority_support', 'investment_products']
      }
    };
  }

  // =====================================================
  // PROFILE COMPLETION TRACKING
  // =====================================================

  /**
   * Get user's profile completion status
   */
  async getProfileCompletionStatus(userId) {
    try {
      console.log('📊 Getting profile completion status for user:', userId);

      const { data, error } = await supabase
        .from(this.tableName.profileCompletionSteps)
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;

      const allSteps = data || [];
      const completedSteps = allSteps.filter(step => step.completed === true);

      // Calculate weighted completion percentage
      const totalWeight = Object.values(this.completionSteps).reduce((sum, step) => sum + step.weight, 0);
      const completedWeight = completedSteps
        .reduce((sum, step) => sum + (this.completionSteps[step.step_name]?.weight || 0), 0);

      const completionPercentage = totalWeight > 0 ? Math.round((completedWeight / totalWeight) * 100) : 0;

      // Get next required step
      const nextStep = this.getNextRequiredStep(completedSteps);

      const status = {
        completionPercentage,
        completedSteps,
        pendingSteps: Object.keys(this.completionSteps).filter(stepName =>
          !completedSteps.find(step => step.step_name === stepName)
        ),
        totalSteps: Object.keys(this.completionSteps).length,
        completedCount: completedSteps.length,
        isComplete: completedSteps.length === Object.keys(this.completionSteps).length,
        nextStep
      };

      console.log('✅ Profile completion status calculated:', status);
      return { success: true, data: status };
    } catch (error) {
      console.error('❌ Error getting profile completion status:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user profile (PRODUCTION FIX)
   */
  async getProfile(userId) {
    try {
      console.log('📋 Getting profile for user:', userId);

      // Query the profiles table directly using schema-essential.sql structure
      const { data, error } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .select('*')
        .eq('id', userId) // id field directly references auth.users(id)
        .single();

      if (error) {
        // Handle case where profile doesn't exist
        if (error.code === 'PGRST116') {
          console.log('⚠️ User profile not found for user:', userId);
          return {
            success: true,
            data: null,
            code: 'PROFILE_NOT_FOUND'
          };
        }

        console.error('❌ Database error getting profile:', error);
        throw error;
      }

      console.log('✅ User profile found:', {
        id: data.id,
        fullName: data.full_name,
        phone: data.phone,
        email: data.email,
        avatarUrl: data.avatar_url
      });

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting profile:', error);
      return { success: false, error: error.message };
    }
  }



  /**
   * Get next required step for profile completion
   */
  getNextRequiredStep(completedSteps) {
    const completedStepNames = completedSteps.map(step => step.step_name);
    const allStepNames = Object.keys(this.completionSteps);

    for (const stepName of allStepNames) {
      if (!completedStepNames.includes(stepName)) {
        return {
          step_name: stepName,
          ...this.completionSteps[stepName]
        };
      }
    }

    return null; // All steps completed
  }

  /**
   * Initialize baseline completion steps for new users
   */
  async initializeBaselineSteps(userId) {
    try {
      console.log('🔄 Initializing baseline completion steps for user:', userId);

      // Mark phone verification as completed (since user registered successfully)
      await this.markStepCompleted(userId, 'phone_verification');

      console.log('✅ Baseline completion steps initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing baseline steps:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Mark a profile completion step as completed
   */
  async markStepCompleted(userId, stepName) {
    try {
      // First check if the step already exists
      const { data: existingStep } = await supabase
        .from(this.tableName.profileCompletionSteps)
        .select('*')
        .eq('user_id', userId)
        .eq('step_name', stepName)
        .single();

      if (existingStep) {
        // Update existing step
        const { data, error } = await supabase
          .from(this.tableName.profileCompletionSteps)
          .update({
            completed: true,
            completed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('step_name', stepName)
          .select()
          .single();

        if (error) throw error;
        console.log(`✅ Profile step '${stepName}' updated as completed`);
        return { success: true, data };
      } else {
        // Insert new step
        const { data, error } = await supabase
          .from(this.tableName.profileCompletionSteps)
          .insert({
            user_id: userId,
            step_name: stepName,
            completed: true,
            completed_at: new Date().toISOString(),
            weight: this.completionSteps[stepName]?.weight || 1,
            required_for_level: this.completionSteps[stepName]?.required_for || 'basic'
          })
          .select()
          .single();

        if (error) throw error;
        console.log(`✅ Profile step '${stepName}' marked as completed`);
        return { success: true, data };
      }
    } catch (error) {
      console.error(`❌ Error marking step '${stepName}' as completed:`, error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // PROFILE DATA MANAGEMENT
  // =====================================================

  /**
   * Update user profile with validation (PRODUCTION FIX)
   */
  async updateProfile(userId, profileData) {
    try {
      // Validate required fields
      const validationResult = this.validateProfileData(profileData);
      if (!validationResult.isValid) {
        return { success: false, error: validationResult.errors.join(', ') };
      }

      console.log('🔄 Updating profile for user:', userId);
      console.log('📝 Profile data:', profileData);

      // ✅ FIX: Map field names to match database schema
      const mappedData = this.mapProfileFieldsForDatabase(profileData);

      // PRODUCTION FIX: Use UPSERT with schema-essential.sql structure
      const upsertData = {
        id: userId, // Primary key that references auth.users(id)
        ...mappedData,
        updated_at: new Date().toISOString()
      };

      console.log('📝 Mapped data for database:', upsertData);

      const { data, error } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .upsert(upsertData)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error:', error);

        // ✅ FIX: Handle specific schema errors
        if (error.code === 'PGRST204' && error.message.includes('date_of_birth')) {
          console.error('❌ Schema error: date_of_birth column missing');
          throw new Error('Profile update failed: Database schema needs to be updated. Please run the schema fix script.');
        }

        if (error.code === 'PGRST204') {
          console.error('❌ Schema error: Column not found -', error.message);
          throw new Error('Profile update failed: Database schema mismatch. Please run the schema fix script.');
        }

        throw error;
      }

      console.log('✅ Profile upserted successfully:', data);

      // Initialize baseline steps for new users (phone verification)
      await this.initializeBaselineSteps(userId);

      // Mark basic info as completed if all required fields are present
      if (profileData.full_name && profileData.phone_number) {
        await this.markStepCompleted(userId, 'basic_info');
      }

      console.log('✅ Profile updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Map profile fields to match database schema
   * @param {Object} profileData - Profile data from UI
   * @returns {Object} - Mapped data for database
   */
  mapProfileFieldsForDatabase(profileData) {
    const mapped = {};

    // Map field names to database schema
    if (profileData.full_name !== undefined) mapped.full_name = profileData.full_name;
    if (profileData.email !== undefined) mapped.email = profileData.email;
    if (profileData.country_code !== undefined) mapped.country_code = profileData.country_code;
    if (profileData.date_of_birth !== undefined) mapped.date_of_birth = profileData.date_of_birth;
    if (profileData.gender !== undefined) mapped.gender = profileData.gender;
    if (profileData.address !== undefined) mapped.address = profileData.address;

    // Handle language preference field mapping (both possible field names)
    if (profileData.language_preference !== undefined) mapped.language_preference = profileData.language_preference;
    if (profileData.preferred_language !== undefined) mapped.language_preference = profileData.preferred_language;

    // Handle currency preference field mapping
    if (profileData.currency_preference !== undefined) mapped.currency_preference = profileData.currency_preference;
    if (profileData.preferred_currency !== undefined) mapped.currency_preference = profileData.preferred_currency;

    // Handle phone number field mapping
    if (profileData.phone_number !== undefined) {
      mapped.phone = profileData.phone_number; // Map to 'phone' column
      mapped.phone_number = profileData.phone_number; // Also set phone_number if it exists
    }

    // Handle avatar URL
    if (profileData.avatar_url !== undefined) mapped.avatar_url = profileData.avatar_url;

    console.log('🔄 Field mapping:', {
      original: Object.keys(profileData),
      mapped: Object.keys(mapped)
    });

    console.log('🔍 Language preference mapping:', {
      input_language_preference: profileData.language_preference,
      input_preferred_language: profileData.preferred_language,
      mapped_language_preference: mapped.language_preference
    });

    console.log('🔍 Currency preference mapping:', {
      input_currency_preference: profileData.currency_preference,
      input_preferred_currency: profileData.preferred_currency,
      mapped_currency_preference: mapped.currency_preference
    });

    return mapped;
  }

  /**
   * Validate profile data with comprehensive checks
   */
  validateProfileData(profileData) {
    const errors = [];

    // Full name validation
    if (!profileData.full_name || typeof profileData.full_name !== 'string') {
      errors.push('Full name is required');
    } else if (profileData.full_name.trim().length < 2) {
      errors.push('Full name must be at least 2 characters');
    } else if (profileData.full_name.trim().length > 100) {
      errors.push('Full name must be less than 100 characters');
    } else if (!/^[a-zA-Z\s'-]+$/.test(profileData.full_name.trim())) {
      errors.push('Full name can only contain letters, spaces, hyphens, and apostrophes');
    }

    // Phone number validation (Uganda format)
    if (!profileData.phone_number || typeof profileData.phone_number !== 'string') {
      errors.push('Phone number is required');
    } else {
      const cleanPhone = profileData.phone_number.replace(/\s/g, '');
      const ugandaPhoneRegex = /^(\+256|0)?[7][0-9]{8}$/;
      if (!ugandaPhoneRegex.test(cleanPhone)) {
        errors.push('Please enter a valid Uganda phone number');
      }
    }

    // Email validation (optional but must be valid if provided)
    if (profileData.email && profileData.email.trim() !== '') {
      if (!this.isValidEmail(profileData.email.trim())) {
        errors.push('Please enter a valid email address');
      } else if (profileData.email.trim().length > 255) {
        errors.push('Email address must be less than 255 characters');
      }
    }

    // Date of birth validation (optional but must be reasonable if provided)
    if (profileData.date_of_birth) {
      const dob = new Date(profileData.date_of_birth);

      if (isNaN(dob.getTime())) {
        errors.push('Invalid date of birth format');
      } else {
        const age = this.calculateAge(dob);
        const today = new Date();

        if (dob > today) {
          errors.push('Date of birth cannot be in the future');
        } else if (age < 13) {
          errors.push('You must be at least 13 years old to use this service');
        } else if (age > 120) {
          errors.push('Please enter a valid date of birth');
        }
      }
    }

    // Country code validation - Support all East African countries
    const supportedCountries = ['UG', 'KE', 'TZ', 'RW', 'BI', 'SS', 'ET', 'SO', 'DJ', 'ER'];
    if (profileData.country_code && !supportedCountries.includes(profileData.country_code)) {
      errors.push('Please select a valid East African country');
    }

    // Language validation - Support all configured languages
    const supportedLanguages = ['en', 'sw', 'fr', 'ar', 'am', 'rw', 'rn'];
    if (profileData.preferred_language && !supportedLanguages.includes(profileData.preferred_language)) {
      errors.push('Please select a valid language');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Upload and process profile picture
   */
  async uploadProfilePicture(userId, imageUri) {
    console.log('🚨 UPLOAD FUNCTION CALLED - userId:', userId, 'imageUri:', imageUri);
    try {
      console.log('🔄 Starting profile picture upload for user:', userId);
      console.log('🔄 Image URI:', imageUri);

      // Compress and resize image
      console.log('🔄 Compressing and resizing image...');
      const manipulatedImage = await manipulateAsync(
        imageUri,
        [{ resize: { width: 400, height: 400 } }],
        { compress: 0.8, format: SaveFormat.JPEG }
      );
      console.log('✅ Image compressed successfully:', manipulatedImage.uri);

      // Read file as base64
      console.log('🔄 Reading image as base64...');
      const base64 = await FileSystem.readAsStringAsync(manipulatedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      console.log('✅ Image converted to base64, length:', base64.length);

      // Upload to Supabase Storage using enhanced network service
      const fileName = `profile-pictures/${userId}-${Date.now()}.jpg`;
      console.log('🔄 Uploading to Supabase storage with filename:', fileName);

      const uploadResult = await enhancedNetworkService.uploadFile(
        'user-uploads',
        fileName,
        decodeBase64ToUint8Array(base64),
        {
          contentType: 'image/jpeg',
          upsert: true
        }
      );

      if (!uploadResult.success) {
        console.error('❌ Enhanced network upload error:', uploadResult.error);
        // Fallback to direct Supabase upload
        const { data, error } = await supabase.storage
          .from('user-uploads')
          .upload(fileName, decodeBase64ToUint8Array(base64), {
            contentType: 'image/jpeg',
            upsert: true
          });

        if (error) {
          console.error('❌ Fallback Supabase storage upload error:', error);
          throw error;
        }
        console.log('✅ Fallback upload successful:', data);
      } else {
        console.log('✅ Enhanced network upload successful:', uploadResult.data);
      }

      // Get public URL
      console.log('🔄 Getting public URL...');
      const { data: urlData } = supabase.storage
        .from('user-uploads')
        .getPublicUrl(fileName);

      console.log('✅ Public URL generated:', urlData.publicUrl);

      // Update user profile with new picture URL directly in database
      console.log('🔄 Updating profile with picture URL:', urlData.publicUrl);

      // Update profile with new picture URL using schema-essential.sql structure
      const { data: profileData, error: profileError } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .update({
          avatar_url: urlData.publicUrl, // Use avatar_url field from schema-essential.sql
          updated_at: new Date().toISOString()
        })
        .eq('id', userId) // id field references auth.users(id)
        .select()
        .single();

      if (profileError) {
        console.error('❌ Failed to update profile with picture URL:', profileError);
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      console.log('✅ Profile updated with picture URL successfully:', profileData);

      // Mark profile picture step as completed
      await this.markStepCompleted(userId, 'profile_picture');

      console.log('✅ Profile picture uploaded successfully');
      return { success: true, data: { url: urlData.publicUrl } };
    } catch (error) {
      console.error('❌ Error uploading profile picture:', error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // KYC VERIFICATION SYSTEM
  // =====================================================

  /**
   * Get user's current KYC verification level
   */
  async getKYCVerificationLevel(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.kycVerificationLevels)
        .select('*')
        .eq('user_id', userId)
        .order('achieved_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      return {
        success: true,
        data: data || {
          verification_level: 'basic',
          daily_limit: this.verificationLevels.basic.daily_limit,
          monthly_limit: this.verificationLevels.basic.monthly_limit,
          annual_limit: this.verificationLevels.basic.annual_limit,
          features_enabled: this.verificationLevels.basic.features
        }
      };
    } catch (error) {
      console.error('❌ Error getting KYC verification level:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload KYC document
   */
  async uploadKYCDocument(userId, documentType, imageUri, documentNumber = null) {
    try {
      // Validate document type
      const validDocumentTypes = ['national_id', 'national_id_front', 'national_id_back', 'passport', 'driving_license', 'utility_bill'];
      if (!validDocumentTypes.includes(documentType)) {
        return { success: false, error: 'Invalid document type' };
      }

      // Process and upload image
      const manipulatedImage = await manipulateAsync(
        imageUri,
        [{ resize: { width: 1200 } }], // Higher resolution for documents
        { compress: 0.9, format: SaveFormat.JPEG }
      );

      const base64 = await FileSystem.readAsStringAsync(manipulatedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Upload to secure KYC folder
      const fileName = `kyc-documents/${userId}/${documentType}-${Date.now()}.jpg`;
      console.log('📤 Uploading to storage bucket: kyc-documents, file:', fileName);

      const { data, error } = await supabase.storage
        .from('kyc-documents')
        .upload(fileName, decodeBase64ToUint8Array(base64), {
          contentType: 'image/jpeg'
        });

      if (error) {
        console.error('❌ Storage upload error:', error);
        throw error;
      }

      console.log('✅ Storage upload successful:', data);

      // Save document record
      console.log('💾 Inserting document record to table:', this.tableName.kycDocuments);
      console.log('💾 Document data:', {
        user_id: userId,
        document_type: documentType,
        document_number: documentNumber,
        document_url: fileName,
        verification_status: 'pending',
        country_code: 'UG'
      });

      const { data: docData, error: docError } = await supabase
        .from(this.tableName.kycDocuments)
        .insert({
          user_id: userId,
          document_type: documentType,
          document_number: documentNumber,
          document_url: fileName,
          verification_status: 'pending',
          country_code: 'UG' // TODO: Get from user profile
        })
        .select()
        .single();

      if (docError) {
        console.error('❌ Database insert error:', docError);
        throw docError;
      }

      console.log('✅ Database insert successful:', docData);

      console.log(`✅ KYC document '${documentType}' uploaded successfully`);
      return { success: true, data: docData };
    } catch (error) {
      console.error(`❌ Error uploading KYC document '${documentType}':`, error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }
}

export default new ProfileManagementService();
