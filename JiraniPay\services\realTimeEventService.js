/**
 * Real-time Event Broadcasting Service
 * Implements WebSocket and Server-Sent Events for instant notification delivery
 * and real-time transaction status updates
 */

import { supabase } from './supabaseClient';
import enhancedNotificationService from './enhancedNotificationService';

// Event types for real-time broadcasting
const EVENT_TYPES = {
  TRANSACTION_STATUS_CHANGED: 'transaction_status_changed',
  MONEY_RECEIVED: 'money_received',
  PAYMENT_COMPLETED: 'payment_completed',
  SECURITY_ALERT: 'security_alert',
  FRAUD_ALERT: 'fraud_alert',
  BALANCE_UPDATED: 'balance_updated',
  NOTIFICATION_RECEIVED: 'notification_received',
  RECEIPT_GENERATED: 'receipt_generated',
  LIMIT_WARNING: 'limit_warning',
  VERIFICATION_UPDATE: 'verification_update'
};

class RealTimeEventService {
  constructor() {
    this.eventTypes = EVENT_TYPES;
    this.subscribers = new Map();
    this.connectionStatus = 'disconnected';
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.supabaseChannel = null;
    this.eventQueue = [];
    this.isProcessingQueue = false;
  }

  /**
   * Initialize real-time event service
   */
  async initialize(userId) {
    try {
      console.log('🔄 Initializing real-time event service for user:', userId);

      this.userId = userId;
      
      // Initialize Supabase real-time subscription
      await this.initializeSupabaseRealtime();

      // Start event queue processing
      this.startEventQueueProcessing();

      console.log('✅ Real-time event service initialized');
      
      return { success: true, status: 'connected' };
    } catch (error) {
      console.error('❌ Error initializing real-time service:', error);
      throw error;
    }
  }

  /**
   * Initialize Supabase real-time subscriptions
   */
  async initializeSupabaseRealtime() {
    try {
      // Subscribe to transaction changes
      this.supabaseChannel = supabase
        .channel(`user_events_${this.userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${this.userId}`
          },
          (payload) => this.handleTransactionChange(payload)
        )
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${this.userId}`
          },
          (payload) => this.handleNotificationInsert(payload)
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'security_events',
            filter: `user_id=eq.${this.userId}`
          },
          (payload) => this.handleSecurityEvent(payload)
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'user_profiles',
            filter: `user_id=eq.${this.userId}`
          },
          (payload) => this.handleProfileUpdate(payload)
        )
        .subscribe((status) => {
          console.log('📡 Supabase real-time status:', status);
          this.connectionStatus = status;
          
          if (status === 'SUBSCRIBED') {
            this.reconnectAttempts = 0;
            this.broadcastEvent('connection_status', { status: 'connected' });
          } else if (status === 'CHANNEL_ERROR') {
            this.handleConnectionError();
          }
        });

      console.log('✅ Supabase real-time subscriptions initialized');
    } catch (error) {
      console.error('❌ Error initializing Supabase real-time:', error);
      throw error;
    }
  }

  /**
   * Handle transaction changes
   */
  handleTransactionChange(payload) {
    try {
      console.log('💳 Transaction change detected:', payload.eventType);

      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      switch (eventType) {
        case 'INSERT':
          this.handleNewTransaction(newRecord);
          break;
          
        case 'UPDATE':
          this.handleTransactionUpdate(newRecord, oldRecord);
          break;
          
        default:
          console.log('📝 Unhandled transaction event:', eventType);
      }
    } catch (error) {
      console.error('❌ Error handling transaction change:', error);
    }
  }

  /**
   * Handle new transaction
   */
  handleNewTransaction(transaction) {
    try {
      // Determine event type based on transaction
      let eventType = this.eventTypes.PAYMENT_COMPLETED;
      
      if (transaction.type === 'receive_money' || transaction.type === 'money_received') {
        eventType = this.eventTypes.MONEY_RECEIVED;
      }

      // Broadcast event
      this.broadcastEvent(eventType, {
        transaction: {
          id: transaction.id,
          type: transaction.type,
          amount: transaction.amount,
          currency: transaction.currency,
          status: transaction.status,
          reference: transaction.reference,
          createdAt: transaction.created_at
        }
      });

      // Queue notification
      this.queueEvent({
        type: 'send_notification',
        data: {
          userId: this.userId,
          notificationType: transaction.type === 'receive_money' ? 'money_received' : 'transaction_completed',
          transactionData: transaction
        }
      });

    } catch (error) {
      console.error('❌ Error handling new transaction:', error);
    }
  }

  /**
   * Handle transaction status update
   */
  handleTransactionUpdate(newTransaction, oldTransaction) {
    try {
      // Check if status changed
      if (newTransaction.status !== oldTransaction.status) {
        console.log('📊 Transaction status changed:', {
          id: newTransaction.id,
          from: oldTransaction.status,
          to: newTransaction.status
        });

        // Broadcast status change
        this.broadcastEvent(this.eventTypes.TRANSACTION_STATUS_CHANGED, {
          transaction: {
            id: newTransaction.id,
            oldStatus: oldTransaction.status,
            newStatus: newTransaction.status,
            type: newTransaction.type,
            amount: newTransaction.amount,
            reference: newTransaction.reference
          }
        });

        // Send notification for status change
        if (newTransaction.status === 'completed' || newTransaction.status === 'failed') {
          this.queueEvent({
            type: 'send_notification',
            data: {
              userId: this.userId,
              notificationType: newTransaction.status === 'completed' ? 'transaction_completed' : 'transaction_failed',
              transactionData: newTransaction
            }
          });
        }

        // Generate receipt for completed transactions
        if (newTransaction.status === 'completed') {
          this.queueEvent({
            type: 'generate_receipt',
            data: {
              transactionId: newTransaction.id,
              userId: this.userId
            }
          });
        }
      }

      // Check for balance updates
      if (newTransaction.status === 'completed' && oldTransaction.status !== 'completed') {
        this.broadcastEvent(this.eventTypes.BALANCE_UPDATED, {
          transaction: {
            id: newTransaction.id,
            type: newTransaction.type,
            amount: newTransaction.amount,
            impact: this.calculateBalanceImpact(newTransaction)
          }
        });
      }

    } catch (error) {
      console.error('❌ Error handling transaction update:', error);
    }
  }

  /**
   * Handle notification insert
   */
  handleNotificationInsert(payload) {
    try {
      const notification = payload.new;
      
      console.log('🔔 New notification detected:', notification.type);

      // Broadcast notification event
      this.broadcastEvent(this.eventTypes.NOTIFICATION_RECEIVED, {
        notification: {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          content: notification.content,
          priority: notification.priority,
          createdAt: notification.created_at
        }
      });

    } catch (error) {
      console.error('❌ Error handling notification insert:', error);
    }
  }

  /**
   * Handle security events
   */
  handleSecurityEvent(payload) {
    try {
      const event = payload.new;
      
      console.log('🔒 Security event detected:', event.event_type);

      let eventType = this.eventTypes.SECURITY_ALERT;
      if (event.event_type === 'fraud_alert' || event.event_type === 'high_risk_transaction') {
        eventType = this.eventTypes.FRAUD_ALERT;
      }

      // Broadcast security event
      this.broadcastEvent(eventType, {
        securityEvent: {
          id: event.id,
          type: event.event_type,
          severity: event.severity,
          details: event.details,
          createdAt: event.created_at
        }
      });

    } catch (error) {
      console.error('❌ Error handling security event:', error);
    }
  }

  /**
   * Handle profile updates
   */
  handleProfileUpdate(payload) {
    try {
      const { new: newProfile, old: oldProfile } = payload;

      // Check for verification level changes
      if (newProfile.kyc_level !== oldProfile.kyc_level || 
          newProfile.kyc_status !== oldProfile.kyc_status) {
        
        console.log('✅ Verification status updated');

        this.broadcastEvent(this.eventTypes.VERIFICATION_UPDATE, {
          verification: {
            oldLevel: oldProfile.kyc_level,
            newLevel: newProfile.kyc_level,
            oldStatus: oldProfile.kyc_status,
            newStatus: newProfile.kyc_status
          }
        });

        // Send notification about verification update
        this.queueEvent({
          type: 'send_notification',
          data: {
            userId: this.userId,
            notificationType: 'account_verification',
            verificationData: {
              level: newProfile.kyc_level,
              status: newProfile.kyc_status
            }
          }
        });
      }

    } catch (error) {
      console.error('❌ Error handling profile update:', error);
    }
  }

  /**
   * Subscribe to real-time events
   */
  subscribe(eventType, callback) {
    try {
      if (!this.subscribers.has(eventType)) {
        this.subscribers.set(eventType, new Set());
      }
      
      this.subscribers.get(eventType).add(callback);
      
      console.log('📡 Subscribed to event:', eventType);
      
      return () => {
        this.unsubscribe(eventType, callback);
      };
    } catch (error) {
      console.error('❌ Error subscribing to event:', error);
      return () => {};
    }
  }

  /**
   * Unsubscribe from real-time events
   */
  unsubscribe(eventType, callback) {
    try {
      if (this.subscribers.has(eventType)) {
        this.subscribers.get(eventType).delete(callback);
        
        if (this.subscribers.get(eventType).size === 0) {
          this.subscribers.delete(eventType);
        }
      }
      
      console.log('📡 Unsubscribed from event:', eventType);
    } catch (error) {
      console.error('❌ Error unsubscribing from event:', error);
    }
  }

  /**
   * Broadcast event to subscribers
   */
  broadcastEvent(eventType, data) {
    try {
      console.log('📢 Broadcasting event:', eventType);

      if (this.subscribers.has(eventType)) {
        this.subscribers.get(eventType).forEach(callback => {
          try {
            callback({
              type: eventType,
              data,
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error('❌ Error in event callback:', error);
          }
        });
      }

      // Also broadcast to 'all' subscribers
      if (this.subscribers.has('all')) {
        this.subscribers.get('all').forEach(callback => {
          try {
            callback({
              type: eventType,
              data,
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error('❌ Error in all event callback:', error);
          }
        });
      }

    } catch (error) {
      console.error('❌ Error broadcasting event:', error);
    }
  }

  /**
   * Queue event for processing
   */
  queueEvent(event) {
    this.eventQueue.push({
      ...event,
      timestamp: new Date().toISOString(),
      id: crypto.randomUUID()
    });

    if (!this.isProcessingQueue) {
      this.processEventQueue();
    }
  }

  /**
   * Start event queue processing
   */
  startEventQueueProcessing() {
    setInterval(() => {
      if (!this.isProcessingQueue && this.eventQueue.length > 0) {
        this.processEventQueue();
      }
    }, 1000); // Process every second
  }

  /**
   * Process event queue
   */
  async processEventQueue() {
    if (this.isProcessingQueue || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift();
        await this.processQueuedEvent(event);
      }
    } catch (error) {
      console.error('❌ Error processing event queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Process individual queued event
   */
  async processQueuedEvent(event) {
    try {
      console.log('⚙️ Processing queued event:', event.type);

      switch (event.type) {
        case 'send_notification':
          await enhancedNotificationService.sendNotification(
            event.data.userId,
            {
              type: event.data.notificationType,
              title: this.getNotificationTitle(event.data.notificationType),
              content: this.getNotificationContent(event.data),
              data: event.data.transactionData || event.data.verificationData
            }
          );
          break;

        case 'generate_receipt':
          // Import receipt service dynamically to avoid circular dependency
          const { default: digitalReceiptService } = await import('./digitalReceiptService');
          const receipt = await digitalReceiptService.generateReceipt(
            event.data.transactionId,
            event.data.userId
          );
          
          // Broadcast receipt generated event
          this.broadcastEvent(this.eventTypes.RECEIPT_GENERATED, {
            receipt: {
              id: receipt.receipt.id,
              transactionId: event.data.transactionId,
              downloadUrl: receipt.receipt.downloadUrl
            }
          });
          break;

        default:
          console.log('⚠️ Unknown queued event type:', event.type);
      }

    } catch (error) {
      console.error('❌ Error processing queued event:', error);
    }
  }

  /**
   * Calculate balance impact of transaction
   */
  calculateBalanceImpact(transaction) {
    const amount = parseFloat(transaction.amount);
    const fee = parseFloat(transaction.fee || 0);

    switch (transaction.type) {
      case 'send_money':
      case 'bill_payment':
      case 'airtime_purchase':
      case 'withdrawal':
        return -(amount + fee); // Negative impact

      case 'receive_money':
      case 'deposit':
        return amount; // Positive impact

      default:
        return 0;
    }
  }

  /**
   * Get notification title for type
   */
  getNotificationTitle(type) {
    const titles = {
      transaction_completed: 'Transaction Successful',
      transaction_failed: 'Transaction Failed',
      money_received: 'Money Received',
      account_verification: 'Verification Update'
    };

    return titles[type] || 'JiraniPay Notification';
  }

  /**
   * Get notification content
   */
  getNotificationContent(data) {
    if (data.transactionData) {
      const tx = data.transactionData;
      return `${tx.type.replace('_', ' ')} of ${tx.currency} ${tx.amount} - Ref: ${tx.reference}`;
    }

    if (data.verificationData) {
      return `Your verification level has been updated to ${data.verificationData.level}`;
    }

    return 'You have a new notification';
  }

  /**
   * Handle connection errors
   */
  handleConnectionError() {
    console.log('🔄 Handling connection error, attempting reconnect...');

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      setTimeout(() => {
        this.reconnect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('❌ Max reconnection attempts reached');
      this.broadcastEvent('connection_status', { 
        status: 'failed', 
        error: 'Max reconnection attempts reached' 
      });
    }
  }

  /**
   * Reconnect to real-time service
   */
  async reconnect() {
    try {
      console.log('🔄 Attempting to reconnect...');

      if (this.supabaseChannel) {
        await supabase.removeChannel(this.supabaseChannel);
      }

      await this.initializeSupabaseRealtime();
      
      console.log('✅ Reconnected successfully');
    } catch (error) {
      console.error('❌ Reconnection failed:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Disconnect from real-time service
   */
  async disconnect() {
    try {
      console.log('🔌 Disconnecting real-time service...');

      if (this.supabaseChannel) {
        await supabase.removeChannel(this.supabaseChannel);
        this.supabaseChannel = null;
      }

      this.subscribers.clear();
      this.eventQueue.length = 0;
      this.connectionStatus = 'disconnected';

      console.log('✅ Real-time service disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting real-time service:', error);
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      status: this.connectionStatus,
      subscriberCount: Array.from(this.subscribers.values())
        .reduce((total, set) => total + set.size, 0),
      queueLength: this.eventQueue.length,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

export default new RealTimeEventService();
