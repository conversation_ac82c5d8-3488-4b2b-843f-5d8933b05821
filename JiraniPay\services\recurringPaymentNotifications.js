/**
 * Recurring Payment Notifications Service
 * Specialized notification service for recurring payment events
 * including reminders, success/failure notifications, and status updates
 */

import enhancedNotificationService from './enhancedNotificationService';
import { supabase } from './supabaseClient';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate, formatDateTime } from '../utils/dateUtils';
import { isValidUUID } from '../utils/userUtils';

class RecurringPaymentNotifications {
  constructor() {
    this.notificationTypes = {
      REMINDER: 'recurring_payment_reminder',
      SUCCESS: 'recurring_payment_success',
      FAILURE: 'recurring_payment_failure',
      RETRY: 'recurring_payment_retry',
      PAUSED: 'recurring_payment_paused',
      RESUMED: 'recurring_payment_resumed',
      CANCELLED: 'recurring_payment_cancelled',
      CREATED: 'recurring_payment_created',
      UPDATED: 'recurring_payment_updated'
    };

    // Start reminder processor
    this.startReminderProcessor();
  }

  /**
   * Send payment reminder notification
   */
  async sendPaymentReminder(userId, recurringPayment, daysUntilPayment) {
    try {
      if (!userId || !isValidUUID(userId)) {
        console.error('❌ Invalid user ID for payment reminder');
        return { success: false, error: 'Invalid user ID' };
      }

      const notification = {
        type: this.notificationTypes.REMINDER,
        title: 'Upcoming Payment Reminder',
        content: this.generateReminderContent(recurringPayment, daysUntilPayment),
        data: {
          recurringPaymentId: recurringPayment.id,
          amount: recurringPayment.amount,
          currency: recurringPayment.currency,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          nextPaymentDate: recurringPayment.next_payment_date,
          daysUntilPayment
        },
        priority: 'normal',
        category: 'payment_reminder',
        actionButtons: [
          {
            id: 'view_payment',
            title: 'View Payment',
            action: 'navigate',
            data: { screen: 'RecurringPayments', params: { paymentId: recurringPayment.id } }
          },
          {
            id: 'pause_payment',
            title: 'Pause',
            action: 'api_call',
            data: { endpoint: 'pause_recurring_payment', paymentId: recurringPayment.id }
          }
        ]
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        // Log reminder sent
        await this.logNotificationSent(userId, recurringPayment.id, 'reminder', {
          daysUntilPayment,
          sentAt: new Date().toISOString()
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment reminder:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send payment success notification
   */
  async sendPaymentSuccess(userId, recurringPayment, paymentDetails) {
    try {
      const notification = {
        type: this.notificationTypes.SUCCESS,
        title: 'Payment Successful',
        content: this.generateSuccessContent(recurringPayment, paymentDetails),
        data: {
          recurringPaymentId: recurringPayment.id,
          paymentId: paymentDetails.paymentId,
          amount: paymentDetails.amount,
          currency: recurringPayment.currency,
          reference: paymentDetails.reference,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          nextPaymentDate: paymentDetails.nextPaymentDate
        },
        priority: 'normal',
        category: 'payment_success',
        actionButtons: [
          {
            id: 'view_receipt',
            title: 'View Receipt',
            action: 'navigate',
            data: { screen: 'PaymentReceipt', params: { paymentId: paymentDetails.paymentId } }
          }
        ]
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        await this.logNotificationSent(userId, recurringPayment.id, 'success', paymentDetails);
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment success notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailure(userId, recurringPayment, failureDetails) {
    try {
      const notification = {
        type: this.notificationTypes.FAILURE,
        title: 'Payment Failed',
        content: this.generateFailureContent(recurringPayment, failureDetails),
        data: {
          recurringPaymentId: recurringPayment.id,
          amount: recurringPayment.amount,
          currency: recurringPayment.currency,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          error: failureDetails.error,
          attempts: failureDetails.attempts,
          maxAttempts: failureDetails.maxAttempts || 3
        },
        priority: 'high',
        category: 'payment_failure',
        actionButtons: [
          {
            id: 'update_payment_method',
            title: 'Update Payment Method',
            action: 'navigate',
            data: { screen: 'PaymentMethods' }
          },
          {
            id: 'retry_payment',
            title: 'Retry Now',
            action: 'api_call',
            data: { endpoint: 'retry_recurring_payment', paymentId: recurringPayment.id }
          }
        ]
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        await this.logNotificationSent(userId, recurringPayment.id, 'failure', failureDetails);
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment failure notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send payment retry notification
   */
  async sendPaymentRetry(userId, recurringPayment, retryDetails) {
    try {
      const notification = {
        type: this.notificationTypes.RETRY,
        title: 'Payment Retry Scheduled',
        content: this.generateRetryContent(recurringPayment, retryDetails),
        data: {
          recurringPaymentId: recurringPayment.id,
          amount: recurringPayment.amount,
          currency: recurringPayment.currency,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          retryDate: retryDetails.retryDate,
          attempt: retryDetails.attempt,
          maxAttempts: retryDetails.maxAttempts
        },
        priority: 'normal',
        category: 'payment_retry'
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        await this.logNotificationSent(userId, recurringPayment.id, 'retry', retryDetails);
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment retry notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send payment paused notification
   */
  async sendPaymentPaused(userId, recurringPayment, pauseReason) {
    try {
      const notification = {
        type: this.notificationTypes.PAUSED,
        title: 'Recurring Payment Paused',
        content: this.generatePausedContent(recurringPayment, pauseReason),
        data: {
          recurringPaymentId: recurringPayment.id,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          pauseReason
        },
        priority: 'normal',
        category: 'payment_status',
        actionButtons: [
          {
            id: 'resume_payment',
            title: 'Resume Payment',
            action: 'navigate',
            data: { screen: 'RecurringPayments', params: { paymentId: recurringPayment.id } }
          }
        ]
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        await this.logNotificationSent(userId, recurringPayment.id, 'paused', { pauseReason });
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment paused notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send payment resumed notification
   */
  async sendPaymentResumed(userId, recurringPayment) {
    try {
      const notification = {
        type: this.notificationTypes.RESUMED,
        title: 'Recurring Payment Resumed',
        content: `Your recurring payment to ${recurringPayment.biller?.display_name || 'biller'} has been resumed. Next payment: ${formatDate(recurringPayment.next_payment_date)}`,
        data: {
          recurringPaymentId: recurringPayment.id,
          billerName: recurringPayment.biller?.display_name || 'Biller',
          nextPaymentDate: recurringPayment.next_payment_date
        },
        priority: 'normal',
        category: 'payment_status'
      };

      const result = await enhancedNotificationService.sendNotification(userId, notification);
      
      if (result.success) {
        await this.logNotificationSent(userId, recurringPayment.id, 'resumed', {});
      }

      return result;
    } catch (error) {
      console.error('❌ Error sending payment resumed notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate reminder content
   */
  generateReminderContent(recurringPayment, daysUntilPayment) {
    const amount = formatCurrency(recurringPayment.amount, recurringPayment.currency);
    const biller = recurringPayment.biller?.display_name || 'biller';
    const date = formatDate(recurringPayment.next_payment_date);
    
    if (daysUntilPayment === 0) {
      return `Your recurring payment of ${amount} to ${biller} is due today.`;
    } else if (daysUntilPayment === 1) {
      return `Your recurring payment of ${amount} to ${biller} is due tomorrow (${date}).`;
    } else {
      return `Your recurring payment of ${amount} to ${biller} is due in ${daysUntilPayment} days (${date}).`;
    }
  }

  /**
   * Generate success content
   */
  generateSuccessContent(recurringPayment, paymentDetails) {
    const amount = formatCurrency(paymentDetails.amount, recurringPayment.currency);
    const biller = recurringPayment.biller?.display_name || 'biller';
    const nextDate = formatDate(paymentDetails.nextPaymentDate);
    
    return `Your recurring payment of ${amount} to ${biller} was successful. Reference: ${paymentDetails.reference}. Next payment: ${nextDate}`;
  }

  /**
   * Generate failure content
   */
  generateFailureContent(recurringPayment, failureDetails) {
    const amount = formatCurrency(recurringPayment.amount, recurringPayment.currency);
    const biller = recurringPayment.biller?.display_name || 'biller';
    
    if (failureDetails.attempts >= (failureDetails.maxAttempts || 3)) {
      return `Your recurring payment of ${amount} to ${biller} has failed after ${failureDetails.attempts} attempts and has been paused. Please update your payment method.`;
    } else {
      return `Your recurring payment of ${amount} to ${biller} failed. We'll retry automatically.`;
    }
  }

  /**
   * Generate retry content
   */
  generateRetryContent(recurringPayment, retryDetails) {
    const amount = formatCurrency(recurringPayment.amount, recurringPayment.currency);
    const biller = recurringPayment.biller?.display_name || 'biller';
    const retryDate = formatDateTime(retryDetails.retryDate);
    
    return `Payment of ${amount} to ${biller} failed (attempt ${retryDetails.attempt}/${retryDetails.maxAttempts}). Retrying on ${retryDate}.`;
  }

  /**
   * Generate paused content
   */
  generatePausedContent(recurringPayment, pauseReason) {
    const biller = recurringPayment.biller?.display_name || 'biller';
    
    const reasons = {
      user_paused: `You have paused your recurring payment to ${biller}.`,
      max_retries_reached: `Your recurring payment to ${biller} has been paused after multiple failed attempts.`,
      insufficient_funds: `Your recurring payment to ${biller} has been paused due to insufficient funds.`,
      payment_method_expired: `Your recurring payment to ${biller} has been paused due to an expired payment method.`,
      biller_unavailable: `Your recurring payment to ${biller} has been paused as the biller is temporarily unavailable.`
    };
    
    return reasons[pauseReason] || `Your recurring payment to ${biller} has been paused.`;
  }

  /**
   * Process payment reminders
   */
  async processPaymentReminders() {
    try {
      console.log('🔔 Processing payment reminders...');

      // Get payments due in the next 3 days that need reminders
      const reminderDate = new Date();
      reminderDate.setDate(reminderDate.getDate() + 3);

      const { data: paymentsNeedingReminders, error } = await supabase
        .from('recurring_bill_payments')
        .select(`
          *,
          biller:billers(
            id,
            display_name
          )
        `)
        .eq('is_active', true)
        .eq('is_paused', false)
        .eq('reminder_enabled', true)
        .lte('next_payment_date', reminderDate.toISOString())
        .gte('next_payment_date', new Date().toISOString());

      if (error) {
        console.error('❌ Error fetching payments for reminders:', error);
        return;
      }

      for (const payment of paymentsNeedingReminders) {
        // Check if reminder already sent
        const alreadySent = await this.checkReminderSent(payment.id, payment.next_payment_date);
        if (alreadySent) continue;

        // Calculate days until payment
        const daysUntil = Math.ceil(
          (new Date(payment.next_payment_date) - new Date()) / (1000 * 60 * 60 * 24)
        );

        // Send reminder based on user preference
        if (daysUntil <= (payment.reminder_days_before || 1)) {
          await this.sendPaymentReminder(payment.user_id, payment, daysUntil);
        }
      }

      console.log(`✅ Processed reminders for ${paymentsNeedingReminders.length} payments`);
    } catch (error) {
      console.error('❌ Error processing payment reminders:', error);
    }
  }

  /**
   * Check if reminder already sent
   */
  async checkReminderSent(recurringPaymentId, paymentDate) {
    try {
      const { data, error } = await supabase
        .from('recurring_payment_notifications')
        .select('id')
        .eq('recurring_payment_id', recurringPaymentId)
        .eq('notification_type', 'reminder')
        .eq('payment_date', paymentDate)
        .limit(1);

      if (error) {
        console.error('❌ Error checking reminder status:', error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error('❌ Error checking reminder sent:', error);
      return false;
    }
  }

  /**
   * Log notification sent
   */
  async logNotificationSent(userId, recurringPaymentId, notificationType, metadata = {}) {
    try {
      await supabase
        .from('recurring_payment_notifications')
        .insert({
          user_id: userId,
          recurring_payment_id: recurringPaymentId,
          notification_type: notificationType,
          payment_date: metadata.nextPaymentDate || new Date().toISOString(),
          metadata,
          sent_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging notification:', error);
    }
  }

  /**
   * Start reminder processor
   */
  startReminderProcessor() {
    // Process reminders every 6 hours
    setInterval(() => {
      this.processPaymentReminders().catch(error => {
        console.error('❌ Error in reminder processor:', error);
      });
    }, 6 * 60 * 60 * 1000); // 6 hours

    // Process immediately on startup
    setTimeout(() => {
      this.processPaymentReminders();
    }, 5000); // 5 seconds delay on startup
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(userId, dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      const { data, error } = await supabase
        .from('recurring_payment_notifications')
        .select('notification_type')
        .eq('user_id', userId)
        .gte('sent_at', startDate.toISOString());

      if (error) throw error;

      const stats = data.reduce((acc, notification) => {
        acc[notification.notification_type] = (acc[notification.notification_type] || 0) + 1;
        return acc;
      }, {});

      return { success: true, stats };
    } catch (error) {
      console.error('❌ Error getting notification stats:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new RecurringPaymentNotifications();
