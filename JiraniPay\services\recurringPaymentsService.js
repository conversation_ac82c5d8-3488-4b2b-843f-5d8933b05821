/**
 * Recurring Payments Service
 * Manages scheduled recurring bill payments with user controls,
 * payment reminders, and automatic retry mechanisms
 */

import { supabase } from './supabaseClient';
import billPaymentService from './billPaymentService';
import enhancedNotificationService from './enhancedNotificationService';
import paymentHistoryService from './paymentHistoryService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { isValidUUID } from '../utils/userUtils';

// Frequency types
const FREQUENCY_TYPES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly'
};

// Recurring payment status
const RECURRING_STATUS = {
  ACTIVE: 'active',
  PAUSED: 'paused',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  FAILED: 'failed'
};

// Payment execution status
const EXECUTION_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  RETRYING: 'retrying',
  CANCELLED: 'cancelled'
};

class RecurringPaymentsService {
  constructor() {
    this.frequencyTypes = FREQUENCY_TYPES;
    this.recurringStatus = RECURRING_STATUS;
    this.executionStatus = EXECUTION_STATUS;
    this.processingQueue = new Map();
    this.reminderQueue = new Map();
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    this.maxRetryAttempts = 3;
    this.retryDelayHours = 24;

    // Start background processors
    this.startDuePaymentProcessor();
    this.startReminderProcessor();
  }

  /**
   * Create recurring payment schedule with enhanced validation
   */
  async createRecurringPayment(recurringData) {
    // Support both old and new API signatures
    const userId = recurringData.userId || arguments[0];
    const data = recurringData.userId ? recurringData : arguments[1];
    try {
      console.log('🔄 Creating recurring payment:', { userId, data });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      // Validate recurring payment data
      const validation = await this.validateRecurringPayment(userId, data);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Calculate next payment date
      const nextPaymentDate = this.calculateNextPaymentDate(
        data.startDate,
        data.frequency,
        data.intervalValue || 1,
        data.dayOfMonth,
        data.dayOfWeek
      );

      // Create recurring payment record
      const recurringPayment = {
        user_id: userId,
        biller_id: data.billerId,
        name: data.name,
        account_number: data.accountNumber,
        account_name: data.accountName,
        amount: data.amount,
        currency: data.currency || 'UGX',
        frequency: data.frequency,
        interval_value: data.intervalValue || 1,
        day_of_month: data.dayOfMonth,
        day_of_week: data.dayOfWeek,
        start_date: data.startDate,
        end_date: data.endDate,
        next_payment_date: nextPaymentDate,
        payment_method: data.paymentMethod || 'wallet',
        is_active: data.isActive !== false,
        is_paused: false,
        max_amount: data.maxAmount,
        max_payments: data.maxPayments,
        reminder_enabled: data.reminderEnabled !== false,
        reminder_days_before: data.reminderDays || data.reminderDaysBefore || 1,
        payment_count: 0,
        failed_attempts: 0,
        last_payment_date: null,
        metadata: {
          created_by: 'user',
          biller_name: data.billerName,
          account_name: data.accountName
        },
        created_at: new Date().toISOString()
      };

      const { data: created, error } = await supabase
        .from('recurring_bill_payments')
        .insert(recurringPayment)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating recurring payment:', error);
        return {
          success: false,
          error: 'Failed to create recurring payment'
        };
      }

      // Clear cache
      this.clearUserCache(userId);

      // Send confirmation notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'recurring_payment_created',
          title: 'Recurring Payment Set Up',
          content: `Your recurring payment of ${formatCurrency(data.amount)} to ${data.billerName || 'biller'} has been set up`,
          data: {
            recurringPaymentId: created.id,
            amount: data.amount,
            frequency: data.frequency,
            nextPaymentDate
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send creation notification:', notificationError);
      }

      console.log('✅ Recurring payment created:', created.id);

      return {
        success: true,
        recurringPayment: this.formatRecurringPaymentData(created)
      };
    } catch (error) {
      console.error('❌ Error creating recurring payment:', error);
      return {
        success: false,
        error: 'Failed to create recurring payment'
      };
    }
  }

  /**
   * Get user's recurring payments
   */
  async getUserRecurringPayments(userId, options = {}) {
    try {
      const { includeInactive = false, limit = 20, offset = 0 } = options;

      let query = supabase
        .from('recurring_bill_payments')
        .select(`
          *,
          biller:billers(
            id,
            display_name,
            logo_url,
            category:bill_categories(name, display_name, icon, color)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      const { data: recurringPayments, error } = await query;

      if (error) throw error;

      return {
        success: true,
        recurringPayments: recurringPayments.map(rp => this.formatRecurringPaymentData(rp)),
        pagination: {
          limit,
          offset,
          hasMore: recurringPayments.length === limit
        }
      };
    } catch (error) {
      console.error('❌ Error getting recurring payments:', error);
      throw error;
    }
  }

  /**
   * Get recurring payment details
   */
  async getRecurringPaymentDetails(recurringPaymentId, userId) {
    try {
      const { data: recurringPayment, error } = await supabase
        .from('recurring_bill_payments')
        .select(`
          *,
          biller:billers(
            id,
            display_name,
            logo_url,
            category:bill_categories(name, display_name, icon, color)
          ),
          payments:bill_payments(
            id,
            reference,
            amount,
            status,
            created_at,
            completed_at
          )
        `)
        .eq('id', recurringPaymentId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return {
        success: true,
        recurringPayment: {
          ...this.formatRecurringPaymentData(recurringPayment),
          paymentHistory: recurringPayment.payments.map(payment => ({
            id: payment.id,
            reference: payment.reference,
            amount: payment.amount,
            status: payment.status,
            createdAt: payment.created_at,
            completedAt: payment.completed_at
          }))
        }
      };
    } catch (error) {
      console.error('❌ Error getting recurring payment details:', error);
      throw error;
    }
  }

  /**
   * Update recurring payment
   */
  async updateRecurringPayment(recurringPaymentId, userId, updates) {
    try {
      console.log('📝 Updating recurring payment:', { recurringPaymentId, updates });

      // Validate updates
      const allowedUpdates = [
        'name', 'amount', 'max_amount', 'end_date', 'reminder_enabled', 
        'reminder_days_before', 'is_paused', 'pause_until'
      ];

      const validUpdates = {};
      Object.keys(updates).forEach(key => {
        if (allowedUpdates.includes(key)) {
          validUpdates[key] = updates[key];
        }
      });

      validUpdates.updated_at = new Date().toISOString();

      const { data: updated, error } = await supabase
        .from('recurring_bill_payments')
        .update(validUpdates)
        .eq('id', recurringPaymentId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return {
        success: true,
        recurringPayment: this.formatRecurringPaymentData(updated)
      };
    } catch (error) {
      console.error('❌ Error updating recurring payment:', error);
      throw error;
    }
  }

  /**
   * Pause recurring payment
   */
  async pauseRecurringPayment(recurringPaymentId, userId, pauseUntil = null) {
    try {
      const updates = {
        is_paused: true,
        pause_until: pauseUntil,
        updated_at: new Date().toISOString()
      };

      const result = await this.updateRecurringPayment(recurringPaymentId, userId, updates);

      if (result.success) {
        // Send notification
        await enhancedNotificationService.sendNotification(userId, {
          type: 'recurring_payment_paused',
          title: 'Recurring Payment Paused',
          content: `Your recurring payment has been paused${pauseUntil ? ` until ${formatDate(pauseUntil)}` : ''}`,
          data: { recurringPaymentId }
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error pausing recurring payment:', error);
      throw error;
    }
  }

  /**
   * Resume recurring payment
   */
  async resumeRecurringPayment(recurringPaymentId, userId) {
    try {
      const updates = {
        is_paused: false,
        pause_until: null,
        updated_at: new Date().toISOString()
      };

      const result = await this.updateRecurringPayment(recurringPaymentId, userId, updates);

      if (result.success) {
        // Send notification
        await enhancedNotificationService.sendNotification(userId, {
          type: 'recurring_payment_resumed',
          title: 'Recurring Payment Resumed',
          content: 'Your recurring payment has been resumed',
          data: { recurringPaymentId }
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error resuming recurring payment:', error);
      throw error;
    }
  }

  /**
   * Cancel recurring payment
   */
  async cancelRecurringPayment(recurringPaymentId, userId, reason = 'User cancelled') {
    try {
      const updates = {
        is_active: false,
        is_paused: false,
        metadata: {
          cancelled_at: new Date().toISOString(),
          cancellation_reason: reason
        },
        updated_at: new Date().toISOString()
      };

      const result = await this.updateRecurringPayment(recurringPaymentId, userId, updates);

      if (result.success) {
        // Send notification
        await enhancedNotificationService.sendNotification(userId, {
          type: 'recurring_payment_cancelled',
          title: 'Recurring Payment Cancelled',
          content: 'Your recurring payment has been cancelled',
          data: { recurringPaymentId, reason }
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error cancelling recurring payment:', error);
      throw error;
    }
  }

  /**
   * Process due recurring payments
   */
  async processDuePayments() {
    try {
      console.log('🔄 Processing due recurring payments');

      // Get due payments
      const { data: duePayments, error } = await supabase
        .rpc('get_due_recurring_payments', {
          p_date: new Date().toISOString().split('T')[0]
        });

      if (error) throw error;

      console.log(`📋 Found ${duePayments.length} due payments`);

      for (const duePayment of duePayments) {
        if (!this.processingQueue.has(duePayment.id)) {
          this.processingQueue.set(duePayment.id, true);
          this.processRecurringPayment(duePayment).finally(() => {
            this.processingQueue.delete(duePayment.id);
          });
        }
      }

      return { success: true, processedCount: duePayments.length };
    } catch (error) {
      console.error('❌ Error processing due payments:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process individual recurring payment
   */
  async processRecurringPayment(recurringPayment) {
    try {
      console.log('💳 Processing recurring payment:', recurringPayment.id);

      // Create payment data
      const paymentData = {
        billerId: recurringPayment.biller_id,
        accountNumber: recurringPayment.account_number,
        amount: recurringPayment.amount,
        currency: recurringPayment.currency,
        paymentMethod: 'wallet',
        isRecurring: true,
        recurringPaymentId: recurringPayment.id
      };

      // Process payment
      const paymentResult = await billPaymentService.processBillPayment(
        recurringPayment.user_id,
        paymentData
      );

      if (paymentResult.success) {
        // Update recurring payment for next cycle
        await this.updateForNextPayment(recurringPayment);

        // Send success notification
        await enhancedNotificationService.sendNotification(recurringPayment.user_id, {
          type: 'recurring_payment_processed',
          title: 'Recurring Payment Processed',
          content: `Your recurring payment of ${formatCurrency(recurringPayment.amount)} was processed successfully`,
          data: {
            recurringPaymentId: recurringPayment.id,
            paymentId: paymentResult.payment.id,
            amount: recurringPayment.amount
          }
        });

      } else {
        // Handle failed payment
        await this.handleFailedRecurringPayment(recurringPayment, paymentResult.error);
      }

      return paymentResult;
    } catch (error) {
      console.error('❌ Error processing recurring payment:', error);
      await this.handleFailedRecurringPayment(recurringPayment, error.message);
      throw error;
    }
  }

  /**
   * Update recurring payment for next cycle
   */
  async updateForNextPayment(recurringPayment) {
    try {
      const nextPaymentDate = this.calculateNextPaymentDate(
        recurringPayment.next_payment_date,
        recurringPayment.frequency,
        recurringPayment.interval_value,
        recurringPayment.day_of_month,
        recurringPayment.day_of_week
      );

      const updates = {
        last_payment_date: recurringPayment.next_payment_date,
        next_payment_date: nextPaymentDate,
        payment_count: (recurringPayment.payment_count || 0) + 1,
        failed_attempts: 0, // Reset failed attempts on success
        updated_at: new Date().toISOString()
      };

      // Check if max payments reached
      if (recurringPayment.max_payments && 
          updates.payment_count >= recurringPayment.max_payments) {
        updates.is_active = false;
        updates.metadata = {
          ...recurringPayment.metadata,
          completed_at: new Date().toISOString(),
          completion_reason: 'max_payments_reached'
        };
      }

      // Check if end date reached
      if (recurringPayment.end_date && 
          new Date(nextPaymentDate) > new Date(recurringPayment.end_date)) {
        updates.is_active = false;
        updates.metadata = {
          ...recurringPayment.metadata,
          completed_at: new Date().toISOString(),
          completion_reason: 'end_date_reached'
        };
      }

      await supabase
        .from('recurring_bill_payments')
        .update(updates)
        .eq('id', recurringPayment.id);

    } catch (error) {
      console.error('❌ Error updating for next payment:', error);
    }
  }

  /**
   * Handle failed recurring payment
   */
  async handleFailedRecurringPayment(recurringPayment, errorMessage) {
    try {
      const failedAttempts = (recurringPayment.failed_attempts || 0) + 1;
      const maxFailedAttempts = recurringPayment.max_failed_attempts || 3;

      const updates = {
        failed_attempts: failedAttempts,
        updated_at: new Date().toISOString()
      };

      // Disable if max failed attempts reached
      if (failedAttempts >= maxFailedAttempts) {
        updates.is_active = false;
        updates.metadata = {
          ...recurringPayment.metadata,
          disabled_at: new Date().toISOString(),
          disable_reason: 'max_failed_attempts_reached'
        };
      }

      await supabase
        .from('recurring_bill_payments')
        .update(updates)
        .eq('id', recurringPayment.id);

      // Send failure notification
      await enhancedNotificationService.sendNotification(recurringPayment.user_id, {
        type: 'recurring_payment_failed',
        title: 'Recurring Payment Failed',
        content: `Your recurring payment of ${formatCurrency(recurringPayment.amount)} failed. ${errorMessage}`,
        data: {
          recurringPaymentId: recurringPayment.id,
          failedAttempts,
          maxFailedAttempts,
          error: errorMessage
        }
      });

    } catch (error) {
      console.error('❌ Error handling failed recurring payment:', error);
    }
  }

  /**
   * Send payment reminders
   */
  async sendPaymentReminders() {
    try {
      console.log('🔔 Sending payment reminders');

      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const reminderDate = tomorrow.toISOString().split('T')[0];

      const { data: upcomingPayments, error } = await supabase
        .from('recurring_bill_payments')
        .select(`
          *,
          biller:billers(display_name)
        `)
        .eq('is_active', true)
        .eq('is_paused', false)
        .eq('reminder_enabled', true)
        .eq('next_payment_date', reminderDate);

      if (error) throw error;

      for (const payment of upcomingPayments) {
        await enhancedNotificationService.sendNotification(payment.user_id, {
          type: 'payment_reminder',
          title: 'Payment Reminder',
          content: `Your recurring payment of ${formatCurrency(payment.amount)} to ${payment.biller.display_name} is due tomorrow`,
          data: {
            recurringPaymentId: payment.id,
            amount: payment.amount,
            biller: payment.biller.display_name,
            dueDate: payment.next_payment_date
          }
        });
      }

      console.log(`✅ Sent ${upcomingPayments.length} payment reminders`);

      return { success: true, remindersSent: upcomingPayments.length };
    } catch (error) {
      console.error('❌ Error sending payment reminders:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Calculate next payment date
   */
  calculateNextPaymentDate(currentDate, frequency, intervalValue = 1, dayOfMonth = null, dayOfWeek = null) {
    const current = new Date(currentDate);
    let nextDate = new Date(current);

    switch (frequency) {
      case this.frequencyTypes.DAILY:
        nextDate.setDate(current.getDate() + intervalValue);
        break;

      case this.frequencyTypes.WEEKLY:
        nextDate.setDate(current.getDate() + (intervalValue * 7));
        if (dayOfWeek !== null) {
          const dayDiff = dayOfWeek - nextDate.getDay();
          nextDate.setDate(nextDate.getDate() + dayDiff);
        }
        break;

      case this.frequencyTypes.MONTHLY:
        nextDate.setMonth(current.getMonth() + intervalValue);
        if (dayOfMonth !== null) {
          nextDate.setDate(dayOfMonth);
        }
        break;

      case this.frequencyTypes.QUARTERLY:
        nextDate.setMonth(current.getMonth() + (intervalValue * 3));
        break;

      case this.frequencyTypes.YEARLY:
        nextDate.setFullYear(current.getFullYear() + intervalValue);
        break;

      default:
        nextDate.setMonth(current.getMonth() + 1);
    }

    return nextDate.toISOString().split('T')[0];
  }

  /**
   * Validate recurring payment data
   */
  async validateRecurringPayment(userId, recurringData) {
    const errors = [];

    // Required fields
    if (!recurringData.billerId) errors.push('Biller is required');
    if (!recurringData.accountNumber) errors.push('Account number is required');
    if (!recurringData.amount || recurringData.amount <= 0) errors.push('Valid amount is required');
    if (!recurringData.frequency) errors.push('Payment frequency is required');
    if (!recurringData.startDate) errors.push('Start date is required');

    // Frequency validation
    if (recurringData.frequency && !Object.values(this.frequencyTypes).includes(recurringData.frequency)) {
      errors.push('Invalid payment frequency');
    }

    // Date validation
    if (recurringData.startDate && new Date(recurringData.startDate) < new Date()) {
      errors.push('Start date cannot be in the past');
    }

    if (recurringData.endDate && recurringData.startDate && 
        new Date(recurringData.endDate) <= new Date(recurringData.startDate)) {
      errors.push('End date must be after start date');
    }

    // Check for existing recurring payment
    if (recurringData.billerId && recurringData.accountNumber) {
      try {
        const { data: existing, error } = await supabase
          .from('recurring_bill_payments')
          .select('id')
          .eq('user_id', userId)
          .eq('biller_id', recurringData.billerId)
          .eq('account_number', recurringData.accountNumber)
          .eq('is_active', true)
          .limit(1);

        if (!error && existing && existing.length > 0) {
          errors.push('A recurring payment already exists for this account');
        }
      } catch (error) {
        console.error('Error checking existing recurring payment:', error);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format recurring payment data
   */
  formatRecurringPaymentData(recurringPayment) {
    return {
      id: recurringPayment.id,
      name: recurringPayment.name,
      biller: recurringPayment.biller ? {
        id: recurringPayment.biller.id,
        name: recurringPayment.biller.display_name,
        logo: recurringPayment.biller.logo_url,
        category: recurringPayment.biller.category?.display_name
      } : null,
      accountNumber: recurringPayment.account_number,
      amount: recurringPayment.amount,
      currency: recurringPayment.currency,
      frequency: recurringPayment.frequency,
      intervalValue: recurringPayment.interval_value,
      dayOfMonth: recurringPayment.day_of_month,
      dayOfWeek: recurringPayment.day_of_week,
      startDate: recurringPayment.start_date,
      endDate: recurringPayment.end_date,
      nextPaymentDate: recurringPayment.next_payment_date,
      lastPaymentDate: recurringPayment.last_payment_date,
      isActive: recurringPayment.is_active,
      isPaused: recurringPayment.is_paused,
      pauseUntil: recurringPayment.pause_until,
      maxAmount: recurringPayment.max_amount,
      paymentCount: recurringPayment.payment_count || 0,
      maxPayments: recurringPayment.max_payments,
      failedAttempts: recurringPayment.failed_attempts || 0,
      maxFailedAttempts: recurringPayment.max_failed_attempts || 3,
      reminderEnabled: recurringPayment.reminder_enabled,
      reminderDaysBefore: recurringPayment.reminder_days_before,
      metadata: recurringPayment.metadata,
      createdAt: recurringPayment.created_at,
      updatedAt: recurringPayment.updated_at
    };
  }

  /**
   * Start background processors
   */
  startDuePaymentProcessor() {
    // Process due payments every hour
    setInterval(() => {
      this.processDuePayments().catch(error => {
        console.error('❌ Error in due payment processor:', error);
      });
    }, 60 * 60 * 1000); // 1 hour
  }

  startReminderProcessor() {
    // Send reminders every 6 hours
    setInterval(() => {
      this.sendPaymentReminders().catch(error => {
        console.error('❌ Error in reminder processor:', error);
      });
    }, 6 * 60 * 60 * 1000); // 6 hours
  }

  /**
   * Clear user-specific cache
   */
  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Clear all cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache key for user recurring payments
   */
  getCacheKey(userId, operation, params = {}) {
    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : '';
    return `recurring_${operation}_${userId}_${paramString}`;
  }

  /**
   * Get cached data
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Set cached data
   */
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
}

export default new RecurringPaymentsService();
