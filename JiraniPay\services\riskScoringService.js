/**
 * Risk Scoring System
 * Dynamic risk assessment calculating transaction risk scores based on
 * user behavior, transaction history, and device fingerprinting
 */

import { supabase } from './supabaseClient';
import fraudDetectionService from './fraudDetectionService';

// Risk scoring weights and factors
const RISK_WEIGHTS = {
  userProfile: {
    kycLevel: 0.2,
    accountAge: 0.15,
    verificationStatus: 0.1
  },
  transactionHistory: {
    volume: 0.15,
    frequency: 0.1,
    patterns: 0.1
  },
  deviceFingerprint: {
    deviceTrust: 0.1,
    locationConsistency: 0.05,
    networkPattern: 0.05
  },
  behavioral: {
    timePatterns: 0.05,
    amountPatterns: 0.05
  }
};

class RiskScoringService {
  constructor() {
    this.weights = RISK_WEIGHTS;
    this.baselineRiskScore = 50; // Neutral starting point
  }

  /**
   * Calculate comprehensive risk score for a user/transaction
   */
  async calculateRiskScore(userId, transactionData = null) {
    try {
      console.log('🎯 Calculating risk score for user:', userId);

      const riskComponents = await Promise.all([
        this.assessUserProfileRisk(userId),
        this.assessTransactionHistoryRisk(userId),
        this.assessDeviceRisk(userId, transactionData?.deviceInfo),
        this.assessBehavioralRisk(userId, transactionData)
      ]);

      const [profileRisk, historyRisk, deviceRisk, behavioralRisk] = riskComponents;

      // Calculate weighted risk score
      const weightedScore = 
        (profileRisk.score * this.weights.userProfile.kycLevel) +
        (historyRisk.score * this.weights.transactionHistory.volume) +
        (deviceRisk.score * this.weights.deviceFingerprint.deviceTrust) +
        (behavioralRisk.score * this.weights.behavioral.timePatterns);

      const finalScore = Math.min(100, Math.max(0, this.baselineRiskScore + weightedScore));

      const riskAssessment = {
        userId,
        finalScore: Math.round(finalScore),
        riskLevel: this.getRiskLevel(finalScore),
        components: {
          profile: profileRisk,
          history: historyRisk,
          device: deviceRisk,
          behavioral: behavioralRisk
        },
        recommendations: this.generateRiskRecommendations(finalScore, riskComponents),
        timestamp: new Date().toISOString()
      };

      // Store risk score for future reference
      await this.storeRiskScore(userId, riskAssessment);

      console.log('✅ Risk score calculated:', { 
        userId, 
        score: finalScore, 
        level: riskAssessment.riskLevel 
      });

      return riskAssessment;
    } catch (error) {
      console.error('❌ Error calculating risk score:', error);
      throw error;
    }
  }

  /**
   * Assess user profile risk factors
   */
  async assessUserProfileRisk(userId) {
    try {
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('kyc_level, kyc_status, created_at, is_active')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      let riskScore = 0;
      const factors = [];

      // KYC Level Assessment
      switch (profile.kyc_level) {
        case 0:
        case 1:
          riskScore += 30;
          factors.push('low_kyc_level');
          break;
        case 2:
          riskScore += 10;
          factors.push('medium_kyc_level');
          break;
        case 3:
          riskScore -= 10;
          factors.push('high_kyc_level');
          break;
      }

      // KYC Status Assessment
      if (profile.kyc_status !== 'verified') {
        riskScore += 20;
        factors.push('unverified_kyc');
      }

      // Account Age Assessment
      const accountAge = this.calculateAccountAge(profile.created_at);
      if (accountAge < 7) {
        riskScore += 25;
        factors.push('new_account');
      } else if (accountAge < 30) {
        riskScore += 10;
        factors.push('young_account');
      } else if (accountAge > 365) {
        riskScore -= 5;
        factors.push('mature_account');
      }

      // Account Status
      if (!profile.is_active) {
        riskScore += 15;
        factors.push('inactive_account');
      }

      return {
        category: 'profile',
        score: Math.max(0, Math.min(100, riskScore)),
        factors,
        details: {
          kycLevel: profile.kyc_level,
          kycStatus: profile.kyc_status,
          accountAge,
          isActive: profile.is_active
        }
      };
    } catch (error) {
      console.error('❌ Error assessing profile risk:', error);
      return { category: 'profile', score: 50, factors: ['assessment_error'], error: error.message };
    }
  }

  /**
   * Assess transaction history risk
   */
  async assessTransactionHistoryRisk(userId) {
    try {
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, type, status, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      let riskScore = 0;
      const factors = [];

      if (transactions.length === 0) {
        return {
          category: 'history',
          score: 40,
          factors: ['no_transaction_history'],
          details: { transactionCount: 0 }
        };
      }

      // Transaction Volume Analysis
      const totalAmount = transactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0);
      const avgAmount = totalAmount / transactions.length;
      const completedTransactions = transactions.filter(tx => tx.status === 'completed');
      const failureRate = (transactions.length - completedTransactions.length) / transactions.length;

      // High failure rate indicates risk
      if (failureRate > 0.2) {
        riskScore += 25;
        factors.push('high_failure_rate');
      }

      // Transaction frequency analysis
      const recentTransactions = transactions.filter(tx => {
        const txDate = new Date(tx.created_at);
        const daysDiff = (new Date() - txDate) / (1000 * 60 * 60 * 24);
        return daysDiff <= 30;
      });

      const monthlyFrequency = recentTransactions.length;
      if (monthlyFrequency > 100) {
        riskScore += 15;
        factors.push('high_frequency');
      } else if (monthlyFrequency < 5) {
        riskScore += 10;
        factors.push('low_frequency');
      }

      // Pattern consistency
      const amounts = completedTransactions.map(tx => parseFloat(tx.amount));
      const variance = this.calculateVariance(amounts);
      const coefficient = variance / avgAmount;

      if (coefficient > 2) {
        riskScore += 10;
        factors.push('inconsistent_amounts');
      }

      return {
        category: 'history',
        score: Math.max(0, Math.min(100, riskScore)),
        factors,
        details: {
          transactionCount: transactions.length,
          completedCount: completedTransactions.length,
          failureRate: Math.round(failureRate * 100),
          avgAmount,
          monthlyFrequency,
          amountVariance: coefficient
        }
      };
    } catch (error) {
      console.error('❌ Error assessing history risk:', error);
      return { category: 'history', score: 50, factors: ['assessment_error'], error: error.message };
    }
  }

  /**
   * Assess device and location risk
   */
  async assessDeviceRisk(userId, deviceInfo = {}) {
    try {
      let riskScore = 0;
      const factors = [];

      // Check trusted devices
      const { data: trustedDevices, error } = await supabase
        .from('trusted_devices')
        .select('device_id, created_at, last_used')
        .eq('user_id', userId);

      if (error) throw error;

      if (deviceInfo.deviceId) {
        const isKnownDevice = trustedDevices.some(device => device.device_id === deviceInfo.deviceId);
        
        if (!isKnownDevice) {
          riskScore += 30;
          factors.push('unknown_device');
        } else {
          // Check device usage recency
          const device = trustedDevices.find(d => d.device_id === deviceInfo.deviceId);
          const daysSinceLastUse = this.calculateDaysDifference(device.last_used);
          
          if (daysSinceLastUse > 30) {
            riskScore += 15;
            factors.push('dormant_device');
          }
        }
      }

      // Location consistency check
      if (deviceInfo.location) {
        const { data: recentSessions, error: sessionError } = await supabase
          .from('user_sessions')
          .select('location')
          .eq('user_id', userId)
          .not('location', 'is', null)
          .order('created_at', { ascending: false })
          .limit(10);

        if (sessionError) throw sessionError;

        const knownLocations = [...new Set(recentSessions.map(s => s.location))];
        
        if (!knownLocations.includes(deviceInfo.location)) {
          riskScore += 20;
          factors.push('new_location');
        }
      }

      // Device count analysis
      if (trustedDevices.length > 5) {
        riskScore += 10;
        factors.push('many_devices');
      } else if (trustedDevices.length === 0) {
        riskScore += 25;
        factors.push('no_trusted_devices');
      }

      return {
        category: 'device',
        score: Math.max(0, Math.min(100, riskScore)),
        factors,
        details: {
          trustedDeviceCount: trustedDevices.length,
          currentDeviceKnown: deviceInfo.deviceId ? 
            trustedDevices.some(d => d.device_id === deviceInfo.deviceId) : null,
          knownLocationCount: deviceInfo.location ? 
            [...new Set(trustedDevices.map(d => d.location))].length : null
        }
      };
    } catch (error) {
      console.error('❌ Error assessing device risk:', error);
      return { category: 'device', score: 50, factors: ['assessment_error'], error: error.message };
    }
  }

  /**
   * Assess behavioral risk patterns
   */
  async assessBehavioralRisk(userId, transactionData = null) {
    try {
      let riskScore = 0;
      const factors = [];

      // Time pattern analysis
      const now = new Date();
      const hour = now.getHours();
      
      // Unusual hours (late night/early morning)
      if (hour >= 23 || hour <= 5) {
        riskScore += 15;
        factors.push('unusual_hours');
      }

      // Weekend activity
      const dayOfWeek = now.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        riskScore += 5;
        factors.push('weekend_activity');
      }

      // If transaction data provided, analyze specific patterns
      if (transactionData) {
        // Rapid succession check
        const { data: recentTransactions, error } = await supabase
          .from('transactions')
          .select('created_at')
          .eq('user_id', userId)
          .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
          .order('created_at', { ascending: false });

        if (error) throw error;

        if (recentTransactions.length >= 3) {
          riskScore += 20;
          factors.push('rapid_succession');
        }

        // Amount pattern analysis
        if (transactionData.amount) {
          const { data: userTransactions, error: txError } = await supabase
            .from('transactions')
            .select('amount')
            .eq('user_id', userId)
            .eq('status', 'completed')
            .order('created_at', { ascending: false })
            .limit(20);

          if (txError) throw txError;

          if (userTransactions.length > 0) {
            const amounts = userTransactions.map(tx => parseFloat(tx.amount));
            const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
            
            if (transactionData.amount > avgAmount * 3) {
              riskScore += 15;
              factors.push('unusual_amount');
            }
          }
        }
      }

      return {
        category: 'behavioral',
        score: Math.max(0, Math.min(100, riskScore)),
        factors,
        details: {
          currentHour: hour,
          dayOfWeek,
          isWeekend: dayOfWeek === 0 || dayOfWeek === 6,
          isUnusualHour: hour >= 23 || hour <= 5
        }
      };
    } catch (error) {
      console.error('❌ Error assessing behavioral risk:', error);
      return { category: 'behavioral', score: 50, factors: ['assessment_error'], error: error.message };
    }
  }

  /**
   * Get risk level from score
   */
  getRiskLevel(score) {
    if (score >= 80) return 'critical';
    if (score >= 65) return 'high';
    if (score >= 45) return 'medium';
    if (score >= 25) return 'low';
    return 'minimal';
  }

  /**
   * Generate risk-based recommendations
   */
  generateRiskRecommendations(score, components) {
    const recommendations = [];

    if (score >= 80) {
      recommendations.push({
        type: 'security',
        priority: 'high',
        action: 'Require additional verification',
        reason: 'Critical risk level detected'
      });
    }

    if (score >= 65) {
      recommendations.push({
        type: 'monitoring',
        priority: 'medium',
        action: 'Enhanced monitoring required',
        reason: 'High risk transaction pattern'
      });
    }

    // Component-specific recommendations
    components.forEach(component => {
      if (component.factors.includes('low_kyc_level')) {
        recommendations.push({
          type: 'verification',
          priority: 'medium',
          action: 'Complete KYC verification',
          reason: 'Low verification level increases risk'
        });
      }

      if (component.factors.includes('unknown_device')) {
        recommendations.push({
          type: 'device',
          priority: 'medium',
          action: 'Verify device identity',
          reason: 'Transaction from unknown device'
        });
      }
    });

    return recommendations;
  }

  /**
   * Store risk score for historical tracking
   */
  async storeRiskScore(userId, assessment) {
    try {
      const { error } = await supabase
        .from('risk_scores')
        .insert({
          user_id: userId,
          risk_score: assessment.finalScore,
          risk_level: assessment.riskLevel,
          components: assessment.components,
          recommendations: assessment.recommendations,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('❌ Error storing risk score:', error);
      // Don't throw - storage failure shouldn't stop risk calculation
    }
  }

  /**
   * Helper: Calculate account age in days
   */
  calculateAccountAge(createdAt) {
    const created = new Date(createdAt);
    const now = new Date();
    return Math.floor((now - created) / (1000 * 60 * 60 * 24));
  }

  /**
   * Helper: Calculate variance
   */
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
    return Math.sqrt(variance);
  }

  /**
   * Helper: Calculate days difference
   */
  calculateDaysDifference(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    return Math.floor((now - date) / (1000 * 60 * 60 * 24));
  }
}

export default new RiskScoringService();
