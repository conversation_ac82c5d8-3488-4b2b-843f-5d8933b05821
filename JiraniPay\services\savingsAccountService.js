/**
 * Savings Account Service
 * Comprehensive service for managing savings accounts with goal tracking,
 * interest calculation, automatic transfers, and transaction management
 */

import { supabase } from './supabaseClient';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { isValidUUID, requireAuthentication } from '../utils/userUtils';

class SavingsAccountService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    this.interestCalculationInterval = 24 * 60 * 60 * 1000; // 24 hours
    this.autoTransferCheckInterval = 60 * 60 * 1000; // 1 hour
    
    // Start background processors
    this.startInterestCalculation();
    this.startAutoTransferProcessor();
  }

  /**
   * Create a new savings account
   */
  async createSavingsAccount(userId, accountData) {
    try {
      console.log('💰 Creating savings account:', { userId, accountData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      // Validate account data
      const validation = this.validateAccountData(accountData);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Generate unique account number
      const accountNumber = await this.generateAccountNumber();

      // Create savings account
      const { data: account, error } = await supabase
        .from('savings_accounts')
        .insert({
          user_id: userId,
          account_name: accountData.accountName,
          account_type: accountData.accountType || 'general',
          currency: accountData.currency || 'UGX',
          target_amount: accountData.targetAmount,
          target_date: accountData.targetDate,
          monthly_target: accountData.monthlyTarget,
          interest_rate: accountData.interestRate || 0.05, // 5% default
          interest_calculation_method: accountData.interestCalculationMethod || 'daily',
          auto_transfer_enabled: accountData.autoTransferEnabled || false,
          auto_transfer_amount: accountData.autoTransferAmount,
          auto_transfer_frequency: accountData.autoTransferFrequency,
          auto_transfer_source_account: accountData.autoTransferSourceAccount,
          next_auto_transfer: accountData.autoTransferEnabled ? this.calculateNextAutoTransfer(accountData.autoTransferFrequency) : null,
          description: accountData.description,
          metadata: {
            account_number: accountNumber,
            created_via: 'mobile_app',
            initial_deposit: accountData.initialDeposit || 0
          }
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating savings account:', error);
        return {
          success: false,
          error: 'Failed to create savings account'
        };
      }

      // Process initial deposit if provided
      if (accountData.initialDeposit && accountData.initialDeposit > 0) {
        await this.processDeposit(userId, account.id, accountData.initialDeposit, 'Initial deposit');
      }

      // Clear cache
      this.clearUserCache(userId);

      // Send confirmation notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'savings_account_created',
          title: 'Savings Account Created',
          content: `Your ${accountData.accountName} savings account has been created successfully!`,
          data: {
            accountId: account.id,
            accountName: accountData.accountName,
            accountType: accountData.accountType
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send account creation notification:', notificationError);
      }

      console.log('✅ Savings account created:', account.id);

      return {
        success: true,
        account: this.formatAccountResponse(account)
      };
    } catch (error) {
      console.error('❌ Error creating savings account:', error);
      return {
        success: false,
        error: 'Failed to create savings account'
      };
    }
  }

  /**
   * Get user's savings accounts
   */
  async getUserSavingsAccounts(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      // Check cache first
      const cacheKey = this.getCacheKey(userId, 'accounts', options);
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        return { success: true, accounts: cached };
      }

      console.log('💰 Fetching savings accounts for user:', userId);

      let query = supabase
        .from('savings_accounts')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options.accountType) {
        query = query.eq('account_type', options.accountType);
      }
      if (options.isActive !== undefined) {
        query = query.eq('is_active', options.isActive);
      }

      const { data: accounts, error } = await query;

      if (error) {
        console.error('❌ Error fetching savings accounts:', error);
        return {
          success: false,
          error: 'Failed to fetch savings accounts'
        };
      }

      const formattedAccounts = accounts.map(account => this.formatAccountResponse(account));

      // Cache the result
      this.setCachedData(cacheKey, formattedAccounts);

      console.log(`✅ Found ${formattedAccounts.length} savings accounts`);

      return {
        success: true,
        accounts: formattedAccounts
      };
    } catch (error) {
      console.error('❌ Error getting user savings accounts:', error);
      return {
        success: false,
        error: 'Failed to fetch savings accounts'
      };
    }
  }

  /**
   * Get savings account details
   */
  async getSavingsAccountDetails(accountId, userId) {
    try {
      if (!accountId || !isValidUUID(accountId)) {
        return {
          success: false,
          error: 'Valid account ID is required'
        };
      }

      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      console.log('💰 Fetching savings account details:', accountId);

      const { data: account, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('❌ Error fetching account details:', error);
        return {
          success: false,
          error: 'Account not found'
        };
      }

      // Get recent transactions
      const transactionsResult = await this.getAccountTransactions(accountId, userId, { limit: 10 });
      const recentTransactions = transactionsResult.success ? transactionsResult.transactions : [];

      // Calculate account analytics
      const analytics = await this.calculateAccountAnalytics(accountId);

      return {
        success: true,
        account: {
          ...this.formatAccountResponse(account),
          recentTransactions,
          analytics
        }
      };
    } catch (error) {
      console.error('❌ Error getting savings account details:', error);
      return {
        success: false,
        error: 'Failed to fetch account details'
      };
    }
  }

  /**
   * Process deposit to savings account
   */
  async processDeposit(userId, accountId, amount, description = 'Deposit', sourceAccount = 'wallet') {
    try {
      console.log('💰 Processing deposit:', { userId, accountId, amount });

      // Validate inputs
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }
      if (!accountId || !isValidUUID(accountId)) {
        return { success: false, error: 'Valid account ID is required' };
      }
      if (!amount || amount <= 0) {
        return { success: false, error: 'Valid amount is required' };
      }

      // Get account details
      const accountResult = await this.getSavingsAccountDetails(accountId, userId);
      if (!accountResult.success) {
        return accountResult;
      }

      const account = accountResult.account;

      // Generate transaction reference
      const reference = await this.generateTransactionReference();

      // Create transaction record
      const { data: transaction, error: transactionError } = await supabase
        .from('savings_transactions')
        .insert({
          savings_account_id: accountId,
          user_id: userId,
          transaction_type: 'deposit',
          amount: amount,
          currency: account.currency,
          balance_before: account.currentBalance,
          balance_after: account.currentBalance + amount,
          reference_number: reference,
          source_account: sourceAccount,
          status: 'completed',
          description: description,
          processed_at: new Date().toISOString(),
          metadata: {
            source: 'mobile_app',
            deposit_method: sourceAccount
          }
        })
        .select()
        .single();

      if (transactionError) {
        console.error('❌ Error creating deposit transaction:', transactionError);
        return { success: false, error: 'Failed to process deposit' };
      }

      // Update account balance
      await this.updateAccountBalance(accountId, amount, 'deposit');

      // Clear cache
      this.clearUserCache(userId);

      // Send notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'savings_deposit',
          title: 'Deposit Successful',
          content: `${formatCurrency(amount)} has been deposited to your ${account.accountName} account`,
          data: {
            accountId,
            amount,
            newBalance: account.currentBalance + amount,
            transactionId: transaction.id
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send deposit notification:', notificationError);
      }

      console.log('✅ Deposit processed successfully:', transaction.id);

      return {
        success: true,
        transaction: this.formatTransactionResponse(transaction),
        newBalance: account.currentBalance + amount
      };
    } catch (error) {
      console.error('❌ Error processing deposit:', error);
      return { success: false, error: 'Failed to process deposit' };
    }
  }

  /**
   * Process withdrawal from savings account
   */
  async processWithdrawal(userId, accountId, amount, description = 'Withdrawal', destinationAccount = 'wallet') {
    try {
      console.log('💰 Processing withdrawal:', { userId, accountId, amount });

      // Validate inputs
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }
      if (!accountId || !isValidUUID(accountId)) {
        return { success: false, error: 'Valid account ID is required' };
      }
      if (!amount || amount <= 0) {
        return { success: false, error: 'Valid amount is required' };
      }

      // Get account details
      const accountResult = await this.getSavingsAccountDetails(accountId, userId);
      if (!accountResult.success) {
        return accountResult;
      }

      const account = accountResult.account;

      // Check sufficient balance
      if (account.availableBalance < amount) {
        return {
          success: false,
          error: 'Insufficient balance'
        };
      }

      // Generate transaction reference
      const reference = await this.generateTransactionReference();

      // Create transaction record
      const { data: transaction, error: transactionError } = await supabase
        .from('savings_transactions')
        .insert({
          savings_account_id: accountId,
          user_id: userId,
          transaction_type: 'withdrawal',
          amount: -amount, // Negative for withdrawal
          currency: account.currency,
          balance_before: account.currentBalance,
          balance_after: account.currentBalance - amount,
          reference_number: reference,
          destination_account: destinationAccount,
          status: 'completed',
          description: description,
          processed_at: new Date().toISOString(),
          metadata: {
            source: 'mobile_app',
            withdrawal_method: destinationAccount
          }
        })
        .select()
        .single();

      if (transactionError) {
        console.error('❌ Error creating withdrawal transaction:', transactionError);
        return { success: false, error: 'Failed to process withdrawal' };
      }

      // Update account balance
      await this.updateAccountBalance(accountId, -amount, 'withdrawal');

      // Clear cache
      this.clearUserCache(userId);

      // Send notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'savings_withdrawal',
          title: 'Withdrawal Successful',
          content: `${formatCurrency(amount)} has been withdrawn from your ${account.accountName} account`,
          data: {
            accountId,
            amount,
            newBalance: account.currentBalance - amount,
            transactionId: transaction.id
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send withdrawal notification:', notificationError);
      }

      console.log('✅ Withdrawal processed successfully:', transaction.id);

      return {
        success: true,
        transaction: this.formatTransactionResponse(transaction),
        newBalance: account.currentBalance - amount
      };
    } catch (error) {
      console.error('❌ Error processing withdrawal:', error);
      return { success: false, error: 'Failed to process withdrawal' };
    }
  }

  /**
   * Get account transactions
   */
  async getAccountTransactions(accountId, userId, options = {}) {
    try {
      if (!accountId || !isValidUUID(accountId)) {
        return { success: false, error: 'Valid account ID is required' };
      }
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      let query = supabase
        .from('savings_transactions')
        .select('*')
        .eq('savings_account_id', accountId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }
      if (options.startDate) {
        query = query.gte('created_at', options.startDate);
      }
      if (options.endDate) {
        query = query.lte('created_at', options.endDate);
      }

      const { data: transactions, error } = await query;

      if (error) {
        console.error('❌ Error fetching transactions:', error);
        return { success: false, error: 'Failed to fetch transactions' };
      }

      return {
        success: true,
        transactions: transactions.map(t => this.formatTransactionResponse(t))
      };
    } catch (error) {
      console.error('❌ Error getting account transactions:', error);
      return { success: false, error: 'Failed to fetch transactions' };
    }
  }

  /**
   * Calculate and apply interest
   */
  async calculateInterest(accountId) {
    try {
      const { data: account, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('id', accountId)
        .single();

      if (error || !account) {
        console.error('❌ Account not found for interest calculation:', accountId);
        return;
      }

      // Skip if no balance or no interest rate
      if (account.current_balance <= 0 || account.interest_rate <= 0) {
        return;
      }

      // Calculate days since last calculation
      const lastCalculation = account.last_interest_calculation ?
        new Date(account.last_interest_calculation) :
        new Date(account.created_at);
      const now = new Date();
      const daysDiff = Math.floor((now - lastCalculation) / (1000 * 60 * 60 * 24));

      if (daysDiff < 1) {
        return; // No interest to calculate
      }

      // Calculate daily interest
      const dailyRate = account.interest_rate / 365;
      const interestAmount = account.current_balance * dailyRate * daysDiff;

      if (interestAmount > 0.01) { // Only apply if interest is more than 1 cent
        // Create interest transaction
        const reference = await this.generateTransactionReference();

        await supabase
          .from('savings_transactions')
          .insert({
            savings_account_id: accountId,
            user_id: account.user_id,
            transaction_type: 'interest',
            amount: interestAmount,
            currency: account.currency,
            balance_before: account.current_balance,
            balance_after: account.current_balance + interestAmount,
            reference_number: reference,
            status: 'completed',
            description: `Interest for ${daysDiff} days`,
            processed_at: now.toISOString(),
            metadata: {
              days: daysDiff,
              daily_rate: dailyRate,
              calculation_method: account.interest_calculation_method
            }
          });

        // Update account balance and interest tracking
        await supabase
          .from('savings_accounts')
          .update({
            current_balance: account.current_balance + interestAmount,
            available_balance: account.available_balance + interestAmount,
            total_interest_earned: account.total_interest_earned + interestAmount,
            last_interest_calculation: now.toISOString(),
            updated_at: now.toISOString()
          })
          .eq('id', accountId);

        console.log(`✅ Applied interest: ${interestAmount} to account ${accountId}`);
      }
    } catch (error) {
      console.error('❌ Error calculating interest:', error);
    }
  }

  /**
   * Process automatic transfers
   */
  async processAutoTransfers() {
    try {
      console.log('💰 Processing automatic transfers...');

      const { data: accounts, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('auto_transfer_enabled', true)
        .eq('is_active', true)
        .lte('next_auto_transfer', new Date().toISOString());

      if (error) {
        console.error('❌ Error fetching auto transfer accounts:', error);
        return;
      }

      for (const account of accounts) {
        try {
          // Process the transfer
          const result = await this.processDeposit(
            account.user_id,
            account.id,
            account.auto_transfer_amount,
            'Automatic transfer',
            account.auto_transfer_source_account
          );

          if (result.success) {
            // Calculate next transfer date
            const nextTransfer = this.calculateNextAutoTransfer(account.auto_transfer_frequency);

            // Update next transfer date
            await supabase
              .from('savings_accounts')
              .update({
                next_auto_transfer: nextTransfer.toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', account.id);

            console.log(`✅ Auto transfer completed for account ${account.id}`);
          } else {
            console.error(`❌ Auto transfer failed for account ${account.id}:`, result.error);

            // Send failure notification
            await enhancedNotificationService.sendNotification(account.user_id, {
              type: 'auto_transfer_failed',
              title: 'Automatic Transfer Failed',
              content: `Your automatic transfer to ${account.account_name} failed: ${result.error}`,
              data: { accountId: account.id, error: result.error }
            });
          }
        } catch (transferError) {
          console.error(`❌ Error processing auto transfer for account ${account.id}:`, transferError);
        }
      }
    } catch (error) {
      console.error('❌ Error processing auto transfers:', error);
    }
  }

  /**
   * Update savings goal progress
   */
  async updateGoalProgress(accountId) {
    try {
      const { data: account, error } = await supabase
        .from('savings_accounts')
        .select('*')
        .eq('id', accountId)
        .single();

      if (error || !account || !account.target_amount) {
        return;
      }

      const progressPercentage = Math.min(
        (account.current_balance / account.target_amount) * 100,
        100
      );

      // Check for milestone achievements
      const milestones = [25, 50, 75, 100];
      const currentMilestone = milestones.find(m =>
        progressPercentage >= m &&
        (!account.metadata?.last_milestone || account.metadata.last_milestone < m)
      );

      if (currentMilestone) {
        // Update milestone in metadata
        await supabase
          .from('savings_accounts')
          .update({
            metadata: {
              ...account.metadata,
              last_milestone: currentMilestone,
              milestone_date: new Date().toISOString()
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', accountId);

        // Send milestone notification
        await enhancedNotificationService.sendNotification(account.user_id, {
          type: 'savings_milestone',
          title: 'Savings Milestone Achieved! 🎉',
          content: `Congratulations! You've reached ${currentMilestone}% of your ${account.account_name} goal`,
          data: {
            accountId,
            milestone: currentMilestone,
            currentAmount: account.current_balance,
            targetAmount: account.target_amount
          }
        });
      }
    } catch (error) {
      console.error('❌ Error updating goal progress:', error);
    }
  }

  /**
   * Calculate account analytics
   */
  async calculateAccountAnalytics(accountId) {
    try {
      // Get transaction history for analytics
      const { data: transactions, error } = await supabase
        .from('savings_transactions')
        .select('*')
        .eq('savings_account_id', accountId)
        .eq('status', 'completed')
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching transactions for analytics:', error);
        return {};
      }

      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

      // Calculate various metrics
      const totalDeposits = transactions
        .filter(t => t.transaction_type === 'deposit')
        .reduce((sum, t) => sum + t.amount, 0);

      const totalWithdrawals = transactions
        .filter(t => t.transaction_type === 'withdrawal')
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      const totalInterest = transactions
        .filter(t => t.transaction_type === 'interest')
        .reduce((sum, t) => sum + t.amount, 0);

      const recentTransactions = transactions.filter(t =>
        new Date(t.created_at) >= thirtyDaysAgo
      );

      const monthlyAverage = recentTransactions.length > 0 ?
        recentTransactions
          .filter(t => t.transaction_type === 'deposit')
          .reduce((sum, t) => sum + t.amount, 0) : 0;

      return {
        totalDeposits,
        totalWithdrawals,
        totalInterest,
        netSavings: totalDeposits - totalWithdrawals,
        monthlyAverage,
        transactionCount: transactions.length,
        averageTransactionSize: transactions.length > 0 ?
          totalDeposits / transactions.filter(t => t.transaction_type === 'deposit').length : 0,
        savingsRate: totalDeposits > 0 ? ((totalDeposits - totalWithdrawals) / totalDeposits) * 100 : 0
      };
    } catch (error) {
      console.error('❌ Error calculating account analytics:', error);
      return {};
    }
  }

  /**
   * Utility Methods
   */

  validateAccountData(data) {
    const errors = [];

    if (!data.accountName || data.accountName.trim().length < 2) {
      errors.push('Account name must be at least 2 characters');
    }

    if (data.targetAmount && data.targetAmount <= 0) {
      errors.push('Target amount must be greater than 0');
    }

    if (data.initialDeposit && data.initialDeposit < 0) {
      errors.push('Initial deposit cannot be negative');
    }

    if (data.autoTransferEnabled) {
      if (!data.autoTransferAmount || data.autoTransferAmount <= 0) {
        errors.push('Auto transfer amount is required when auto transfer is enabled');
      }
      if (!data.autoTransferFrequency) {
        errors.push('Auto transfer frequency is required when auto transfer is enabled');
      }
      if (!data.autoTransferSourceAccount) {
        errors.push('Auto transfer source account is required when auto transfer is enabled');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async generateAccountNumber() {
    const prefix = 'SAV';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
  }

  async generateTransactionReference() {
    const prefix = 'TXN';
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  }

  calculateNextAutoTransfer(frequency) {
    const now = new Date();
    const next = new Date(now);

    switch (frequency) {
      case 'daily':
        next.setDate(next.getDate() + 1);
        break;
      case 'weekly':
        next.setDate(next.getDate() + 7);
        break;
      case 'monthly':
        next.setMonth(next.getMonth() + 1);
        break;
      default:
        next.setMonth(next.getMonth() + 1); // Default to monthly
    }

    return next;
  }

  async updateAccountBalance(accountId, amount, transactionType) {
    try {
      await supabase.rpc('update_savings_balance', {
        p_account_id: accountId,
        p_amount: amount,
        p_transaction_type: transactionType
      });

      // Update goal progress if this is a deposit
      if (transactionType === 'deposit') {
        await this.updateGoalProgress(accountId);
      }
    } catch (error) {
      console.error('❌ Error updating account balance:', error);
      throw error;
    }
  }

  formatAccountResponse(account) {
    return {
      id: account.id,
      accountName: account.account_name,
      accountType: account.account_type,
      currency: account.currency,
      currentBalance: parseFloat(account.current_balance),
      availableBalance: parseFloat(account.available_balance),
      targetAmount: account.target_amount ? parseFloat(account.target_amount) : null,
      targetDate: account.target_date,
      monthlyTarget: account.monthly_target ? parseFloat(account.monthly_target) : null,
      interestRate: parseFloat(account.interest_rate),
      interestCalculationMethod: account.interest_calculation_method,
      totalInterestEarned: parseFloat(account.total_interest_earned),
      autoTransferEnabled: account.auto_transfer_enabled,
      autoTransferAmount: account.auto_transfer_amount ? parseFloat(account.auto_transfer_amount) : null,
      autoTransferFrequency: account.auto_transfer_frequency,
      autoTransferSourceAccount: account.auto_transfer_source_account,
      nextAutoTransfer: account.next_auto_transfer,
      isActive: account.is_active,
      isLocked: account.is_locked,
      lockReason: account.lock_reason,
      description: account.description,
      metadata: account.metadata,
      createdAt: account.created_at,
      updatedAt: account.updated_at,
      // Calculated fields
      progressPercentage: account.target_amount ?
        Math.min((parseFloat(account.current_balance) / parseFloat(account.target_amount)) * 100, 100) : 0,
      accountNumber: account.metadata?.account_number
    };
  }

  formatTransactionResponse(transaction) {
    return {
      id: transaction.id,
      accountId: transaction.savings_account_id,
      transactionType: transaction.transaction_type,
      amount: parseFloat(transaction.amount),
      currency: transaction.currency,
      balanceBefore: parseFloat(transaction.balance_before),
      balanceAfter: parseFloat(transaction.balance_after),
      referenceNumber: transaction.reference_number,
      externalReference: transaction.external_reference,
      sourceAccount: transaction.source_account,
      destinationAccount: transaction.destination_account,
      status: transaction.status,
      description: transaction.description,
      processedAt: transaction.processed_at,
      metadata: transaction.metadata,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at
    };
  }

  /**
   * Cache Management
   */
  getCacheKey(userId, operation, params = {}) {
    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : '';
    return `savings_${operation}_${userId}_${paramString}`;
  }

  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Background Processors
   */
  startInterestCalculation() {
    // Calculate interest every 24 hours
    setInterval(async () => {
      try {
        console.log('💰 Starting daily interest calculation...');

        const { data: accounts, error } = await supabase
          .from('savings_accounts')
          .select('id')
          .eq('is_active', true)
          .gt('current_balance', 0)
          .gt('interest_rate', 0);

        if (error) {
          console.error('❌ Error fetching accounts for interest calculation:', error);
          return;
        }

        for (const account of accounts) {
          await this.calculateInterest(account.id);
        }

        console.log(`✅ Interest calculation completed for ${accounts.length} accounts`);
      } catch (error) {
        console.error('❌ Error in interest calculation processor:', error);
      }
    }, this.interestCalculationInterval);
  }

  startAutoTransferProcessor() {
    // Process auto transfers every hour
    setInterval(() => {
      this.processAutoTransfers().catch(error => {
        console.error('❌ Error in auto transfer processor:', error);
      });
    }, this.autoTransferCheckInterval);
  }

  /**
   * Get savings summary for user
   */
  async getSavingsSummary(userId) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      const { data: summary, error } = await supabase
        .from('savings_summary')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        console.error('❌ Error fetching savings summary:', error);
        return { success: false, error: 'Failed to fetch savings summary' };
      }

      return {
        success: true,
        summary: summary || {
          totalAccounts: 0,
          totalSavings: 0,
          totalInterest: 0,
          averageInterestRate: 0,
          goalAccounts: 0,
          autoTransferAccounts: 0
        }
      };
    } catch (error) {
      console.error('❌ Error getting savings summary:', error);
      return { success: false, error: 'Failed to fetch savings summary' };
    }
  }

  /**
   * Get savings transactions for analytics
   */
  async getSavingsTransactions(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required', transactions: [] };
      }

      let query = supabase
        .from('savings_transactions')
        .select(`
          *,
          savings_account:savings_accounts(account_name, account_type)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.startDate) {
        query = query.gte('created_at', options.startDate.toISOString());
      }

      if (options.endDate) {
        query = query.lte('created_at', options.endDate.toISOString());
      }

      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error getting savings transactions:', error);
        return { success: false, error: error.message, transactions: [] };
      }

      return { success: true, transactions: data || [] };
    } catch (error) {
      console.error('❌ Error getting savings transactions:', error);
      return { success: false, error: error.message, transactions: [] };
    }
  }
}

export default new SavingsAccountService();
