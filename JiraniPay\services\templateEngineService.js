/**
 * Template Engine Service
 * Flexible template system for notifications and receipts with localization
 * and branding support
 */

import { supabase } from './supabaseClient';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime, formatDate, formatRelativeTime } from '../utils/dateUtils';

// Built-in template variables and functions
const TEMPLATE_FUNCTIONS = {
  formatCurrency: (amount, currency = 'UGX') => formatCurrency(amount, currency),
  formatDateTime: (date) => formatDateTime(date),
  formatDate: (date) => formatDate(date),
  formatRelativeTime: (date) => formatRelativeTime(date),
  uppercase: (text) => text?.toUpperCase() || '',
  lowercase: (text) => text?.toLowerCase() || '',
  capitalize: (text) => text?.charAt(0).toUpperCase() + text?.slice(1).toLowerCase() || '',
  truncate: (text, length = 50) => text?.length > length ? text.substring(0, length) + '...' : text || ''
};

// Default templates for different notification types and channels
const DEFAULT_TEMPLATES = {
  transaction_completed: {
    push: {
      title: 'Transaction Successful',
      body: 'Your {{transactionType}} of {{formatCurrency amount currency}} was successful. Ref: {{reference}}'
    },
    sms: {
      content: 'JiraniPay: Your {{transactionType}} of {{formatCurrency amount currency}} was successful. Ref: {{reference}}. Thank you!'
    },
    email: {
      subject: 'Transaction Successful - {{formatCurrency amount currency}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #1a73e8; color: white; padding: 20px; text-align: center;">
            <h1>🎉 Transaction Successful</h1>
          </div>
          <div style="padding: 20px;">
            <p>Hello {{userName}},</p>
            <p>Your {{transactionType}} has been completed successfully!</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>Transaction Details:</h3>
              <p><strong>Amount:</strong> {{formatCurrency amount currency}}</p>
              <p><strong>Reference:</strong> {{reference}}</p>
              <p><strong>Date:</strong> {{formatDateTime timestamp}}</p>
              {{#if recipient}}<p><strong>Recipient:</strong> {{recipient}}</p>{{/if}}
            </div>
            <p>Thank you for using JiraniPay!</p>
          </div>
        </div>
      `
    }
  },
  money_received: {
    push: {
      title: 'Money Received',
      body: 'You received {{formatCurrency amount currency}} from {{senderName}}. Ref: {{reference}}'
    },
    sms: {
      content: 'JiraniPay: You received {{formatCurrency amount currency}} from {{senderName}}. Ref: {{reference}}. Check your wallet!'
    },
    email: {
      subject: 'Money Received - {{formatCurrency amount currency}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #28a745; color: white; padding: 20px; text-align: center;">
            <h1>💰 Money Received</h1>
          </div>
          <div style="padding: 20px;">
            <p>Hello {{userName}},</p>
            <p>Great news! You've received money in your JiraniPay wallet.</p>
            <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>Payment Details:</h3>
              <p><strong>Amount:</strong> {{formatCurrency amount currency}}</p>
              <p><strong>From:</strong> {{senderName}}</p>
              <p><strong>Reference:</strong> {{reference}}</p>
              <p><strong>Date:</strong> {{formatDateTime timestamp}}</p>
            </div>
            <p>The money is now available in your wallet.</p>
          </div>
        </div>
      `
    }
  },
  security_alert: {
    push: {
      title: 'Security Alert',
      body: 'Security alert: {{alertMessage}}. If this wasn\'t you, contact support immediately.'
    },
    sms: {
      content: 'JiraniPay Security Alert: {{alertMessage}}. If this wasn\'t you, contact support at +256 700 123 456 immediately.'
    },
    email: {
      subject: 'Important Security Alert - JiraniPay',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #dc3545; color: white; padding: 20px; text-align: center;">
            <h1>🔒 Security Alert</h1>
          </div>
          <div style="padding: 20px;">
            <p>Hello {{userName}},</p>
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>Security Alert:</h3>
              <p>{{alertMessage}}</p>
              <p><strong>Time:</strong> {{formatDateTime timestamp}}</p>
              {{#if location}}<p><strong>Location:</strong> {{location}}</p>{{/if}}
              {{#if device}}<p><strong>Device:</strong> {{device}}</p>{{/if}}
            </div>
            <h4>What should you do?</h4>
            <ul>
              <li>If this was you, no action is needed</li>
              <li>If this wasn't you, change your password immediately</li>
              <li>Review your recent account activity</li>
              <li>Contact our support team if you need assistance</li>
            </ul>
            <p><strong>Support:</strong> +256 700 123 456</p>
          </div>
        </div>
      `
    }
  }
};

// Localization strings
const LOCALIZATION = {
  en: {
    transaction_types: {
      send_money: 'money transfer',
      receive_money: 'money received',
      bill_payment: 'bill payment',
      airtime_purchase: 'airtime purchase',
      withdrawal: 'cash withdrawal',
      deposit: 'cash deposit'
    },
    common: {
      hello: 'Hello',
      thank_you: 'Thank you',
      support: 'Support',
      amount: 'Amount',
      reference: 'Reference',
      date: 'Date',
      time: 'Time',
      status: 'Status'
    }
  },
  sw: { // Swahili
    transaction_types: {
      send_money: 'uhamishaji wa pesa',
      receive_money: 'pesa zilizopokelewa',
      bill_payment: 'malipo ya bili',
      airtime_purchase: 'ununuzi wa airtime',
      withdrawal: 'kutoa pesa',
      deposit: 'kuweka pesa'
    },
    common: {
      hello: 'Hujambo',
      thank_you: 'Asante',
      support: 'Msaada',
      amount: 'Kiasi',
      reference: 'Rejea',
      date: 'Tarehe',
      time: 'Wakati',
      status: 'Hali'
    }
  },
  lg: { // Luganda
    transaction_types: {
      send_money: 'okusindika ssente',
      receive_money: 'ssente ezifunye',
      bill_payment: 'okusasula bili',
      airtime_purchase: 'okugula airtime',
      withdrawal: 'okuggya ssente',
      deposit: 'okuteeka ssente'
    },
    common: {
      hello: 'Oli otya',
      thank_you: 'Webale',
      support: 'Obuyambi',
      amount: 'Omuwendo',
      reference: 'Reference',
      date: 'Olunaku',
      time: 'Obudde',
      status: 'Embeera'
    }
  }
};

class TemplateEngineService {
  constructor() {
    this.defaultTemplates = DEFAULT_TEMPLATES;
    this.templateFunctions = TEMPLATE_FUNCTIONS;
    this.localization = LOCALIZATION;
    this.templateCache = new Map();
  }

  /**
   * Render template with data
   */
  async renderTemplate(templateType, channel, data, options = {}) {
    try {
      console.log('🎨 Rendering template:', { templateType, channel });

      const {
        language = 'en',
        useCustomTemplate = true,
        userId = null
      } = options;

      // Get template
      const template = await this.getTemplate(templateType, channel, language, useCustomTemplate);
      
      // Prepare template data with localization
      const templateData = await this.prepareTemplateData(data, language);

      // Render template
      const rendered = this.processTemplate(template, templateData);

      console.log('✅ Template rendered successfully');

      return {
        success: true,
        rendered,
        templateType,
        channel,
        language
      };
    } catch (error) {
      console.error('❌ Error rendering template:', error);
      throw error;
    }
  }

  /**
   * Get template from database or use default
   */
  async getTemplate(templateType, channel, language, useCustomTemplate) {
    try {
      // Check cache first
      const cacheKey = `${templateType}_${channel}_${language}`;
      if (this.templateCache.has(cacheKey)) {
        return this.templateCache.get(cacheKey);
      }

      let template = null;

      // Try to get custom template from database
      if (useCustomTemplate) {
        const { data: customTemplate, error } = await supabase
          .from('notification_templates')
          .select('*')
          .eq('type', templateType)
          .eq('channel', channel)
          .eq('language', language)
          .eq('is_active', true)
          .single();

        if (!error && customTemplate) {
          template = {
            subject: customTemplate.subject_template,
            title: customTemplate.subject_template,
            body: customTemplate.content_template,
            content: customTemplate.content_template,
            html: customTemplate.html_template
          };
        }
      }

      // Fall back to default template
      if (!template) {
        template = this.defaultTemplates[templateType]?.[channel];
      }

      if (!template) {
        throw new Error(`Template not found: ${templateType}/${channel}`);
      }

      // Cache template
      this.templateCache.set(cacheKey, template);

      return template;
    } catch (error) {
      console.error('❌ Error getting template:', error);
      throw error;
    }
  }

  /**
   * Prepare template data with localization and helper functions
   */
  async prepareTemplateData(data, language) {
    const localizedData = { ...data };

    // Add localization strings
    localizedData.t = this.localization[language] || this.localization.en;

    // Add template functions
    Object.entries(this.templateFunctions).forEach(([name, func]) => {
      localizedData[name] = func;
    });

    // Localize transaction type if present
    if (localizedData.transactionType) {
      const localizedType = this.localization[language]?.transaction_types?.[localizedData.transactionType];
      if (localizedType) {
        localizedData.transactionType = localizedType;
      }
    }

    // Add current timestamp if not present
    if (!localizedData.timestamp) {
      localizedData.timestamp = new Date().toISOString();
    }

    return localizedData;
  }

  /**
   * Process template with data using simple template engine
   */
  processTemplate(template, data) {
    const result = {};

    Object.entries(template).forEach(([key, templateString]) => {
      if (typeof templateString === 'string') {
        result[key] = this.interpolateTemplate(templateString, data);
      } else {
        result[key] = templateString;
      }
    });

    return result;
  }

  /**
   * Simple template interpolation with support for functions and conditionals
   */
  interpolateTemplate(template, data) {
    // Handle function calls like {{formatCurrency amount currency}}
    template = template.replace(/\{\{(\w+)\s+([^}]+)\}\}/g, (match, funcName, args) => {
      if (this.templateFunctions[funcName]) {
        const argValues = args.split(/\s+/).map(arg => {
          // Check if it's a variable reference
          if (data.hasOwnProperty(arg)) {
            return data[arg];
          }
          // Check if it's a quoted string
          if (arg.startsWith('"') && arg.endsWith('"')) {
            return arg.slice(1, -1);
          }
          // Check if it's a number
          if (!isNaN(arg)) {
            return parseFloat(arg);
          }
          return arg;
        });
        
        try {
          return this.templateFunctions[funcName](...argValues);
        } catch (error) {
          console.error('❌ Error calling template function:', error);
          return match; // Return original if function fails
        }
      }
      return match;
    });

    // Handle simple variable interpolation {{variable}}
    template = template.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      return data.hasOwnProperty(varName) ? data[varName] : match;
    });

    // Handle simple conditionals {{#if variable}}content{{/if}}
    template = template.replace(/\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/gs, (match, varName, content) => {
      return data[varName] ? content : '';
    });

    return template;
  }

  /**
   * Create custom template
   */
  async createCustomTemplate(templateData) {
    try {
      console.log('📝 Creating custom template:', templateData.name);

      const { data: template, error } = await supabase
        .from('notification_templates')
        .insert({
          name: templateData.name,
          type: templateData.type,
          channel: templateData.channel,
          language: templateData.language || 'en',
          subject_template: templateData.subject,
          content_template: templateData.content,
          html_template: templateData.html,
          variables: templateData.variables || [],
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Clear cache for this template type
      this.clearTemplateCache(templateData.type, templateData.channel, templateData.language);

      console.log('✅ Custom template created');

      return {
        success: true,
        template
      };
    } catch (error) {
      console.error('❌ Error creating custom template:', error);
      throw error;
    }
  }

  /**
   * Update custom template
   */
  async updateCustomTemplate(templateId, updates) {
    try {
      console.log('📝 Updating custom template:', templateId);

      const { data: template, error } = await supabase
        .from('notification_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)
        .select()
        .single();

      if (error) throw error;

      // Clear cache for this template
      this.clearTemplateCache(template.type, template.channel, template.language);

      console.log('✅ Custom template updated');

      return {
        success: true,
        template
      };
    } catch (error) {
      console.error('❌ Error updating custom template:', error);
      throw error;
    }
  }

  /**
   * Get available templates
   */
  async getAvailableTemplates(filters = {}) {
    try {
      let query = supabase
        .from('notification_templates')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.type) {
        query = query.eq('type', filters.type);
      }
      if (filters.channel) {
        query = query.eq('channel', filters.channel);
      }
      if (filters.language) {
        query = query.eq('language', filters.language);
      }

      const { data: templates, error } = await query;

      if (error) throw error;

      return {
        success: true,
        templates: templates.map(template => ({
          id: template.id,
          name: template.name,
          type: template.type,
          channel: template.channel,
          language: template.language,
          variables: template.variables,
          createdAt: template.created_at,
          updatedAt: template.updated_at
        }))
      };
    } catch (error) {
      console.error('❌ Error getting available templates:', error);
      throw error;
    }
  }

  /**
   * Preview template with sample data
   */
  async previewTemplate(templateType, channel, language = 'en', sampleData = {}) {
    try {
      const defaultSampleData = {
        userName: 'John Doe',
        amount: 50000,
        currency: 'UGX',
        reference: 'JP123456789',
        transactionType: 'send_money',
        recipient: 'Jane Smith',
        senderName: 'Alice Johnson',
        timestamp: new Date().toISOString(),
        alertMessage: 'Login from new device detected'
      };

      const mergedData = { ...defaultSampleData, ...sampleData };

      return await this.renderTemplate(templateType, channel, mergedData, {
        language,
        useCustomTemplate: true
      });
    } catch (error) {
      console.error('❌ Error previewing template:', error);
      throw error;
    }
  }

  /**
   * Validate template syntax
   */
  validateTemplate(templateString) {
    try {
      const errors = [];

      // Check for unmatched braces
      const openBraces = (templateString.match(/\{\{/g) || []).length;
      const closeBraces = (templateString.match(/\}\}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        errors.push('Unmatched template braces');
      }

      // Check for unmatched conditionals
      const ifStatements = (templateString.match(/\{\{#if/g) || []).length;
      const endIfStatements = (templateString.match(/\{\{\/if\}\}/g) || []).length;
      
      if (ifStatements !== endIfStatements) {
        errors.push('Unmatched conditional statements');
      }

      // Check for invalid function calls
      const functionCalls = templateString.match(/\{\{(\w+)\s+[^}]+\}\}/g) || [];
      functionCalls.forEach(call => {
        const funcName = call.match(/\{\{(\w+)/)[1];
        if (!this.templateFunctions[funcName]) {
          errors.push(`Unknown function: ${funcName}`);
        }
      });

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Template validation failed: ' + error.message]
      };
    }
  }

  /**
   * Clear template cache
   */
  clearTemplateCache(type = null, channel = null, language = null) {
    if (type && channel && language) {
      const cacheKey = `${type}_${channel}_${language}`;
      this.templateCache.delete(cacheKey);
    } else {
      this.templateCache.clear();
    }
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages() {
    return Object.keys(this.localization).map(code => ({
      code,
      name: this.getLanguageName(code)
    }));
  }

  /**
   * Get language name
   */
  getLanguageName(code) {
    const names = {
      en: 'English',
      sw: 'Kiswahili',
      lg: 'Luganda'
    };
    return names[code] || code;
  }

  /**
   * Get template variables for a template type
   */
  getTemplateVariables(templateType) {
    const commonVariables = [
      'userName', 'timestamp', 'formatDateTime', 'formatDate', 
      'formatCurrency', 'formatRelativeTime'
    ];

    const typeSpecificVariables = {
      transaction_completed: ['amount', 'currency', 'reference', 'transactionType', 'recipient'],
      money_received: ['amount', 'currency', 'reference', 'senderName'],
      security_alert: ['alertMessage', 'location', 'device'],
      fraud_alert: ['alertMessage', 'riskLevel', 'riskScore'],
      limit_warning: ['limitType', 'currentUsage', 'limit']
    };

    return [
      ...commonVariables,
      ...(typeSpecificVariables[templateType] || [])
    ];
  }
}

export default new TemplateEngineService();
