/**
 * Transaction Limits Service
 * Enforces daily, weekly, and monthly transaction limits based on user verification levels
 * Provides real-time limit checking and management
 */

import { supabase } from './supabaseClient';
import { formatCurrency } from '../utils/currencyUtils';

// Verification Level Limits (in UGX)
const VERIFICATION_LIMITS = {
  basic: {
    daily: 500000,    // UGX 500K
    weekly: 2000000,  // UGX 2M
    monthly: 8000000, // UGX 8M
    single: 100000,   // UGX 100K per transaction
    description: 'Basic verification - Phone verified'
  },
  enhanced: {
    daily: 2000000,   // UGX 2M
    weekly: 10000000, // UGX 10M
    monthly: 40000000, // UGX 40M
    single: 500000,   // UGX 500K per transaction
    description: 'Enhanced verification - ID document verified'
  },
  premium: {
    daily: 10000000,  // UGX 10M
    weekly: 50000000, // UGX 50M
    monthly: 200000000, // UGX 200M
    single: 2000000,  // UGX 2M per transaction
    description: 'Premium verification - Full KYC completed'
  }
};

class TransactionLimitsService {
  constructor() {
    this.limits = VERIFICATION_LIMITS;
  }

  /**
   * Get user's current verification level and limits
   */
  async getUserLimits(userId) {
    try {
      console.log('🔍 Getting user limits for:', userId);

      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('kyc_level, kyc_status')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('❌ Error fetching user profile:', error);
        throw error;
      }

      // Determine verification level based on KYC status
      let verificationLevel = 'basic';
      if (profile.kyc_status === 'verified' && profile.kyc_level >= 2) {
        verificationLevel = 'enhanced';
      }
      if (profile.kyc_status === 'verified' && profile.kyc_level >= 3) {
        verificationLevel = 'premium';
      }

      const limits = this.limits[verificationLevel];
      
      console.log('✅ User limits retrieved:', { verificationLevel, limits });
      
      return {
        verificationLevel,
        limits,
        profile: {
          kycLevel: profile.kyc_level,
          kycStatus: profile.kyc_status
        }
      };
    } catch (error) {
      console.error('❌ Error getting user limits:', error);
      throw error;
    }
  }

  /**
   * Get user's transaction usage for specified period
   */
  async getTransactionUsage(userId, period = 'daily') {
    try {
      console.log('📊 Getting transaction usage for:', userId, period);

      const now = new Date();
      let startDate;

      switch (period) {
        case 'daily':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          startDate = new Date(now);
          startDate.setDate(now.getDate() - dayOfWeek);
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'monthly':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          throw new Error('Invalid period specified');
      }

      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount, type, status, created_at')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .gte('created_at', startDate.toISOString())
        .in('type', ['send_money', 'bill_payment', 'airtime_purchase', 'withdrawal']);

      if (error) {
        console.error('❌ Error fetching transactions:', error);
        throw error;
      }

      const totalAmount = transactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0);
      const transactionCount = transactions.length;

      console.log('✅ Transaction usage calculated:', { 
        period, 
        totalAmount, 
        transactionCount,
        startDate 
      });

      return {
        totalAmount,
        transactionCount,
        transactions,
        period,
        startDate,
        endDate: now
      };
    } catch (error) {
      console.error('❌ Error getting transaction usage:', error);
      throw error;
    }
  }

  /**
   * Check if a transaction would exceed limits
   */
  async checkTransactionLimits(userId, amount, transactionType = 'send_money') {
    try {
      console.log('🔍 Checking transaction limits:', { userId, amount, transactionType });

      // Get user limits
      const { verificationLevel, limits } = await this.getUserLimits(userId);

      // Check single transaction limit
      if (amount > limits.single) {
        return {
          allowed: false,
          reason: 'single_transaction_limit',
          message: `Transaction amount ${formatCurrency(amount)} exceeds single transaction limit of ${formatCurrency(limits.single)}`,
          limit: limits.single,
          verificationLevel
        };
      }

      // Check daily limit
      const dailyUsage = await this.getTransactionUsage(userId, 'daily');
      if (dailyUsage.totalAmount + amount > limits.daily) {
        return {
          allowed: false,
          reason: 'daily_limit',
          message: `Transaction would exceed daily limit of ${formatCurrency(limits.daily)}`,
          currentUsage: dailyUsage.totalAmount,
          limit: limits.daily,
          remaining: limits.daily - dailyUsage.totalAmount,
          verificationLevel
        };
      }

      // Check weekly limit
      const weeklyUsage = await this.getTransactionUsage(userId, 'weekly');
      if (weeklyUsage.totalAmount + amount > limits.weekly) {
        return {
          allowed: false,
          reason: 'weekly_limit',
          message: `Transaction would exceed weekly limit of ${formatCurrency(limits.weekly)}`,
          currentUsage: weeklyUsage.totalAmount,
          limit: limits.weekly,
          remaining: limits.weekly - weeklyUsage.totalAmount,
          verificationLevel
        };
      }

      // Check monthly limit
      const monthlyUsage = await this.getTransactionUsage(userId, 'monthly');
      if (monthlyUsage.totalAmount + amount > limits.monthly) {
        return {
          allowed: false,
          reason: 'monthly_limit',
          message: `Transaction would exceed monthly limit of ${formatCurrency(limits.monthly)}`,
          currentUsage: monthlyUsage.totalAmount,
          limit: limits.monthly,
          remaining: limits.monthly - monthlyUsage.totalAmount,
          verificationLevel
        };
      }

      console.log('✅ Transaction within limits');
      
      return {
        allowed: true,
        verificationLevel,
        limits,
        usage: {
          daily: dailyUsage,
          weekly: weeklyUsage,
          monthly: monthlyUsage
        }
      };
    } catch (error) {
      console.error('❌ Error checking transaction limits:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive limit status for user dashboard
   */
  async getLimitStatus(userId) {
    try {
      console.log('📊 Getting comprehensive limit status for:', userId);

      const { verificationLevel, limits, profile } = await this.getUserLimits(userId);
      
      const [dailyUsage, weeklyUsage, monthlyUsage] = await Promise.all([
        this.getTransactionUsage(userId, 'daily'),
        this.getTransactionUsage(userId, 'weekly'),
        this.getTransactionUsage(userId, 'monthly')
      ]);

      const status = {
        verificationLevel,
        limits,
        profile,
        usage: {
          daily: {
            used: dailyUsage.totalAmount,
            limit: limits.daily,
            remaining: limits.daily - dailyUsage.totalAmount,
            percentage: (dailyUsage.totalAmount / limits.daily) * 100,
            transactionCount: dailyUsage.transactionCount
          },
          weekly: {
            used: weeklyUsage.totalAmount,
            limit: limits.weekly,
            remaining: limits.weekly - weeklyUsage.totalAmount,
            percentage: (weeklyUsage.totalAmount / limits.weekly) * 100,
            transactionCount: weeklyUsage.transactionCount
          },
          monthly: {
            used: monthlyUsage.totalAmount,
            limit: limits.monthly,
            remaining: limits.monthly - monthlyUsage.totalAmount,
            percentage: (monthlyUsage.totalAmount / limits.monthly) * 100,
            transactionCount: monthlyUsage.transactionCount
          }
        },
        recommendations: this.generateRecommendations(verificationLevel, dailyUsage, weeklyUsage, monthlyUsage, limits)
      };

      console.log('✅ Limit status compiled');
      return status;
    } catch (error) {
      console.error('❌ Error getting limit status:', error);
      throw error;
    }
  }

  /**
   * Generate recommendations for limit optimization
   */
  generateRecommendations(verificationLevel, dailyUsage, weeklyUsage, monthlyUsage, limits) {
    const recommendations = [];

    // Check if user is approaching limits
    const dailyPercentage = (dailyUsage.totalAmount / limits.daily) * 100;
    const weeklyPercentage = (weeklyUsage.totalAmount / limits.weekly) * 100;
    const monthlyPercentage = (monthlyUsage.totalAmount / limits.monthly) * 100;

    if (dailyPercentage > 80) {
      recommendations.push({
        type: 'warning',
        title: 'Daily Limit Warning',
        message: `You've used ${dailyPercentage.toFixed(1)}% of your daily limit`,
        action: 'Consider upgrading verification level for higher limits'
      });
    }

    if (weeklyPercentage > 80) {
      recommendations.push({
        type: 'warning',
        title: 'Weekly Limit Warning',
        message: `You've used ${weeklyPercentage.toFixed(1)}% of your weekly limit`,
        action: 'Monitor your spending or upgrade verification'
      });
    }

    if (verificationLevel === 'basic') {
      recommendations.push({
        type: 'upgrade',
        title: 'Upgrade to Enhanced Verification',
        message: 'Increase your limits by 4x with ID verification',
        action: 'Complete enhanced verification'
      });
    } else if (verificationLevel === 'enhanced') {
      recommendations.push({
        type: 'upgrade',
        title: 'Upgrade to Premium Verification',
        message: 'Get the highest limits with full KYC',
        action: 'Complete premium verification'
      });
    }

    return recommendations;
  }

  /**
   * Record limit violation for monitoring
   */
  async recordLimitViolation(userId, amount, reason, metadata = {}) {
    try {
      console.log('⚠️ Recording limit violation:', { userId, amount, reason });

      const { error } = await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: 'limit_violation',
          severity: 'medium',
          details: {
            amount,
            reason,
            timestamp: new Date().toISOString(),
            ...metadata
          },
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('❌ Error recording limit violation:', error);
        throw error;
      }

      console.log('✅ Limit violation recorded');
    } catch (error) {
      console.error('❌ Error recording limit violation:', error);
      // Don't throw - this is logging only
    }
  }
}

export default new TransactionLimitsService();
