import { EAST_AFRICAN_COUNTRIES } from './countriesConfig.js';

/**
 * Phone Validation Utilities for JiraniPay
 * Handles phone number formatting, validation, and network provider detection
 * Optimized for Uganda and East African phone number formats
 */

/**
 * Clean phone number by removing all non-digit characters
 * @param {string} phoneNumber Raw phone number
 * @returns {string} Cleaned phone number with only digits
 */
export function cleanPhoneNumber(phoneNumber) {
  if (!phoneNumber) return '';
  return phoneNumber.replace(/\D/g, '');
}

/**
 * Format phone number to international format
 * @param {string} phoneNumber Raw phone number
 * @param {string} countryCode Country code (default: 'UG')
 * @returns {string} Formatted phone number
 */
export function formatPhoneNumber(phoneNumber, countryCode = 'UG') {
  const cleaned = cleanPhoneNumber(phoneNumber);
  const country = EAST_AFRICAN_COUNTRIES[countryCode];
  
  if (!country) {
    return phoneNumber; // Return original if country not supported
  }

  // Remove country code if present
  let localNumber = cleaned;
  const countryDigits = country.code.replace('+', '');
  
  if (localNumber.startsWith(countryDigits)) {
    localNumber = localNumber.substring(countryDigits.length);
  }
  
  // Remove leading zero if present
  if (localNumber.startsWith('0')) {
    localNumber = localNumber.substring(1);
  }

  // Validate length
  if (localNumber.length !== country.phoneLength) {
    return phoneNumber; // Return original if invalid length
  }

  return `${country.code}${localNumber}`;
}

/**
 * Detect network provider from phone number
 * @param {string} phoneNumber Phone number (formatted or unformatted)
 * @param {string} countryCode Country code (default: 'UG')
 * @returns {Object} Network provider information
 */
export function detectNetworkProvider(phoneNumber, countryCode = 'UG') {
  const cleaned = cleanPhoneNumber(phoneNumber);
  const country = EAST_AFRICAN_COUNTRIES[countryCode];
  
  if (!country) {
    return {
      provider: 'Unknown',
      name: 'Unknown Provider',
      color: '#666666'
    };
  }

  // Extract local number
  let localNumber = cleaned;
  const countryDigits = country.code.replace('+', '');
  
  if (localNumber.startsWith(countryDigits)) {
    localNumber = localNumber.substring(countryDigits.length);
  }
  
  if (localNumber.startsWith('0')) {
    localNumber = localNumber.substring(1);
  }

  // Get first 2 digits for provider detection
  const prefix = localNumber.substring(0, 2);

  // Find matching network provider
  for (const [providerKey, providerData] of Object.entries(country.networks)) {
    if (providerData.prefixes.includes(prefix)) {
      return {
        provider: providerKey,
        name: providerData.name,
        color: providerData.color,
        prefix: prefix
      };
    }
  }

  return {
    provider: 'Unknown',
    name: 'Unknown Provider',
    color: '#666666',
    prefix: prefix
  };
}

/**
 * Validate phone number format and provider
 * @param {string} phoneNumber Phone number to validate
 * @param {string} countryCode Country code (default: 'UG')
 * @returns {Object} Validation result with details
 */
export function validatePhoneNumber(phoneNumber, countryCode = 'UG') {
  const result = {
    isValid: false,
    formatted: '',
    networkProvider: null,
    errors: []
  };

  if (!phoneNumber) {
    result.errors.push('Phone number is required');
    return result;
  }

  const cleaned = cleanPhoneNumber(phoneNumber);
  const country = EAST_AFRICAN_COUNTRIES[countryCode];

  if (!country) {
    result.errors.push(`Country ${countryCode} is not supported`);
    return result;
  }

  // Check if phone number is too short or too long
  if (cleaned.length < 9) {
    result.errors.push('Phone number is too short');
    return result;
  }

  if (cleaned.length > 15) {
    result.errors.push('Phone number is too long');
    return result;
  }

  // Format the phone number
  const formatted = formatPhoneNumber(phoneNumber, countryCode);
  
  // Extract local number for validation
  let localNumber = cleaned;
  const countryDigits = country.code.replace('+', '');
  
  if (localNumber.startsWith(countryDigits)) {
    localNumber = localNumber.substring(countryDigits.length);
  }
  
  if (localNumber.startsWith('0')) {
    localNumber = localNumber.substring(1);
  }

  // Validate local number length
  if (localNumber.length !== country.phoneLength) {
    result.errors.push(`Invalid phone number length for ${country.name}`);
    return result;
  }

  // Detect network provider
  const networkProvider = detectNetworkProvider(phoneNumber, countryCode);
  
  if (networkProvider.provider === 'Unknown') {
    result.errors.push('Unknown network provider');
    // Don't return here - still consider it valid for international numbers
  }

  // If we reach here, the phone number is valid
  result.isValid = true;
  result.formatted = formatted;
  result.networkProvider = networkProvider;

  return result;
}

/**
 * Check if phone number is from Uganda
 * @param {string} phoneNumber Phone number to check
 * @returns {boolean} True if Uganda number
 */
export function isUgandaNumber(phoneNumber) {
  const cleaned = cleanPhoneNumber(phoneNumber);
  return cleaned.startsWith('256') || 
         (cleaned.startsWith('0') && cleaned.length === 10) ||
         (cleaned.length === 9 && !cleaned.startsWith('0'));
}

/**
 * Get phone number display format for UI
 * @param {string} phoneNumber Phone number
 * @param {boolean} showCountryCode Whether to show country code
 * @returns {string} Display formatted phone number
 */
export function getDisplayFormat(phoneNumber, showCountryCode = true) {
  const validation = validatePhoneNumber(phoneNumber);
  
  if (!validation.isValid) {
    return phoneNumber; // Return original if invalid
  }

  const formatted = validation.formatted;
  
  if (!showCountryCode && formatted.startsWith('+256')) {
    // Convert to local format: +256777123456 -> 0777123456
    const localPart = formatted.substring(4); // Remove +256
    return `0${localPart}`;
  }

  return formatted;
}

/**
 * Validate multiple phone numbers
 * @param {Array} phoneNumbers Array of phone numbers
 * @param {string} countryCode Country code
 * @returns {Array} Array of validation results
 */
export function validateMultiplePhoneNumbers(phoneNumbers, countryCode = 'UG') {
  return phoneNumbers.map(phone => ({
    phoneNumber: phone,
    ...validatePhoneNumber(phone, countryCode)
  }));
}

/**
 * Get network provider color for UI styling
 * @param {string} phoneNumber Phone number
 * @param {string} countryCode Country code
 * @returns {string} Hex color code
 */
export function getNetworkColor(phoneNumber, countryCode = 'UG') {
  const provider = detectNetworkProvider(phoneNumber, countryCode);
  return provider.color;
}

/**
 * Check if two phone numbers are the same
 * @param {string} phone1 First phone number
 * @param {string} phone2 Second phone number
 * @returns {boolean} True if same number
 */
export function isSamePhoneNumber(phone1, phone2) {
  const formatted1 = formatPhoneNumber(phone1);
  const formatted2 = formatPhoneNumber(phone2);
  return formatted1 === formatted2;
}

/**
 * Generate phone number suggestions for autocomplete
 * @param {string} input Partial phone number input
 * @param {string} countryCode Country code
 * @returns {Array} Array of suggested completions
 */
export function generatePhoneNumberSuggestions(input, countryCode = 'UG') {
  const country = EAST_AFRICAN_COUNTRIES[countryCode];
  if (!country) return [];

  const cleaned = cleanPhoneNumber(input);
  const suggestions = [];

  // If input is empty or very short, suggest common prefixes
  if (cleaned.length <= 2) {
    Object.values(country.networks).forEach(network => {
      network.prefixes.forEach(prefix => {
        suggestions.push({
          suggestion: `0${prefix}`,
          provider: network.name,
          color: network.color
        });
      });
    });
  }

  return suggestions.slice(0, 5); // Limit to 5 suggestions
}
