-- =====================================================
-- JiraniPay Predictive Analytics & Budgeting Schema
-- Extends existing database for budget management and forecasting
-- =====================================================

-- User Budgets Table
-- Stores user-created budgets with categories and limits
CREATE TABLE user_budgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  budget_type VARCHAR(50) DEFAULT 'monthly', -- monthly, weekly, yearly
  total_amount DECIMAL(15,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'UGX',
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  is_auto_generated BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraint
  CONSTRAINT fk_user_budgets_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT check_budget_amount_positive 
    CHECK (total_amount > 0),
  CONSTRAINT check_budget_dates 
    CHECK (end_date IS NULL OR end_date >= start_date)
);

-- Budget Categories Table
-- Defines spending categories within budgets
CREATE TABLE budget_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  budget_id UUID NOT NULL,
  category_name VARCHAR(100) NOT NULL,
  category_id VARCHAR(100), -- Links to transaction categories
  allocated_amount DECIMAL(15,2) NOT NULL,
  spent_amount DECIMAL(15,2) DEFAULT 0,
  icon VARCHAR(50) DEFAULT 'folder',
  color VARCHAR(7) DEFAULT '#B2BEC3',
  priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high
  is_essential BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraint
  CONSTRAINT fk_budget_categories_budget_id 
    FOREIGN KEY (budget_id) REFERENCES user_budgets(id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT check_allocated_amount_positive 
    CHECK (allocated_amount >= 0),
  CONSTRAINT check_spent_amount_non_negative 
    CHECK (spent_amount >= 0),
  CONSTRAINT check_priority_range 
    CHECK (priority BETWEEN 1 AND 3)
);

-- Budget Alerts Table
-- Stores budget alert configurations and history
CREATE TABLE budget_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  budget_id UUID,
  category_id UUID,
  user_id UUID NOT NULL,
  alert_type VARCHAR(50) NOT NULL, -- threshold, overspend, forecast
  threshold_percentage INTEGER DEFAULT 80, -- Alert when X% of budget is reached
  threshold_amount DECIMAL(15,2),
  is_enabled BOOLEAN DEFAULT true,
  notification_channels JSONB DEFAULT '["push"]', -- push, email, sms
  last_triggered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraints
  CONSTRAINT fk_budget_alerts_budget_id 
    FOREIGN KEY (budget_id) REFERENCES user_budgets(id) ON DELETE CASCADE,
  CONSTRAINT fk_budget_alerts_category_id 
    FOREIGN KEY (category_id) REFERENCES budget_categories(id) ON DELETE CASCADE,
  CONSTRAINT fk_budget_alerts_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT check_threshold_percentage_range 
    CHECK (threshold_percentage BETWEEN 1 AND 200),
  CONSTRAINT check_threshold_amount_positive 
    CHECK (threshold_amount IS NULL OR threshold_amount > 0)
);

-- Spending Forecasts Table
-- Stores generated spending predictions and forecasts
CREATE TABLE spending_forecasts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  forecast_type VARCHAR(50) NOT NULL, -- monthly, quarterly, category
  forecast_period VARCHAR(20) NOT NULL, -- 2025-01, 2025-Q1, etc.
  category_id VARCHAR(100),
  predicted_amount DECIMAL(15,2) NOT NULL,
  lower_bound DECIMAL(15,2),
  upper_bound DECIMAL(15,2),
  confidence_score DECIMAL(3,2), -- 0.00 to 1.00
  actual_amount DECIMAL(15,2), -- Filled in after the period
  accuracy_score DECIMAL(3,2), -- Calculated after period ends
  model_version VARCHAR(20) DEFAULT 'v1.0',
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  period_start_date DATE NOT NULL,
  period_end_date DATE NOT NULL,
  
  -- Foreign key constraint
  CONSTRAINT fk_spending_forecasts_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT check_predicted_amount_positive 
    CHECK (predicted_amount >= 0),
  CONSTRAINT check_confidence_score_range 
    CHECK (confidence_score IS NULL OR confidence_score BETWEEN 0 AND 1),
  CONSTRAINT check_accuracy_score_range 
    CHECK (accuracy_score IS NULL OR accuracy_score BETWEEN 0 AND 1),
  CONSTRAINT check_forecast_dates 
    CHECK (period_end_date >= period_start_date)
);

-- Budget Performance Table
-- Tracks budget performance metrics and analytics
CREATE TABLE budget_performance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  budget_id UUID NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  total_budgeted DECIMAL(15,2) NOT NULL,
  total_spent DECIMAL(15,2) NOT NULL,
  variance_amount DECIMAL(15,2) NOT NULL, -- spent - budgeted
  variance_percentage DECIMAL(5,2) NOT NULL,
  categories_over_budget INTEGER DEFAULT 0,
  categories_under_budget INTEGER DEFAULT 0,
  performance_score DECIMAL(3,2), -- 0.00 to 1.00
  insights JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraint
  CONSTRAINT fk_budget_performance_budget_id 
    FOREIGN KEY (budget_id) REFERENCES user_budgets(id) ON DELETE CASCADE,
    
  -- Check constraints
  CONSTRAINT check_performance_dates 
    CHECK (period_end >= period_start),
  CONSTRAINT check_performance_score_range 
    CHECK (performance_score IS NULL OR performance_score BETWEEN 0 AND 1)
);

-- Predictive Models Table
-- Stores model parameters and performance metrics
CREATE TABLE predictive_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  model_type VARCHAR(50) NOT NULL, -- spending_forecast, budget_suggestion
  model_version VARCHAR(20) NOT NULL,
  parameters JSONB NOT NULL DEFAULT '{}',
  training_data_period INTEGER DEFAULT 12, -- months
  accuracy_metrics JSONB DEFAULT '{}',
  last_trained_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraint
  CONSTRAINT fk_predictive_models_user_id 
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- =====================================================
-- Indexes for Performance Optimization
-- =====================================================

-- User budgets indexes
CREATE INDEX IF NOT EXISTS idx_user_budgets_user_id 
ON user_budgets(user_id) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_user_budgets_dates 
ON user_budgets(start_date, end_date) WHERE is_active = true;

-- Budget categories indexes
CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_id 
ON budget_categories(budget_id);

CREATE INDEX IF NOT EXISTS idx_budget_categories_category_id 
ON budget_categories(category_id);

-- Budget alerts indexes
CREATE INDEX IF NOT EXISTS idx_budget_alerts_user_id 
ON budget_alerts(user_id) WHERE is_enabled = true;

CREATE INDEX IF NOT EXISTS idx_budget_alerts_budget_id 
ON budget_alerts(budget_id) WHERE is_enabled = true;

-- Spending forecasts indexes
CREATE INDEX IF NOT EXISTS idx_spending_forecasts_user_id 
ON spending_forecasts(user_id);

CREATE INDEX IF NOT EXISTS idx_spending_forecasts_period 
ON spending_forecasts(forecast_period, forecast_type);

CREATE INDEX IF NOT EXISTS idx_spending_forecasts_dates 
ON spending_forecasts(period_start_date, period_end_date);

-- Budget performance indexes
CREATE INDEX IF NOT EXISTS idx_budget_performance_budget_id 
ON budget_performance(budget_id);

CREATE INDEX IF NOT EXISTS idx_budget_performance_dates 
ON budget_performance(period_start, period_end);

-- =====================================================
-- Row Level Security (RLS) Policies
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE spending_forecasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE predictive_models ENABLE ROW LEVEL SECURITY;

-- User budgets policies
DROP POLICY IF EXISTS user_budgets_policy ON user_budgets;
CREATE POLICY user_budgets_policy ON user_budgets
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Budget categories policies
DROP POLICY IF EXISTS budget_categories_policy ON budget_categories;
CREATE POLICY budget_categories_policy ON budget_categories
  FOR ALL USING (
    budget_id IN (
      SELECT id FROM user_budgets WHERE user_id::text = auth.uid()::text
    )
  );

-- Budget alerts policies
DROP POLICY IF EXISTS budget_alerts_policy ON budget_alerts;
CREATE POLICY budget_alerts_policy ON budget_alerts
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Spending forecasts policies
DROP POLICY IF EXISTS spending_forecasts_policy ON spending_forecasts;
CREATE POLICY spending_forecasts_policy ON spending_forecasts
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Budget performance policies
DROP POLICY IF EXISTS budget_performance_policy ON budget_performance;
CREATE POLICY budget_performance_policy ON budget_performance
  FOR ALL USING (
    budget_id IN (
      SELECT id FROM user_budgets WHERE user_id::text = auth.uid()::text
    )
  );

-- Predictive models policies
DROP POLICY IF EXISTS predictive_models_policy ON predictive_models;
CREATE POLICY predictive_models_policy ON predictive_models
  FOR ALL USING (auth.uid()::text = user_id::text);

-- =====================================================
-- Functions and Triggers
-- =====================================================

-- Function to update budget spent amounts
CREATE OR REPLACE FUNCTION update_budget_spent_amount()
RETURNS TRIGGER AS $$
BEGIN
  -- Update spent amount when transactions are added/modified
  UPDATE budget_categories 
  SET spent_amount = (
    SELECT COALESCE(SUM(ABS(amount)), 0)
    FROM transactions 
    WHERE user_id = NEW.user_id 
    AND category = budget_categories.category_id
    AND created_at >= (
      SELECT start_date FROM user_budgets 
      WHERE id = budget_categories.budget_id
    )
    AND (
      SELECT end_date FROM user_budgets 
      WHERE id = budget_categories.budget_id
    ) IS NULL OR created_at <= (
      SELECT end_date FROM user_budgets 
      WHERE id = budget_categories.budget_id
    )
  ),
  updated_at = NOW()
  WHERE budget_id IN (
    SELECT id FROM user_budgets 
    WHERE user_id = NEW.user_id AND is_active = true
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update budget amounts on transaction changes
DROP TRIGGER IF EXISTS trigger_update_budget_spent ON transactions;
CREATE TRIGGER trigger_update_budget_spent
  AFTER INSERT OR UPDATE OR DELETE ON transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_budget_spent_amount();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at columns
DROP TRIGGER IF EXISTS update_user_budgets_updated_at ON user_budgets;
CREATE TRIGGER update_user_budgets_updated_at 
  BEFORE UPDATE ON user_budgets 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_budget_categories_updated_at ON budget_categories;
CREATE TRIGGER update_budget_categories_updated_at 
  BEFORE UPDATE ON budget_categories 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Sample Data (Optional)
-- =====================================================

-- Insert sample budget categories for reference
-- Note: Replace 'your-user-id-here' with actual user_id

/*
-- Sample budget
INSERT INTO user_budgets (user_id, name, description, total_amount, start_date) VALUES
  ('your-user-id-here', 'Monthly Budget', 'AI-generated monthly budget', 2000000, CURRENT_DATE);

-- Sample budget categories
INSERT INTO budget_categories (budget_id, category_name, category_id, allocated_amount, priority, is_essential) VALUES
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Food & Dining', 'food_dining', 600000, 3, true),
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Transportation', 'transportation', 300000, 3, true),
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Bills & Utilities', 'bills_utilities', 400000, 3, true),
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Entertainment', 'entertainment', 200000, 1, false),
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Shopping', 'shopping', 300000, 2, false),
  ((SELECT id FROM user_budgets WHERE name = 'Monthly Budget' LIMIT 1), 'Healthcare', 'healthcare', 200000, 2, true);
*/

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check if tables were created successfully
-- SELECT table_name FROM information_schema.tables 
-- WHERE table_schema = 'public' 
-- AND table_name IN ('user_budgets', 'budget_categories', 'budget_alerts', 'spending_forecasts', 'budget_performance', 'predictive_models');

-- Check indexes
-- SELECT indexname FROM pg_indexes WHERE tablename IN ('user_budgets', 'budget_categories', 'spending_forecasts');
