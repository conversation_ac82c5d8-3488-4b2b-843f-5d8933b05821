import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Translation System Debugger Component
 * 
 * This component provides real-time debugging of the translation system
 * to identify exactly what's happening with the t() function
 */
const TranslationSystemDebugger = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [error, setError] = useState(null);

  // Try to get the language context
  let languageContext = null;
  let contextError = null;
  
  try {
    languageContext = useLanguage();
  } catch (err) {
    contextError = err.message;
  }

  useEffect(() => {
    const runDebugTests = () => {
      try {
        const info = {
          timestamp: new Date().toISOString(),
          contextAvailable: !!languageContext,
          contextError: contextError,
        };

        if (languageContext) {
          const { t, currentLanguage, isInitialized, currentLanguageConfig } = languageContext;
          
          info.currentLanguage = currentLanguage;
          info.isInitialized = isInitialized;
          info.hasTranslationFunction = typeof t === 'function';
          info.currentLanguageConfig = currentLanguageConfig;

          if (typeof t === 'function') {
            // Test critical keys
            const testKeys = [
              'editProfile',
              'fullName', 
              'emailAddress',
              'phoneNumber',
              'accountVerification',
              'privacyAndData'
            ];

            info.translationTests = {};
            testKeys.forEach(key => {
              try {
                const result = t(key);
                info.translationTests[key] = {
                  input: key,
                  output: result,
                  isWorking: result !== key,
                  outputType: typeof result
                };
              } catch (err) {
                info.translationTests[key] = {
                  input: key,
                  error: err.message,
                  isWorking: false
                };
              }
            });

            // Test nested keys
            const nestedTestKeys = [
              'common.save',
              'profile.editProfile',
              'common.fullName'
            ];

            info.nestedTranslationTests = {};
            nestedTestKeys.forEach(key => {
              try {
                const result = t(key);
                info.nestedTranslationTests[key] = {
                  input: key,
                  output: result,
                  isWorking: result !== key,
                  outputType: typeof result
                };
              } catch (err) {
                info.nestedTranslationTests[key] = {
                  input: key,
                  error: err.message,
                  isWorking: false
                };
              }
            });

            // Test if translations object is available
            try {
              // Try to access the internal translations
              info.translationsAvailable = true;
              info.translationObjectType = 'accessible';
            } catch (err) {
              info.translationsAvailable = false;
              info.translationError = err.message;
            }
          }
        }

        setDebugInfo(info);
      } catch (err) {
        setError(err.message);
      }
    };

    runDebugTests();
    
    // Re-run every 2 seconds to catch initialization changes
    const interval = setInterval(runDebugTests, 2000);
    
    return () => clearInterval(interval);
  }, [languageContext, contextError]);

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>🚨 Translation System Debug - ERROR</Text>
        <Text style={styles.error}>Error: {error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔍 Translation System Debug</Text>
      <Text style={styles.timestamp}>Last Update: {debugInfo.timestamp}</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📋 Context Status</Text>
        <Text style={styles.item}>Context Available: {debugInfo.contextAvailable ? '✅ YES' : '❌ NO'}</Text>
        {debugInfo.contextError && (
          <Text style={styles.error}>Context Error: {debugInfo.contextError}</Text>
        )}
        {debugInfo.contextAvailable && (
          <>
            <Text style={styles.item}>Current Language: {debugInfo.currentLanguage || 'undefined'}</Text>
            <Text style={styles.item}>Is Initialized: {debugInfo.isInitialized ? '✅ YES' : '❌ NO'}</Text>
            <Text style={styles.item}>Has t() Function: {debugInfo.hasTranslationFunction ? '✅ YES' : '❌ NO'}</Text>
            <Text style={styles.item}>Language Config: {debugInfo.currentLanguageConfig ? '✅ Available' : '❌ Missing'}</Text>
          </>
        )}
      </View>

      {debugInfo.translationTests && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔑 Direct Key Tests</Text>
          {Object.entries(debugInfo.translationTests).map(([key, test]) => (
            <View key={key} style={styles.testItem}>
              <Text style={styles.testKey}>{key}:</Text>
              {test.error ? (
                <Text style={styles.error}>ERROR: {test.error}</Text>
              ) : (
                <>
                  <Text style={[styles.testResult, test.isWorking ? styles.success : styles.failure]}>
                    "{test.output}" {test.isWorking ? '✅' : '❌'}
                  </Text>
                  <Text style={styles.testType}>Type: {test.outputType}</Text>
                </>
              )}
            </View>
          ))}
        </View>
      )}

      {debugInfo.nestedTranslationTests && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔗 Nested Key Tests</Text>
          {Object.entries(debugInfo.nestedTranslationTests).map(([key, test]) => (
            <View key={key} style={styles.testItem}>
              <Text style={styles.testKey}>{key}:</Text>
              {test.error ? (
                <Text style={styles.error}>ERROR: {test.error}</Text>
              ) : (
                <>
                  <Text style={[styles.testResult, test.isWorking ? styles.success : styles.failure]}>
                    "{test.output}" {test.isWorking ? '✅' : '❌'}
                  </Text>
                  <Text style={styles.testType}>Type: {test.outputType}</Text>
                </>
              )}
            </View>
          ))}
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📊 Summary</Text>
        <Text style={styles.item}>
          Translation System Status: {
            debugInfo.contextAvailable && debugInfo.hasTranslationFunction && debugInfo.isInitialized
              ? '✅ WORKING'
              : '❌ BROKEN'
          }
        </Text>
        {debugInfo.translationTests && (
          <Text style={styles.item}>
            Working Keys: {Object.values(debugInfo.translationTests).filter(t => t.isWorking).length} / {Object.keys(debugInfo.translationTests).length}
          </Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    margin: 10,
    borderRadius: 5,
    borderWidth: 2,
    borderColor: '#007AFF',
    maxHeight: 400,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#007AFF',
  },
  timestamp: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  section: {
    marginBottom: 15,
    padding: 8,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  item: {
    fontSize: 12,
    marginBottom: 4,
    color: '#333',
  },
  testItem: {
    marginBottom: 8,
    padding: 4,
    backgroundColor: '#f9f9f9',
    borderRadius: 3,
  },
  testKey: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  testResult: {
    fontSize: 11,
    marginTop: 2,
  },
  testType: {
    fontSize: 10,
    color: '#666',
    fontStyle: 'italic',
  },
  success: {
    color: 'green',
  },
  failure: {
    color: 'red',
  },
  error: {
    color: 'red',
    fontSize: 11,
    fontStyle: 'italic',
  },
});

export default TranslationSystemDebugger;
