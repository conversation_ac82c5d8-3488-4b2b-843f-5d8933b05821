/**
 * Environment Configuration for JiraniPay
 *
 * This file manages environment-specific settings for development, staging, and production.
 *
 * 🔒 SECURITY NOTICE:
 * This file has been updated to use secure environment configuration.
 * All credentials are now loaded from environment variables.
 *
 * SETUP INSTRUCTIONS:
 * 1. Use the new secure configuration system in config/secureEnvironment.js
 * 2. Create separate Supabase projects for each environment
 * 3. Set up environment files using templates (.env.example, etc.)
 * 4. Never hardcode credentials in source code
 *
 * For detailed setup instructions, see: docs/SECURE_ENVIRONMENT_SETUP.md
 */

import secureConfig from './secureEnvironment';

// Environment detection using secure configuration
const isDevelopment = secureConfig.isDevelopment();
const isStaging = secureConfig.isStaging();
const isProduction = secureConfig.isProduction();

// Production mode flag - determined by environment
const PRODUCTION_MODE = isProduction;

// Base configuration
const config = {
  // App Information
  app: {
    name: 'JiraniPay',
    version: '1.0.0',
    bundleId: 'com.jiranipay.app',
  },

  // Environment flags
  environment: {
    isDevelopment,
    isProduction,
    enableLogging: isDevelopment,
    enableDebugMode: isDevelopment,
  },

  // API Configuration
  api: {
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },

  // Authentication Configuration
  auth: {
    // OTP Configuration
    otp: {
      length: 6,
      expiryMinutes: 5,
      resendCooldownSeconds: 60,
    },
    
    // Password Requirements
    password: {
      minLength: 8,
      requireNumbers: true,
      requireSpecialChars: true,
      requireUppercase: false, // More user-friendly for East African users
    },

    // Session Configuration
    session: {
      autoRefresh: true,
      persistSession: true,
      sessionTimeoutHours: 24,
    },

    // Biometric Configuration
    biometric: {
      enabled: true,
      promptMessage: 'Authenticate to access your account',
      fallbackToPassword: true,
    },
  },

  // Regional Configuration for East Africa
  regional: {
    defaultCountry: 'UG', // Uganda
    supportedCountries: ['UG', 'KE', 'TZ', 'RW', 'BI', 'SS', 'ET', 'SO', 'DJ', 'ER'],
    defaultCurrency: 'UGX',
    defaultLanguage: 'en',
    supportedLanguages: ['en', 'sw', 'fr', 'ar', 'am'], // English, Swahili, French, Arabic, Amharic
  },

  // Feature Flags
  features: {
    biometricAuth: true,
    multiLanguage: true,
    darkMode: true,
    offlineMode: false, // Future feature
    pushNotifications: false, // Future feature
  },
};

// Development Environment Configuration
const developmentConfig = {
  ...config,
  ...secureConfig,

  // Override with development-specific settings
  supabase: {
    ...secureConfig.supabase,
    enableRealTimeSubscriptions: false,
    enableEdgeFunctions: false,
  },

  api: {
    ...config.api,
    ...secureConfig.api,
  },

  logging: secureConfig.logging,
  security: secureConfig.security,
  features: secureConfig.features,
};

// Staging Environment Configuration
const stagingConfig = {
  ...config,
  ...secureConfig,

  // Override with staging-specific settings
  supabase: {
    ...secureConfig.supabase,
    enableRealTimeSubscriptions: true,
    enableEdgeFunctions: true,
  },

  api: {
    ...config.api,
    ...secureConfig.api,
  },

  logging: secureConfig.logging,
  security: secureConfig.security,
  features: secureConfig.features,
};

// Production Environment Configuration
const productionConfig = {
  ...config,
  ...secureConfig,

  // Override with production-specific settings
  supabase: {
    ...secureConfig.supabase,
    enableRealTimeSubscriptions: true,
    enableEdgeFunctions: true,
  },
  
  // Production API endpoints
  api: {
    ...config.api,
    baseUrl: 'https://api.jiranipay.com', // Your production API
    // Real API endpoints for production
    endpoints: {
      // Authentication
      auth: 'https://auth.jiranipay.com',

      // Financial Services
      mobileMoneyApi: 'https://api.jiranipay.com/mobile-money',
      bankingApi: 'https://api.jiranipay.com/banking',
      billPaymentApi: 'https://api.jiranipay.com/bills',

      // External APIs
      mtnApi: 'https://api.mtn.com/v1', // MTN Mobile Money API
      airtelApi: 'https://api.airtel.com/v1', // Airtel Money API
      umemeApi: 'https://api.umeme.co.ug/v1', // UMEME API
      dstvApi: 'https://api.dstv.com/v1', // DStv API

      // Currency and Exchange
      exchangeRateApi: 'https://api.exchangerate-api.com/v4/latest/UGX',

      // Compliance and KYC
      kycApi: 'https://api.jiranipay.com/kyc',
      complianceApi: 'https://api.jiranipay.com/compliance',
    }
  },
  
  // Production logging
  logging: {
    level: 'error',
    enableConsoleLogging: false,
    enableRemoteLogging: true,
  },

  // Production Security Configuration
  security: {
    enableEncryption: true,
    enableAuditTrails: true,
    enableFraudDetection: true,
    enableKYCValidation: true,
    maxTransactionAmount: ********, // UGX 10M
    dailyTransactionLimit: ********, // UGX 50M
    requireBiometricForHighValue: true,
    highValueThreshold: 500000, // UGX 500K
  },

  // Compliance Configuration
  compliance: {
    enableAMLChecks: true,
    enableSanctionScreening: true,
    enableTransactionMonitoring: true,
    reportingEnabled: true,
    dataRetentionYears: 7,
    requireKYCForAmounts: 1000000, // UGX 1M
  },
};

// Export the appropriate configuration based on environment
const currentConfig = isProduction
  ? productionConfig
  : isStaging
    ? stagingConfig
    : developmentConfig;

// Validation function to ensure all required configurations are set
export const validateConfiguration = () => {
  // Use the secure configuration validation
  const secureConfigErrors = secureConfig.validate();

  const errors = [...secureConfigErrors];

  // Additional legacy validation for compatibility
  if (currentConfig.supabase && currentConfig.supabase.url) {
    if (!currentConfig.supabase.url.startsWith('https://')) {
      errors.push('Invalid Supabase URL format');
    }
  }

  if (currentConfig.supabase && currentConfig.supabase.anonKey) {
    if (!currentConfig.supabase.anonKey.startsWith('eyJ')) {
      errors.push('Invalid Supabase anon key format');
    }
  }

  // Validate required app configuration
  if (!currentConfig.app.name || !currentConfig.app.version) {
    errors.push('App information incomplete');
  }

  if (errors.length > 0) {
    if (isDevelopment) {
            console.error('🚨 CONFIGURATION ERRORS:');
      errors.forEach(error => console.error(`- ${error}`));
      return false;
    } else {
      // Production: Log error and throw
      console.error('Configuration validation failed');
      throw new Error('Production environment requires valid configuration');
    }
  }

  return true;
};

// Utility functions (defined after all configuration to avoid circular dependencies)
export const isProductionMode = () => PRODUCTION_MODE || isProduction;
export const getEnvironmentName = () => {
  if (PRODUCTION_MODE || isProduction) return 'production';
  return 'development';
};

// Initialize configuration silently in production mode
// Only show configuration details in development
if (isDevelopment && !PRODUCTION_MODE) {
  console.log('🚀 JiraniPay Configuration Loaded:');
  console.log(`- Environment: ${getEnvironmentName().toUpperCase()}`);
  console.log(`- App Version: ${currentConfig.app.version}`);
  console.log(`- Default Country: ${currentConfig.regional.defaultCountry}`);
  console.log(`- Supported Languages: ${currentConfig.regional.supportedLanguages.join(', ')}`);
  console.log('🔧 Development mode active');

  // Validate configuration in development
  validateConfiguration();
} else if (PRODUCTION_MODE) {
  // Silent initialization in production mode
  // Only validate configuration without logging
  validateConfiguration();
}

export default currentConfig;

// Export additional utilities for production mode management
export { PRODUCTION_MODE, isDevelopment, isProduction };
