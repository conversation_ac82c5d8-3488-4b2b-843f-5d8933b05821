import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import DateTimePicker from '@react-native-community/datetimepicker';

import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';
import CountrySelector from '../components/CountrySelector';
import LanguageSelector from '../components/LanguageSelector';
import ResponsiveText from '../components/ResponsiveText';

const EditProfileScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { onProfileUpdated } = route.params || {};

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  // Profile state
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone_number: '',
    date_of_birth: null,
    country_code: 'UG',
    preferred_language: 'en',
  });

  // Form validation
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // Load profile data
  useEffect(() => {
    loadProfile();

    // Animate screen entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const user = authService.getCurrentUser();
      if (!user) {
        Alert.alert(t('common.error'), t('auth.userNotAuthenticated'));
        navigation.goBack();
        return;
      }

      const profileData = await profileManagementService.getProfile(user.id);
      if (profileData.success) {
        const data = profileData.data;
        setProfile(data);
        setFormData({
          full_name: data.full_name || '',
          email: data.email || '',
          phone_number: data.phone_number || '',
          date_of_birth: data.date_of_birth ? new Date(data.date_of_birth) : null,
          country_code: data.country_code || 'UG',
          preferred_language: data.preferred_language || 'en',
        });
      } else {
        throw new Error(profileData.error || t('profile.loadingProfile'));
      }
    } catch (error) {
      console.error('❌ Failed to load profile:', error);
      Alert.alert(t('common.error'), error.message);
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Handle field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Validation
  const validateForm = () => {
    const newErrors = {};

    // Full name validation
    if (!formData.full_name.trim()) {
      newErrors.full_name = t('profile.nameRequired');
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = t('profile.nameRequired');
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email && !isValidEmail(formData.email)) {
      newErrors.email = t('profile.validEmailRequired');
    }

    // Phone number validation
    if (!formData.phone_number.trim()) {
      newErrors.phone_number = t('profile.validPhoneRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Save profile
  const handleSave = async () => {
    if (!validateForm()) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(t('common.error'), t('common.pleaseCorrectErrors'));
      return;
    }

    setSaving(true);
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        throw new Error(t('auth.userNotAuthenticated'));
      }

      const updateData = {
        full_name: formData.full_name.trim(),
        email: formData.email.trim() || null,
        phone_number: formData.phone_number.trim(),
        country_code: formData.country_code,
        preferred_language: formData.preferred_language,
        date_of_birth: formData.date_of_birth ?
          formData.date_of_birth.toISOString().split('T')[0] : null,
      };

      const result = await profileManagementService.updateProfile(user.id, updateData);

      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(
          t('common.success'),
          t('profile.profileUpdated'),
          [
            {
              text: t('common.ok'),
              onPress: () => {
                if (onProfileUpdated) onProfileUpdated();
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        throw new Error(result.error || t('profile.profileUpdateFailed'));
      }
    } catch (error) {
      console.error('❌ Profile update failed:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(t('common.error'), error.message);
    } finally {
      setSaving(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (hasChanges) {
      Alert.alert(
        t('common.unsavedChanges'),
        t('common.unsavedChangesMessage'),
        [
          { text: t('common.cancel'), style: 'cancel' },
          {
            text: t('common.discard'),
            style: 'destructive',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  // Date picker handlers
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleFieldChange('date_of_birth', selectedDate);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          {t('profile.loadingProfile')}
        </Text>
      </View>
    );
  }

  const styles = createStyles(theme);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <LinearGradient
        colors={[Colors.primary.main, Colors.primary.dark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.neutral.white} />
          </TouchableOpacity>

          <ResponsiveText style={styles.headerTitle}>
            {t('profile.editProfile')}
          </ResponsiveText>

          <TouchableOpacity
            style={[styles.saveButton, (!hasChanges || saving) && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={!hasChanges || saving}
            activeOpacity={0.7}
          >
            {saving ? (
              <ActivityIndicator size="small" color={Colors.neutral.white} />
            ) : (
              <ResponsiveText style={styles.saveButtonText}>
                {t('profile.save')}
              </ResponsiveText>
            )}
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Form Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.form}>
            {/* Personal Information Section */}
            <View style={styles.section}>
              <ResponsiveText style={styles.sectionTitle}>
                {t('profile.personalInformation')}
              </ResponsiveText>

              {/* Full Name */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.fullName')} *
                </ResponsiveText>
                <TextInput
                  style={[styles.textInput, errors.full_name && styles.inputError]}
                  value={formData.full_name}
                  onChangeText={(value) => handleFieldChange('full_name', value)}
                  placeholder={t('profile.fullName')}
                  placeholderTextColor={theme.colors.textSecondary}
                  autoCapitalize="words"
                  returnKeyType="next"
                />
                {errors.full_name && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.full_name}
                  </ResponsiveText>
                )}
              </View>

              {/* Email Address */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.emailAddress')}
                </ResponsiveText>
                <TextInput
                  style={[styles.textInput, errors.email && styles.inputError]}
                  value={formData.email}
                  onChangeText={(value) => handleFieldChange('email', value)}
                  placeholder={t('profile.emailAddress')}
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  returnKeyType="next"
                />
                {errors.email && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.email}
                  </ResponsiveText>
                )}
              </View>

              {/* Phone Number */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.phoneNumber')} *
                </ResponsiveText>
                <TextInput
                  style={[styles.textInput, errors.phone_number && styles.inputError]}
                  value={formData.phone_number}
                  onChangeText={(value) => handleFieldChange('phone_number', value)}
                  placeholder={t('profile.phoneNumber')}
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="phone-pad"
                  returnKeyType="next"
                />
                {errors.phone_number && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.phone_number}
                  </ResponsiveText>
                )}
              </View>

              {/* Date of Birth */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.dateOfBirth')}
                </ResponsiveText>
                <TouchableOpacity
                  style={[styles.dateInput, errors.date_of_birth && styles.inputError]}
                  onPress={() => setShowDatePicker(true)}
                >
                  <ResponsiveText style={[styles.dateText, !formData.date_of_birth && styles.placeholderText]}>
                    {formData.date_of_birth
                      ? formData.date_of_birth.toLocaleDateString()
                      : t('profile.dateOfBirth')
                    }
                  </ResponsiveText>
                  <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
                </TouchableOpacity>
                {errors.date_of_birth && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.date_of_birth}
                  </ResponsiveText>
                )}
              </View>

              {/* Country */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.country')}
                </ResponsiveText>
                <CountrySelector
                  selectedCountryCode={formData.country_code}
                  onCountrySelect={(countryCode) => handleFieldChange('country_code', countryCode)}
                  style={styles.selectorContainer}
                />
                {errors.country_code && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.country_code}
                  </ResponsiveText>
                )}
              </View>

              {/* Language Preference */}
              <View style={styles.fieldContainer}>
                <ResponsiveText style={styles.fieldLabel}>
                  {t('profile.preferredLanguage')}
                </ResponsiveText>
                <LanguageSelector
                  selectedCountryCode={formData.country_code}
                  onLanguageChange={(languageCode) => handleFieldChange('preferred_language', languageCode)}
                  style={styles.selectorContainer}
                />
                {errors.preferred_language && (
                  <ResponsiveText style={styles.errorText}>
                    {errors.preferred_language}
                  </ResponsiveText>
                )}
              </View>
            </View>

            {/* Bottom spacing */}
            <View style={styles.bottomSpacing} />
          </View>
        </ScrollView>
      </Animated.View>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.date_of_birth || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          maximumDate={new Date()}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: Colors.neutral.white,
    fontWeight: '600',
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: theme.colors.text,
  },
  inputError: {
    borderColor: Colors.status.error,
  },
  errorText: {
    color: Colors.status.error,
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  placeholderText: {
    color: theme.colors.textSecondary,
  },
  selectorContainer: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default EditProfileScreen;