import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import CountrySelector from '../components/CountrySelector';
import LanguageSelector from '../components/LanguageSelector';
import authService from '../services/authService';
import databaseService from '../services/databaseService';
import profileManagementService from '../services/profileManagementService';

const EditProfileScreen = ({ navigation, route }) => {
  // Get current profile data from route params
  const { userProfile: initialProfile } = route.params || {};
  
  // Form state
  const [formData, setFormData] = useState({
    full_name: initialProfile?.full_name || '',
    email: initialProfile?.email || '',
    phone_number: initialProfile?.phone_number || '',
    date_of_birth: initialProfile?.date_of_birth ? new Date(initialProfile.date_of_birth) : null,
    country_code: initialProfile?.country_code || 'UG',
    preferred_language: initialProfile?.preferred_language || 'en',
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // Theme and language
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Track changes
  useEffect(() => {
    const hasFormChanges = 
      formData.full_name !== (initialProfile?.full_name || '') ||
      formData.email !== (initialProfile?.email || '') ||
      formData.phone_number !== (initialProfile?.phone_number || '') ||
      formData.country_code !== (initialProfile?.country_code || 'UG') ||
      formData.preferred_language !== (initialProfile?.preferred_language || 'en') ||
      (formData.date_of_birth && initialProfile?.date_of_birth && 
       formData.date_of_birth.toISOString().split('T')[0] !== initialProfile.date_of_birth);
    
    setHasChanges(hasFormChanges);
  }, [formData, initialProfile]);

  // Validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhoneNumber = (phone) => {
    // Uganda phone number validation (can be extended for other East African countries)
    const ugandaPhoneRegex = /^(\+256|0)?[7][0-9]{8}$/;
    return ugandaPhoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validateForm = () => {
    const newErrors = {};

    // Full name validation
    if (!formData.full_name.trim()) {
      newErrors.full_name = t('fullNameRequired');
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = t('fullNameMinLength');
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email && !validateEmail(formData.email)) {
      newErrors.email = t('validEmailRequired');
    }

    // Phone number validation
    if (!formData.phone_number.trim()) {
      newErrors.phone_number = t('phoneNumberRequired');
    } else if (!validatePhoneNumber(formData.phone_number)) {
      newErrors.phone_number = t('validUgandaPhoneRequired');
    }

    // Date of birth validation (optional but must be reasonable if provided)
    if (formData.date_of_birth) {
      const today = new Date();
      const age = today.getFullYear() - formData.date_of_birth.getFullYear();
      if (age < 13 || age > 120) {
        newErrors.date_of_birth = t('validDateOfBirthRequired');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Handle date selection
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleFieldChange('date_of_birth', selectedDate);
    }
  };

  // Save profile changes
  const handleSaveProfile = async () => {
    if (!validateForm()) {
      Alert.alert(t('validationError'), t('pleaseCorrectTheErrorsAndTryAgain'));
      return;
    }

    setSaving(true);
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        throw new Error(t('userNotAuthenticated'));
      }

      // Prepare update data
      const updateData = {
        full_name: formData.full_name.trim(),
        email: formData.email.trim() || null,
        phone_number: formData.phone_number.trim(),
        country_code: formData.country_code,
        preferred_language: formData.preferred_language,
        date_of_birth: formData.date_of_birth ? formData.date_of_birth.toISOString().split('T')[0] : null,
      };

      console.log('💾 Saving profile data for user:', user.id);
      console.log('📝 Update data:', updateData);

      // Update profile using profileManagementService
      const result = await profileManagementService.updateProfile(user.id, updateData);

      console.log('📊 Profile update result:', result);

      if (result.success) {
        console.log('✅ Profile updated successfully');
        Alert.alert(t('success'), t('yourProfileHasBeenUpdatedSuccessfully'),
          [
            {
              text: t('ok'),
              onPress: () => {
                // Navigate back with success flag
                navigation.goBack();
                // The ProfileScreen will refresh automatically when it comes back into focus
              }
            }
          ]
        );
      } else {
        console.error('❌ Profile update failed:', result.error);
        throw new Error(result.error || t('profileUpdateFailed'));
      }
    } catch (error) {
      console.error('❌ Error updating profile:', error);

      // Provide specific error messages based on error type
      let errorMessage = t('failedToUpdateProfile');

      if (error.message.includes('JSON object requested')) {
        errorMessage = t('profileUpdateFailedDatabase');
      } else if (error.message.includes('network')) {
        errorMessage = t('networkError');
      } else if (error.message.includes('validation')) {
        errorMessage = t('validationError');
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        t('profileUpdateFailedTitle'),
        errorMessage,
        [{ text: t('ok') }]
      );
    } finally {
      setSaving(false);
    }
  };

  // Handle back navigation with unsaved changes
  const handleBackPress = () => {
    if (hasChanges) {
      Alert.alert(t('unsavedChanges'), t('youHaveUnsavedChangesAreYouSureYouWantToGoBack'),
        [
          { text: t('cancel'), style: 'cancel' },
          { text: t('discard'), style: 'destructive', onPress: () => navigation.goBack() }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={handleBackPress} />
        <Text style={styles.headerTitle}>{t('editProfile')}</Text>
        <TouchableOpacity
          style={[styles.saveButton, (!hasChanges || saving) && styles.saveButtonDisabled]}
          onPress={handleSaveProfile}
          disabled={!hasChanges || saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color={Colors.neutral.white} />
          ) : (
            <Text style={styles.saveButtonText}>{t('save')}</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Full Name */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('fullName')}</Text>
          <TextInput
            style={[styles.textInput, errors.full_name && styles.inputError]}
            value={formData.full_name}
            onChangeText={(value) => handleFieldChange('full_name', value)}
            placeholder={t('enterYourFullName')}
            placeholderTextColor={theme.colors.textSecondary}
            autoCapitalize="words"
          />
          {errors.full_name && <Text style={styles.errorText}>{errors.full_name}</Text>}
        </View>

        {/* Email */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('emailAddress')}</Text>
          <TextInput
            style={[styles.textInput, errors.email && styles.inputError]}
            value={formData.email}
            onChangeText={(value) => handleFieldChange('email', value)}
            placeholder={t('enterYourEmailAddress')}
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
          />
          {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
        </View>

        {/* Phone Number */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('phoneNumber')}</Text>
          <TextInput
            style={[styles.textInput, errors.phone_number && styles.inputError]}
            value={formData.phone_number}
            onChangeText={(value) => handleFieldChange('phone_number', value)}
            placeholder={t('enterYourPhoneNumber')}
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="phone-pad"
          />
          {errors.phone_number && <Text style={styles.errorText}>{errors.phone_number}</Text>}
        </View>

        {/* Date of Birth */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('dateOfBirth')}</Text>
          <TouchableOpacity
            style={[styles.dateInput, errors.date_of_birth && styles.inputError]}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={[styles.dateText, !formData.date_of_birth && styles.placeholderText]}>
              {formData.date_of_birth
                ? formData.date_of_birth.toLocaleDateString()
                : t('selectYourDateOfBirth')
              }
            </Text>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          {errors.date_of_birth && <Text style={styles.errorText}>{errors.date_of_birth}</Text>}
        </View>

        {/* Country */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('country')}</Text>
          <CountrySelector
            selectedCountryCode={formData.country_code}
            onCountrySelect={(countryCode, country) => handleFieldChange('country_code', countryCode)}
            style={styles.selectorContainer}
          />
          {errors.country_code && (
            <Text style={styles.errorText}>{errors.country_code}</Text>
          )}
        </View>

        {/* Language Preference */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>{t('preferredLanguage')}</Text>
          <LanguageSelector
            selectedCountryCode={formData.country_code}
            onLanguageChange={(languageCode) => handleFieldChange('preferred_language', languageCode)}
            style={styles.selectorContainer}
          />
          {errors.preferred_language && (
            <Text style={styles.errorText}>{errors.preferred_language}</Text>
          )}
        </View>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.date_of_birth || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          maximumDate={new Date()}
          minimumDate={new Date(1900, 0, 1)}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  saveButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.textSecondary,
    opacity: 0.6,
  },
  saveButtonText: {
    color: Colors.neutral.white,
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: theme.colors.surface,
    color: theme.colors.text,
    minHeight: 56,
  },
  inputError: {
    borderColor: Colors.status.error,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 56,
  },
  dateText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  placeholderText: {
    color: theme.colors.textSecondary,
  },
  countryText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  languageText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: Colors.status.error,
    marginTop: 4,
  },
  selectorContainer: {
    marginBottom: 0,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default EditProfileScreen;
