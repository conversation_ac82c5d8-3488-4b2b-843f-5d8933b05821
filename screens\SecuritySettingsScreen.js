import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import PinKeypad from '../components/PinKeypad';
import securityManagementService from '../services/securityManagementService';
import authService from '../services/authService';

const SecuritySettingsScreen = ({ navigation }) => {
  // Use theme and language contexts
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [securitySettings, setSecuritySettings] = useState(null);
  const [biometricAvailability, setBiometricAvailability] = useState(null);
  const [showPinModal, setShowPinModal] = useState(false);
  const [securityLevel, setSecurityLevel] = useState(null);
  const [pinAction, setPinAction] = useState(''); // 'setup', 'change'
  const [currentPin, setCurrentPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');

  useEffect(() => {
    loadSecurityData();
  }, []);

  // Add focus listener to reload settings when returning to screen
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('🔄 SecuritySettingsScreen focused, reloading data');
      loadSecurityData();
    });

    return unsubscribe;
  }, [navigation]);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        
        // Load security settings from database
        const settingsResult = await securityManagementService.getSecuritySettings(currentUser.id);
        if (settingsResult.success) {
          // Also check SecureStore for biometric setting to ensure consistency
          const biometricEnabled = await securityManagementService.isBiometricEnabled();

          const mergedSettings = {
            ...settingsResult.data,
            biometric_enabled: biometricEnabled // Use SecureStore value as source of truth
          };

          console.log('🔍 Loaded security settings:', mergedSettings);
          setSecuritySettings(mergedSettings);
        }

        // Check biometric availability
        const biometricResult = await securityManagementService.checkBiometricAvailability();
        if (biometricResult.success) {
          setBiometricAvailability(biometricResult.data);
        }

        // Calculate security level
        const securityLevelResult = await securityManagementService.calculateSecurityLevel(currentUser.id);
        if (securityLevelResult.success) {
          console.log('🔒 Security level calculated:', securityLevelResult);
          setSecurityLevel(securityLevelResult);
        }
      }
    } catch (error) {
      console.error('❌ Error loading security data:', error);
      Alert.alert(t('error'), t('failedToLoadSecuritySettings'));
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricToggle = async (enabled) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      if (!biometricAvailability?.isAvailable) {
        Alert.alert(
          t('errors.biometricNotAvailable'),
          t('security.biometricNotAvailableMessage'),
          [{ text: t('common.ok') }]
        );
        return;
      }

      let result;
      if (enabled) {
        result = await securityManagementService.enableBiometricAuth(user.id);
      } else {
        result = await securityManagementService.disableBiometricAuth(user.id);
      }

      if (result.success) {
        // Update local state
        setSecuritySettings(prev => ({
          ...prev,
          biometric_enabled: enabled
        }));

        // Verify the setting was actually saved by checking SecureStore
        const actualState = await securityManagementService.isBiometricEnabled();
        console.log('🔍 Biometric state after toggle:', { requested: enabled, actual: actualState });

        if (actualState === enabled) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          Alert.alert(
            t('success'),
            enabled ? t('success.biometricEnabled') : t('success.biometricDisabled')
          );
        } else {
          // State mismatch - reload settings
          console.log('⚠️ State mismatch detected, reloading settings');
          await loadSecurityData();
          Alert.alert(t('warning'), t('settingMayNotHaveBeenSavedCorrectlyPleaseTryAgain'));
        }
      } else {
        Alert.alert(t('error'), result.error);
      }
    } catch (error) {
      console.error('❌ Error toggling biometric:', error);
      Alert.alert(t('error'), t('failedToUpdateBiometricSettings'));
    }
  };

  const getSecurityLevelColor = (level) => {
    switch (level) {
      case 'high': return theme.colors.success;
      case 'medium': return theme.colors.warning;
      case 'basic': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getSecurityLevelIcon = (level) => {
    switch (level) {
      case 'high': return 'shield-checkmark';
      case 'medium': return 'shield';
      case 'basic': return 'shield-outline';
      default: return 'shield-outline';
    }
  };

  const handlePinSetup = () => {
    setPinAction('setup');
    setCurrentPin('');
    setNewPin('');
    setConfirmPin('');
    setShowPinModal(true);
  };

  const handlePinChange = () => {
    setPinAction('change');
    setCurrentPin('');
    setNewPin('');
    setConfirmPin('');
    setShowPinModal(true);
  };

  const processPinAction = async () => {
    try {
      if (pinAction === 'setup') {
        if (newPin.length !== 6) {
          Alert.alert(t('error'), t('pinMustBe6Digits'));
          return;
        }
        if (newPin !== confirmPin) {
          Alert.alert(t('error'), t('pinsDoNotMatch'));
          return;
        }

        const result = await securityManagementService.setupPIN(user.id, newPin);
        if (result.success) {
          setSecuritySettings(prev => ({ ...prev, pin_enabled: true }));
          setShowPinModal(false);
          setCurrentPin('');
          setNewPin('');
          setConfirmPin('');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          Alert.alert(t('success'), t('pinSetUpSuccessfully'));
        } else {
          Alert.alert(t('error'), result.error);
        }
      } else if (pinAction === 'change') {
        if (currentPin.length !== 6) {
          Alert.alert(t('error'), t('currentPinMustBe6Digits'));
          return;
        }
        if (newPin.length !== 6) {
          Alert.alert(t('error'), t('newPinMustBe6Digits'));
          return;
        }
        if (newPin !== confirmPin) {
          Alert.alert(t('error'), t('newPinsDoNotMatch'));
          return;
        }

        const result = await securityManagementService.changePIN(user.id, currentPin, newPin);
        if (result.success) {
          setShowPinModal(false);
          setCurrentPin('');
          setNewPin('');
          setConfirmPin('');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          Alert.alert(t('success'), t('pinChangedSuccessfully'));
        } else {
          Alert.alert(t('error'), result.error);
        }
      }
    } catch (error) {
      console.error('❌ Error processing PIN action:', error);
      Alert.alert(t('error'), t('failedToProcessPinAction'));
    }
  };

  // PIN input handlers
  const handlePinNumberPress = (number) => {
    if (pinAction === 'setup') {
      if (newPin.length < 6 && confirmPin.length === 0) {
        setNewPin(prev => prev + number);
      } else if (newPin.length === 6 && confirmPin.length < 6) {
        setConfirmPin(prev => prev + number);
      }
    } else if (pinAction === 'change') {
      if (currentPin.length < 6) {
        setCurrentPin(prev => prev + number);
      } else if (newPin.length < 6 && confirmPin.length === 0) {
        setNewPin(prev => prev + number);
      } else if (newPin.length === 6 && confirmPin.length < 6) {
        setConfirmPin(prev => prev + number);
      }
    }
  };

  const handlePinBackspace = () => {
    if (pinAction === 'setup') {
      if (confirmPin.length > 0) {
        setConfirmPin(prev => prev.slice(0, -1));
      } else if (newPin.length > 0) {
        setNewPin(prev => prev.slice(0, -1));
      }
    } else if (pinAction === 'change') {
      if (confirmPin.length > 0) {
        setConfirmPin(prev => prev.slice(0, -1));
      } else if (newPin.length > 0) {
        setNewPin(prev => prev.slice(0, -1));
      } else if (currentPin.length > 0) {
        setCurrentPin(prev => prev.slice(0, -1));
      }
    }
  };

  const handlePinClear = () => {
    setCurrentPin('');
    setNewPin('');
    setConfirmPin('');
  };

  const renderSecurityOption = (icon, title, subtitle, value, onToggle, iconColor, disabled = false) => (
    <View style={[styles.securityOption, disabled && styles.disabledOption]}>
      <View style={styles.optionLeft}>
        <View style={[styles.optionIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          <Text style={styles.optionSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: theme.colors.border, true: theme.colors.primary + '40' }}
        thumbColor={value ? theme.colors.primary : theme.colors.textSecondary}
        disabled={disabled}
      />
    </View>
  );

  const renderActionOption = (icon, title, subtitle, onPress, iconColor, showChevron = true) => (
    <TouchableOpacity style={styles.actionOption} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.optionLeft}>
        <View style={[styles.optionIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          <Text style={styles.optionSubtitle}>{subtitle}</Text>
        </View>
      </View>
      {showChevron && (
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('loadingSecuritySettings')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Modern Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={theme.colors.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('profile.securitySettings')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('security.protectYourAccount')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Security Level Status */}
        {securityLevel && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('security.securityLevel')}</Text>
            <View style={[styles.securityLevelCard, { borderColor: getSecurityLevelColor(securityLevel.level) }]}>
              <View style={styles.securityLevelHeader}>
                <View style={[styles.securityLevelBadge, { backgroundColor: getSecurityLevelColor(securityLevel.level) }]}>
                  <Ionicons
                    name={getSecurityLevelIcon(securityLevel.level)}
                    size={20}
                    color={theme.colors.white}
                  />
                  <Text style={styles.securityLevelText}>
                    {securityLevel.level.toUpperCase()}
                  </Text>
                </View>
                <Text style={styles.securityScore}>{securityLevel.score}/100</Text>
              </View>
              <Text style={styles.securityDescription}>{securityLevel.description}</Text>
              {securityLevel.recommendations && securityLevel.recommendations.length > 0 && (
                <View style={styles.recommendationsContainer}>
                  <Text style={styles.recommendationsTitle}>{t('security.recommendations')}</Text>
                  {securityLevel.recommendations.slice(0, 2).map((rec, index) => (
                    <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                  ))}
                </View>
              )}
            </View>
          </View>
        )}

        {/* Authentication Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('security.authenticationMethods')}</Text>
          
          {renderSecurityOption(
            'finger-print',
            t('profile.biometricAuthentication'),
            biometricAvailability?.isAvailable
              ? t('profile.enableBiometric')
              : t('errors.biometricNotAvailable'),
            securitySettings?.biometric_enabled || false,
            handleBiometricToggle,
            Colors.primary.main,
            !biometricAvailability?.isAvailable
          )}

          {renderActionOption(
            'keypad',
            securitySettings?.pin_enabled ? t('profile.changePin') : t('profile.setUpPin'),
            securitySettings?.pin_enabled
              ? t('profile.updateAccountPassword')
              : t('profile.createSixDigitPin'),
            securitySettings?.pin_enabled ? handlePinChange : handlePinSetup,
            Colors.secondary.lake
          )}
        </View>

        {/* Account Security */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('security.accountSecurity')}</Text>
          
          {renderActionOption(
            'shield-checkmark',
            t('profile.twoFactorAuth'),
            t('profile.addExtraLayerSecurity'),
            () => navigation.navigate('TwoFactorAuth'),
            Colors.accent.gold
          )}

          {renderActionOption(
            'time',
            t('profile.sessionTimeout'),
            t('profile.sessionWillTimeoutAfterMinutes', { minutes: securitySettings?.session_timeout_minutes || 30 }),
            () => navigation.navigate('SessionTimeout'),
            Colors.secondary.forest
          )}

          {renderActionOption(
            'phone-portrait',
            t('profile.trustedDevices'),
            t('profile.manageDeviceAccess'),
            () => navigation.navigate('TrustedDevices'),
            Colors.primary.main
          )}
        </View>

        {/* Security Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('security.securityInformation')}</Text>
          
          {renderActionOption(
            'document-text',
            t('profile.securityActivity'),
            t('profile.viewRecentSecurityEvents'),
            () => navigation.navigate('SecurityActivity'),
            Colors.neutral.charcoal,
            true
          )}

          {renderActionOption(
            'help-circle',
            t('profile.securityTips'),
            t('profile.learnAccountSecurity'),
            () => navigation.navigate('SecurityTips'),
            Colors.accent.gold,
            true
          )}
        </View>
      </ScrollView>

      {/* PIN Setup/Change Modal */}
      <Modal
        visible={showPinModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowPinModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowPinModal(false)}>
              <Text style={styles.modalCancel}>{t('common.cancel')}</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {pinAction === 'setup' ? t('profile.setUpPin') : t('profile.changePin')}
            </Text>
            <TouchableOpacity onPress={processPinAction}>
              <Text style={styles.modalSave}>{t('common.save')}</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            {pinAction === 'change' && (
              <View style={styles.pinInputGroup}>
                <Text style={styles.pinLabel}>{t('currentPin')}</Text>
                <Text style={styles.pinInput}>{currentPin.replace(/./g, '•')}</Text>
              </View>
            )}
            
            <View style={styles.pinInputGroup}>
              <Text style={styles.pinLabel}>
                {t('profile.newPin')}
              </Text>
              <Text style={styles.pinInput}>{newPin.replace(/./g, '•')}</Text>
            </View>
            
            <View style={styles.pinInputGroup}>
              <Text style={styles.pinLabel}>{t('confirmPin')}</Text>
              <Text style={styles.pinInput}>{confirmPin.replace(/./g, '•')}</Text>
            </View>

            {/* PIN Keypad */}
            <PinKeypad
              onNumberPress={handlePinNumberPress}
              onBackspace={handlePinBackspace}
              onClear={handlePinClear}
            />

            <Text style={styles.pinNote}>
              {t('enterYour6digitPinUsingTheKeypadAbove')}
            </Text>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: theme.colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 15,
  },
  securityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  disabledOption: {
    opacity: 0.6,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalCancel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalSave: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  pinInputGroup: {
    marginBottom: 20,
  },
  pinLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  pinInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    padding: 15,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 8,
    letterSpacing: 8,
  },
  pinNote: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 20,
  },
  securityLevelCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    marginBottom: 8,
  },
  securityLevelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  securityLevelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  securityLevelText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '700',
  },
  securityScore: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
  },
  securityDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  recommendationsContainer: {
    marginTop: 8,
  },
  recommendationsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  recommendationItem: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
});

export default SecuritySettingsScreen;
