/**
 * BottomNavigation i18n Verification Test
 * 
 * Tests to verify that BottomNavigation.js is properly internationalized
 * and all tab labels use translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Verifying BottomNavigation.js i18n Implementation\n');

// Read the BottomNavigation.js file
const bottomNavigationPath = path.join(__dirname, '../components/BottomNavigation.js');
const bottomNavigationContent = fs.readFileSync(bottomNavigationPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = bottomNavigationContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = bottomNavigationContent.includes('const { t }') || bottomNavigationContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded tab labels (should not exist)
console.log('\n✅ Test 3: No hardcoded tab labels');
const hardcodedLabels = [
  'Home',
  'Bills', 
  'QR Pay',
  'Wallet',
  'Profile'
];

let hardcodedFound = [];
hardcodedLabels.forEach(label => {
  // Check if the label appears as a hardcoded string (not in translation key)
  const lines = bottomNavigationContent.split('\n');
  let foundAsHardcoded = false;
  lines.forEach(line => {
    if ((line.includes(`'${label}'`) || line.includes(`"${label}"`)) && 
        !line.includes('t(') && 
        !line.trim().startsWith('//') && 
        !line.trim().startsWith('*')) {
      foundAsHardcoded = true;
    }
  });
  if (foundAsHardcoded) {
    hardcodedFound.push(label);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   No hardcoded tab labels found: ✅ PASS');
} else {
  console.log('   Hardcoded tab labels found: ❌ FAIL');
  hardcodedFound.forEach(label => console.log(`     - "${label}"`));
}

// Test 4: Check for proper navigation translation key usage
console.log('\n✅ Test 4: Navigation translation keys usage');
const navigationKeys = [
  't(\'navigation.home\')',
  't(\'navigation.bills\')',
  't(\'navigation.qrPay\')',
  't(\'navigation.wallet\')',
  't(\'navigation.profile\')'
];

let keysFound = 0;
navigationKeys.forEach(key => {
  if (bottomNavigationContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Navigation keys found: ${keysFound}/${navigationKeys.length} ${keysFound === navigationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required navigation keys exist in en.js
console.log('\n✅ Test 5: Navigation keys in en.js');
const requiredNavKeys = [
  'home:',
  'bills:',
  'qrPay:',
  'wallet:',
  'profile:'
];

let navKeysInTranslations = 0;
requiredNavKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    navKeysInTranslations++;
  }
});

console.log(`   Required navigation keys in en.js: ${navKeysInTranslations}/${requiredNavKeys.length} ${navKeysInTranslations === requiredNavKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check tabs array structure with translation keys
console.log('\n✅ Test 6: Tabs array structure');
const hasTabsArray = bottomNavigationContent.includes('const tabs = [') || bottomNavigationContent.includes('tabs = [');
const tabsUseTranslations = bottomNavigationContent.includes('title: t(\'navigation.');
console.log(`   Tabs array with translations: ${hasTabsArray && tabsUseTranslations ? '✅ PASS' : '❌ FAIL'}`);

// Test 7: Check for icon configuration
console.log('\n✅ Test 7: Icon configuration');
const hasIconConfig = bottomNavigationContent.includes('icon:') && 
                      bottomNavigationContent.includes('iconType:');
console.log(`   Icon configuration present: ${hasIconConfig ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check for center QR button special handling
console.log('\n✅ Test 8: Center QR button configuration');
const hasCenterButton = bottomNavigationContent.includes('isCenter: true');
console.log(`   Center QR button configured: ${hasCenterButton ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (keysFound === navigationKeys.length) passedTests++;
if (navKeysInTranslations === requiredNavKeys.length) passedTests++;
if (hasTabsArray && tabsUseTranslations) passedTests++;
if (hasIconConfig) passedTests++;
if (hasCenterButton) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 BottomNavigation.js i18n implementation: ✅ VERIFIED COMPLETE');
  console.log('   All tab labels use proper translation keys and the component is fully internationalized!');
} else {
  console.log('\n⚠️  BottomNavigation.js i18n implementation: 🔄 NEEDS ATTENTION');
  console.log('   Some issues need to be addressed.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test navigation with different languages');
console.log('2. Verify tab switching works correctly');
console.log('3. Test QR center button functionality');
console.log('4. Proceed to DashboardScreen.js implementation');
