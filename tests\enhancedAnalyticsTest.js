/**
 * Enhanced Analytics Implementation Test
 * Comprehensive test suite for the enhanced dashboard analytics feature
 */

import enhancedAnalyticsService from '../services/enhancedAnalyticsService';
import analyticsPerformanceService from '../services/analyticsPerformanceService';
import analyticsExportService from '../services/analyticsExportService';

// Mock user data for testing
const mockUserId = 'test-user-123';
const mockAnalyticsData = {
  period: 'month',
  dateRange: {
    start: '2024-01-01T00:00:00.000Z',
    end: '2024-01-31T23:59:59.999Z'
  },
  wallet: {
    currentBalance: 1500000,
    availableBalance: 1450000,
    pendingBalance: 50000,
    currency: 'UGX'
  },
  transactions: {
    totalTransactions: 45,
    totalSpent: 800000,
    totalReceived: 1200000,
    netFlow: 400000,
    averageTransaction: 26666.67
  },
  savings: {
    totalAccounts: 3,
    totalBalance: 2500000,
    totalDeposits: 500000,
    totalWithdrawals: 100000,
    netSavings: 400000,
    savingsRate: 33.33
  },
  investments: {
    totalPortfolios: 2,
    totalValue: 1800000,
    totalInvested: 1500000,
    totalGainLoss: 300000,
    totalReturn: 20.0
  },
  spendingCategories: [
    { category: 'Food & Dining', amount: 300000 },
    { category: 'Transportation', amount: 200000 },
    { category: 'Shopping', amount: 150000 },
    { category: 'Bills & Utilities', amount: 100000 },
    { category: 'Entertainment', amount: 50000 }
  ],
  monthlyTrends: [
    { month: 'Oct 2024', income: 1000000, expenses: 600000, net: 400000 },
    { month: 'Nov 2024', income: 1100000, expenses: 700000, net: 400000 },
    { month: 'Dec 2024', income: 1200000, expenses: 800000, net: 400000 },
    { month: 'Jan 2024', income: 1200000, expenses: 800000, net: 400000 }
  ],
  summary: {
    totalNetWorth: 5800000,
    monthlyIncome: 1200000,
    monthlyExpenses: 800000,
    monthlySavings: 400000,
    savingsRate: 33.33,
    investmentReturn: 20.0
  },
  insights: [
    {
      type: 'spending',
      title: 'Top Spending Category',
      description: 'You spent the most on Food & Dining',
      amount: 300000,
      icon: 'trending-down',
      color: '#E74C3C'
    },
    {
      type: 'savings',
      title: 'Great Savings Rate',
      description: 'You\'re saving 33.3% of your income',
      amount: 400000,
      icon: 'trending-up',
      color: '#27AE60'
    }
  ]
};

/**
 * Test Suite Class
 */
class EnhancedAnalyticsTest {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Enhanced Analytics Test Suite...\n');

    try {
      // Initialize services
      await this.initializeServices();

      // Test analytics service
      await this.testAnalyticsService();

      // Test performance service
      await this.testPerformanceService();

      // Test export service
      await this.testExportService();

      // Test real-time functionality
      await this.testRealTimeFeatures();

      // Test chart components
      await this.testChartComponents();

      // Test error handling
      await this.testErrorHandling();

      // Generate test report
      this.generateTestReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Initialize services for testing
   */
  async initializeServices() {
    this.addTest('Service Initialization');

    try {
      // Initialize performance service
      const perfResult = await analyticsPerformanceService.initialize();
      this.assert(perfResult.success, 'Performance service should initialize successfully');

      // Initialize analytics service
      const analyticsResult = await enhancedAnalyticsService.initializeRealTime(mockUserId);
      this.assert(analyticsResult.success, 'Analytics service should initialize successfully');

      this.passTest('Service Initialization');
    } catch (error) {
      this.failTest('Service Initialization', error.message);
    }
  }

  /**
   * Test analytics service functionality
   */
  async testAnalyticsService() {
    this.addTest('Analytics Service');

    try {
      // Test dashboard analytics retrieval
      const analyticsResult = await enhancedAnalyticsService.getDashboardAnalytics(mockUserId, 'month');
      this.assert(analyticsResult.success, 'Should retrieve dashboard analytics successfully');
      this.assert(analyticsResult.data, 'Should return analytics data');
      this.assert(analyticsResult.data.period === 'month', 'Should return correct period');

      // Test date range calculation
      const dateRange = enhancedAnalyticsService.getDateRange('month');
      this.assert(dateRange.start && dateRange.end, 'Should calculate date range correctly');

      // Test summary metrics calculation
      const summary = enhancedAnalyticsService.calculateSummaryMetrics(mockAnalyticsData);
      this.assert(summary.totalNetWorth > 0, 'Should calculate net worth correctly');
      this.assert(summary.savingsRate >= 0, 'Should calculate savings rate correctly');

      this.passTest('Analytics Service');
    } catch (error) {
      this.failTest('Analytics Service', error.message);
    }
  }

  /**
   * Test performance service functionality
   */
  async testPerformanceService() {
    this.addTest('Performance Service');

    try {
      // Test cache functionality
      const testKey = 'test_cache_key';
      const testData = { test: 'data', timestamp: Date.now() };
      
      analyticsPerformanceService.setCache(testKey, testData);
      const cachedData = analyticsPerformanceService.getFromCache(testKey);
      this.assert(cachedData && cachedData.test === 'data', 'Should cache and retrieve data correctly');

      // Test optimized data retrieval
      const optimizedResult = await analyticsPerformanceService.getOptimizedData(
        'test_optimized',
        async () => ({ optimized: true }),
        { useCache: true }
      );
      this.assert(optimizedResult.success, 'Should retrieve optimized data successfully');

      // Test batch queries
      const batchQueries = [
        {
          key: 'query1',
          queryFunction: async () => ({ result: 1 }),
          options: { useCache: false }
        },
        {
          key: 'query2',
          queryFunction: async () => ({ result: 2 }),
          options: { useCache: false }
        }
      ];

      const batchResult = await analyticsPerformanceService.batchQueries(batchQueries);
      this.assert(batchResult.success, 'Should execute batch queries successfully');
      this.assert(Object.keys(batchResult.results).length === 2, 'Should return all batch results');

      // Test cache statistics
      const cacheStats = analyticsPerformanceService.getCacheStats();
      this.assert(cacheStats.size >= 0, 'Should return cache statistics');

      this.passTest('Performance Service');
    } catch (error) {
      this.failTest('Performance Service', error.message);
    }
  }

  /**
   * Test export service functionality
   */
  async testExportService() {
    this.addTest('Export Service');

    try {
      // Test PDF export
      const pdfResult = await analyticsExportService.exportToPDF(mockAnalyticsData, {
        title: 'Test Analytics Report',
        period: 'month',
        userInfo: { name: 'Test User' }
      });
      this.assert(pdfResult.success, 'Should export PDF successfully');
      this.assert(pdfResult.format === 'pdf', 'Should return correct format');

      // Test CSV export
      const csvResult = await analyticsExportService.exportToExcel(mockAnalyticsData, {
        title: 'Test Analytics Report',
        period: 'month',
        format: 'csv'
      });
      this.assert(csvResult.success, 'Should export CSV successfully');
      this.assert(csvResult.format === 'csv', 'Should return correct format');

      // Test chart data export
      const chartResult = await analyticsExportService.exportChartData(
        'spending_trend',
        mockAnalyticsData.monthlyTrends,
        { format: 'pdf' }
      );
      this.assert(chartResult.success, 'Should export chart data successfully');

      // Test HTML generation
      const htmlContent = analyticsExportService.generatePDFHTML(mockAnalyticsData, {
        title: 'Test Report',
        period: 'month',
        userInfo: { name: 'Test User' }
      });
      this.assert(htmlContent.includes('Test Report'), 'Should generate HTML content correctly');

      // Test CSV generation
      const csvContent = analyticsExportService.generateCSVContent(mockAnalyticsData, {
        title: 'Test Report',
        period: 'month'
      });
      this.assert(csvContent.includes('FINANCIAL SUMMARY'), 'Should generate CSV content correctly');

      this.passTest('Export Service');
    } catch (error) {
      this.failTest('Export Service', error.message);
    }
  }

  /**
   * Test real-time functionality
   */
  async testRealTimeFeatures() {
    this.addTest('Real-time Features');

    try {
      // Test real-time subscription
      let updateReceived = false;
      const unsubscribe = enhancedAnalyticsService.subscribe((update) => {
        updateReceived = true;
      });

      // Simulate real-time update
      enhancedAnalyticsService.handleTransactionUpdate({
        eventType: 'INSERT',
        new: { id: 'test-transaction', amount: 100000 }
      });

      // Wait a bit for the update to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      this.assert(updateReceived, 'Should receive real-time updates');

      // Cleanup
      unsubscribe();

      this.passTest('Real-time Features');
    } catch (error) {
      this.failTest('Real-time Features', error.message);
    }
  }

  /**
   * Test chart components
   */
  async testChartComponents() {
    this.addTest('Chart Components');

    try {
      // Test chart data validation
      const spendingTrendData = mockAnalyticsData.monthlyTrends;
      this.assert(Array.isArray(spendingTrendData), 'Spending trend data should be an array');
      this.assert(spendingTrendData.length > 0, 'Should have spending trend data');

      const categoryData = mockAnalyticsData.spendingCategories;
      this.assert(Array.isArray(categoryData), 'Category data should be an array');
      this.assert(categoryData.length > 0, 'Should have category data');

      // Test data structure
      const firstTrend = spendingTrendData[0];
      this.assert(firstTrend.month && firstTrend.income !== undefined && firstTrend.expenses !== undefined, 
        'Trend data should have required fields');

      const firstCategory = categoryData[0];
      this.assert(firstCategory.category && firstCategory.amount !== undefined, 
        'Category data should have required fields');

      this.passTest('Chart Components');
    } catch (error) {
      this.failTest('Chart Components', error.message);
    }
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    this.addTest('Error Handling');

    try {
      // Test invalid user ID
      const invalidResult = await enhancedAnalyticsService.getDashboardAnalytics(null, 'month');
      this.assert(!invalidResult.success || invalidResult.error, 'Should handle invalid user ID gracefully');

      // Test invalid period
      const invalidPeriodResult = await enhancedAnalyticsService.getDashboardAnalytics(mockUserId, 'invalid');
      this.assert(invalidPeriodResult, 'Should handle invalid period gracefully');

      // Test export with invalid data
      const invalidExportResult = await analyticsExportService.exportToPDF(null);
      this.assert(!invalidExportResult.success, 'Should handle invalid export data gracefully');

      this.passTest('Error Handling');
    } catch (error) {
      this.failTest('Error Handling', error.message);
    }
  }

  /**
   * Test utilities
   */
  addTest(testName) {
    this.totalTests++;
    console.log(`🧪 Running test: ${testName}`);
  }

  passTest(testName) {
    this.passedTests++;
    this.testResults.push({ name: testName, status: 'PASSED' });
    console.log(`✅ ${testName} - PASSED\n`);
  }

  failTest(testName, error) {
    this.failedTests++;
    this.testResults.push({ name: testName, status: 'FAILED', error });
    console.log(`❌ ${testName} - FAILED: ${error}\n`);
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * Generate test report
   */
  generateTestReport() {
    console.log('📊 Enhanced Analytics Test Report');
    console.log('=====================================');
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.failedTests}`);
    console.log(`Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%\n`);

    console.log('Test Results:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    console.log('\n🎉 Enhanced Analytics Implementation Test Complete!');
    
    if (this.failedTests === 0) {
      console.log('🚀 All tests passed! The enhanced analytics feature is ready for production.');
    } else {
      console.log('⚠️ Some tests failed. Please review and fix the issues before deployment.');
    }
  }
}

// Export test class for use
export default EnhancedAnalyticsTest;

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new EnhancedAnalyticsTest();
  test.runAllTests();
}
