/**
 * OnboardingScreen i18n Implementation Test
 * 
 * Tests to verify that OnboardingScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing OnboardingScreen.js i18n Implementation\n');

// Read the OnboardingScreen.js file
const onboardingScreenPath = path.join(__dirname, '../screens/OnboardingScreen.js');
const onboardingScreenContent = fs.readFileSync(onboardingScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = onboardingScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = onboardingScreenContent.includes('const { t }') || onboardingScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded strings that should have been replaced
console.log('\n✅ Test 3: Hardcoded strings replacement');
const hardcodedStrings = [
  'Pay Bills Easily',
  'Multiple Payment Options',
  'AI-Powered Insights',
  'Pay your utility bills, airtime, and more with just a few taps',
  'Link bank accounts, mobile money, or use your in-app wallet',
  'Get smart financial advice and track your spending habits',
  'Skip',
  'Get Started',
  'Next'
];

let hardcodedFound = [];
hardcodedStrings.forEach(str => {
  // Check if the string appears in user-facing code (not in comments)
  const lines = onboardingScreenContent.split('\n');
  let foundInUserCode = false;
  lines.forEach(line => {
    if (line.includes(`'${str}'`) || line.includes(`"${str}"`)) {
      if (!line.trim().startsWith('//') && !line.trim().startsWith('*') && !line.includes('console.')) {
        foundInUserCode = true;
      }
    }
  });
  if (foundInUserCode) {
    hardcodedFound.push(str);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   All hardcoded strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded strings still found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for proper translation key usage
console.log('\n✅ Test 4: Translation key usage');
const translationKeys = [
  't(\'onboarding.payBillsEasily\')',
  't(\'onboarding.multiplePaymentOptions\')',
  't(\'onboarding.aiPoweredInsights\')',
  't(\'onboarding.payBillsDescription\')',
  't(\'onboarding.multiplePaymentDescription\')',
  't(\'onboarding.aiInsightsDescription\')',
  't(\'onboarding.skip\')',
  't(\'onboarding.getStarted\')',
  't(\'common.next\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (onboardingScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required translation keys exist in en.js
console.log('\n✅ Test 5: Translation keys in en.js');
const requiredKeys = [
  'payBillsEasily:',
  'multiplePaymentOptions:',
  'aiPoweredInsights:',
  'payBillsDescription:',
  'multiplePaymentDescription:',
  'aiInsightsDescription:',
  'skip:',
  'getStarted:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that slides array is defined inside component (for translation access)
console.log('\n✅ Test 6: Slides array location');
const slidesInsideComponent = onboardingScreenContent.includes('const slides = [') && 
                              onboardingScreenContent.indexOf('const slides = [') > onboardingScreenContent.indexOf('const { t }');
console.log(`   Slides array inside component: ${slidesInsideComponent ? '✅ PASS' : '❌ FAIL'}`);

// Test 7: Check for conditional button text with translations
console.log('\n✅ Test 7: Conditional button text');
const hasConditionalButtonText = onboardingScreenContent.includes('t(\'onboarding.getStarted\')') && 
                                 onboardingScreenContent.includes('t(\'common.next\')');
console.log(`   Conditional button text translated: ${hasConditionalButtonText ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 7;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (slidesInsideComponent) passedTests++;
if (hasConditionalButtonText) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 OnboardingScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  OnboardingScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test onboarding flow with different languages');
console.log('2. Verify slide content displays correctly in multiple languages');
console.log('3. Test navigation buttons in different languages');
console.log('4. Proceed to BottomNavigation.js verification');
